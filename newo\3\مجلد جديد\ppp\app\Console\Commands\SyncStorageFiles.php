<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\File;

class SyncStorageFiles extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'storage:sync';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Sync storage files to public/storage directory';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $this->info('Starting storage files synchronization...');

        $sourceDir = storage_path('uploads');
        $targetDir = public_path('storage');

        // Check if source directory exists
        if (!File::exists($sourceDir)) {
            $this->error('Source directory does not exist: ' . $sourceDir);
            return 1;
        }

        // Create target directory if it doesn't exist
        if (!File::exists($targetDir)) {
            File::makeDirectory($targetDir, 0755, true);
            $this->info('Created target directory: ' . $targetDir);
        }

        try {
            // Copy all files from source to target
            File::copyDirectory($sourceDir, $targetDir);
            $this->info('Successfully synchronized storage files!');
            $this->info('Source: ' . $sourceDir);
            $this->info('Target: ' . $targetDir);
            
            return 0;
        } catch (\Exception $e) {
            $this->error('Error during synchronization: ' . $e->getMessage());
            return 1;
        }
    }
}
