<?php

namespace App\Http\Controllers;

use App\Models\ProductService;
use App\Models\ChartOfAccount;
use App\Models\ProductServiceCategory;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;

class AccountDistributionController extends Controller
{
    /**
     * Display the account distribution page
     */
    public function index()
    {
        if (!Auth::user()->can('manage product & service')) {
            return redirect()->back()->with('error', __('Permission denied.'));
        }

        // Get all products with their relationships
        $products = ProductService::with(['category', 'taxes', 'saleAccount', 'expenseAccount'])
            ->where('created_by', Auth::user()->creatorId())
            ->get();

        // Get chart of accounts for dropdowns
        $chartAccounts = ChartOfAccount::where('created_by', Auth::user()->creatorId())
            ->where('is_enabled', 1)
            ->orderBy('name')
            ->get();

        // Get categories for display
        $categories = ProductServiceCategory::where('created_by', Auth::user()->creatorId())
            ->get();

        return view('account-distribution.index', compact('products', 'chartAccounts', 'categories'));
    }

    /**
     * Update product account information via AJAX
     */
    public function updateInline(Request $request)
    {
        if (!Auth::user()->can('manage product & service')) {
            return response()->json(['success' => false, 'message' => __('Permission denied.')]);
        }

        $request->validate([
            'id' => 'required|integer',
            'field' => 'required|string|in:sale_chartaccount_id,expense_chartaccount_id,category_id,type',
            'value' => 'required'
        ]);

        try {
            $product = ProductService::where('id', $request->id)
                ->where('created_by', Auth::user()->creatorId())
                ->first();

            if (!$product) {
                return response()->json(['success' => false, 'message' => __('Product not found.')]);
            }

            $responseData = ['success' => true, 'message' => __('Updated successfully.')];

            // Handle different field types
            if (in_array($request->field, ['sale_chartaccount_id', 'expense_chartaccount_id'])) {
                // Verify the chart account exists and belongs to the user
                $chartAccount = ChartOfAccount::where('id', $request->value)
                    ->where('created_by', Auth::user()->creatorId())
                    ->where('is_enabled', 1)
                    ->first();

                if (!$chartAccount) {
                    return response()->json(['success' => false, 'message' => __('Invalid account selected.')]);
                }

                $responseData['account_name'] = $chartAccount->name;
                $responseData['message'] = __('Account updated successfully.');
            }
            elseif ($request->field === 'category_id') {
                // Verify the category exists and belongs to the user
                $category = ProductServiceCategory::where('id', $request->value)
                    ->where('created_by', Auth::user()->creatorId())
                    ->first();

                if (!$category) {
                    return response()->json(['success' => false, 'message' => __('Invalid category selected.')]);
                }

                $responseData['category_name'] = $category->name;
                $responseData['message'] = __('Category updated successfully.');
            }
            elseif ($request->field === 'type') {
                // Validate type value
                if (!in_array($request->value, ['product', 'service'])) {
                    return response()->json(['success' => false, 'message' => __('Invalid type selected.')]);
                }

                $responseData['type_name'] = $request->value === 'product' ? __('منتج') : __('خدمة');
                $responseData['message'] = __('Type updated successfully.');
            }

            // Update the product
            $product->{$request->field} = $request->value;
            $product->save();

            return response()->json($responseData);

        } catch (\Exception $e) {
            return response()->json(['success' => false, 'message' => __('An error occurred while updating.')]);
        }
    }

    /**
     * Get chart of accounts for AJAX requests
     */
    public function getChartAccounts(Request $request)
    {
        if (!Auth::user()->can('manage product & service')) {
            return response()->json(['success' => false, 'message' => __('Permission denied.')]);
        }

        $accounts = ChartOfAccount::where('created_by', Auth::user()->creatorId())
            ->where('is_enabled', 1)
            ->orderBy('name')
            ->get(['id', 'name', 'code']);

        return response()->json(['success' => true, 'accounts' => $accounts]);
    }
}
