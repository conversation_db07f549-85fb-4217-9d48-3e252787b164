<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\Pos;
use App\Models\PosV2;
use App\Models\PosPayment;
use App\Models\PosV2Payment;
use App\Models\PosProduct;
use App\Models\PosV2Product;
use App\Models\ProductService;
use App\Models\Customer;
use App\Models\Warehouse;
use App\Models\WarehouseProduct;
use App\Models\Shift;
use App\Models\Utility;
use App\Models\FinancialRecord;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Validator;

class PosApiController extends Controller
{
    /**
     * Get all POS invoices
     */
    public function index(Request $request)
    {
        try {
            $user = Auth::user();
            $query = PosV2::where('created_by', $user->creatorId())
                          ->with(['customer', 'warehouse', 'posPayment']);

            // Filter by date range
            if ($request->has('start_date') && $request->has('end_date')) {
                $query->whereBetween('pos_date', [$request->start_date, $request->end_date]);
            }

            // Filter by warehouse
            if ($request->has('warehouse_id')) {
                $query->where('warehouse_id', $request->warehouse_id);
            }

            // Filter by customer
            if ($request->has('customer_id')) {
                $query->where('customer_id', $request->customer_id);
            }

            // Filter by shift
            if ($request->has('shift_id')) {
                $query->where('shift_id', $request->shift_id);
            }

            $invoices = $query->orderBy('created_at', 'desc')->paginate(50);

            return response()->json([
                'success' => true,
                'data' => $invoices,
                'message' => 'POS invoices retrieved successfully'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Error retrieving POS invoices: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Store a new POS invoice
     */
    public function store(Request $request)
    {
        try {
            $validator = Validator::make($request->all(), [
                'customer_id' => 'required|integer',
                'warehouse_id' => 'required|integer',
                'products' => 'required|array',
                'products.*.product_id' => 'required|integer',
                'products.*.quantity' => 'required|numeric|min:1',
                'products.*.price' => 'required|numeric|min:0',
                'payment_type' => 'required|string',
                'total_amount' => 'required|numeric|min:0'
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Validation failed',
                    'errors' => $validator->errors()
                ], 422);
            }

            DB::beginTransaction();

            $user = Auth::user();
            $settings = Utility::settings();

            // Create POS V2 record
            $pos = new PosV2();
            $pos->pos_id = $this->invoicePosV2Number();
            $pos->customer_id = $request->customer_id;
            $pos->warehouse_id = $request->warehouse_id;
            $pos->pos_date = date('Y-m-d');
            $pos->created_by = $user->creatorId();
            $pos->user_id = $user->id;
            $pos->status = 1;
            $pos->status_type = 'normal';
            
            // Set shift if provided
            if ($request->has('shift_id')) {
                $pos->shift_id = $request->shift_id;
            }
            
            $pos->save();

            $mainsubtotal = 0;
            $totalTax = 0;
            $totalDiscount = 0;

            // Add products to POS
            foreach ($request->products as $productData) {
                $product = ProductService::find($productData['product_id']);
                
                if (!$product) {
                    DB::rollBack();
                    return response()->json([
                        'success' => false,
                        'message' => 'Product not found: ' . $productData['product_id']
                    ], 404);
                }

                $posProduct = new PosV2Product();
                $posProduct->product_id = $productData['product_id'];
                $posProduct->pos_id = $pos->id;
                $posProduct->quantity = $productData['quantity'];
                $posProduct->price = $productData['price'];
                
                // Calculate tax
                $tax = 0;
                if (!empty($product->tax_id)) {
                    $taxes = \Utility::tax($product->tax_id);
                    foreach ($taxes as $taxData) {
                        $tax += ($productData['price'] * $productData['quantity'] * $taxData->rate) / 100;
                    }
                }
                
                $posProduct->tax = $tax;
                $posProduct->discount = $productData['discount'] ?? 0;
                $posProduct->total = ($productData['price'] * $productData['quantity']) + $tax - ($productData['discount'] ?? 0);
                $posProduct->description = $productData['description'] ?? '';
                $posProduct->save();

                $mainsubtotal += $posProduct->total;
                $totalTax += $tax;
                $totalDiscount += $productData['discount'] ?? 0;

                // Update warehouse stock
                $warehouseProduct = WarehouseProduct::where('warehouse_id', $request->warehouse_id)
                                                  ->where('product_id', $productData['product_id'])
                                                  ->first();
                
                if ($warehouseProduct) {
                    $warehouseProduct->quantity -= $productData['quantity'];
                    $warehouseProduct->save();
                }
            }

            // Create payment record
            $payment = new PosV2Payment();
            $payment->pos_id = $pos->id;
            $payment->date = date('Y-m-d');
            $payment->amount = $request->total_amount;
            $payment->discount = $totalDiscount;
            $payment->discount_amount = $totalDiscount;
            $payment->created_by = $user->creatorId();
            $payment->payment_type = $request->payment_type;
            
            if ($request->payment_type == 'cash') {
                $payment->cash_amount = $request->total_amount;
                $payment->network_amount = 0;
            } else {
                $payment->cash_amount = 0;
                $payment->network_amount = $request->total_amount;
                $payment->transaction_number = $request->transaction_number ?? '';
            }
            
            $payment->save();

            // Update POS payment status
            $pos->is_payment_set = true;
            $pos->save();

            DB::commit();

            return response()->json([
                'success' => true,
                'data' => [
                    'pos_id' => $pos->id,
                    'invoice_number' => $pos->pos_id,
                    'total_amount' => $request->total_amount,
                    'payment_id' => $payment->id
                ],
                'message' => 'POS invoice created successfully'
            ]);

        } catch (\Exception $e) {
            DB::rollBack();
            return response()->json([
                'success' => false,
                'message' => 'Error creating POS invoice: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Show specific POS invoice
     */
    public function show($id)
    {
        try {
            $user = Auth::user();
            $pos = PosV2::where('id', $id)
                        ->where('created_by', $user->creatorId())
                        ->with(['customer', 'warehouse', 'posPayment', 'items.product'])
                        ->first();

            if (!$pos) {
                return response()->json([
                    'success' => false,
                    'message' => 'POS invoice not found'
                ], 404);
            }

            return response()->json([
                'success' => true,
                'data' => $pos,
                'message' => 'POS invoice retrieved successfully'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Error retrieving POS invoice: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Generate POS V2 invoice number
     */
    private function invoicePosV2Number()
    {
        $user = Auth::user();
        $latest = PosV2::where('created_by', $user->creatorId())->latest()->first();
        
        if (!$latest) {
            return 1;
        }
        
        return $latest->pos_id + 1;
    }

    /**
     * Add product to cart (session-based for desktop app)
     */
    public function addToCart(Request $request)
    {
        try {
            $validator = Validator::make($request->all(), [
                'product_id' => 'required|integer',
                'quantity' => 'required|numeric|min:1',
                'warehouse_id' => 'required|integer'
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Validation failed',
                    'errors' => $validator->errors()
                ], 422);
            }

            $product = ProductService::find($request->product_id);
            
            if (!$product) {
                return response()->json([
                    'success' => false,
                    'message' => 'Product not found'
                ], 404);
            }

            // تم إزالة فحص المخزون للسماح بالبيع على الكميات الصفرية والسالبة
            $warehouseProduct = WarehouseProduct::where('warehouse_id', $request->warehouse_id)
                                              ->where('product_id', $request->product_id)
                                              ->first();

            if (!$warehouseProduct || $warehouseProduct->quantity < $request->quantity) {
                return response()->json([
                    'success' => false,
                    'message' => 'Insufficient stock in warehouse'
                ], 400);
            }

            // For desktop app, we'll return the cart data instead of using sessions
            $cartItem = [
                'product_id' => $product->id,
                'name' => $product->name,
                'sku' => $product->sku,
                'price' => $product->sale_price,
                'quantity' => $request->quantity,
                'tax_rate' => 0,
                'discount' => 0,
                'subtotal' => $product->sale_price * $request->quantity,
                'image' => $product->pro_image ?? null
            ];

            // Calculate tax if applicable
            if (!empty($product->tax_id)) {
                $taxes = \Utility::tax($product->tax_id);
                $taxRate = 0;
                foreach ($taxes as $tax) {
                    $taxRate += $tax->rate;
                }
                $cartItem['tax_rate'] = $taxRate;
                $cartItem['tax_amount'] = ($cartItem['subtotal'] * $taxRate) / 100;
                $cartItem['total'] = $cartItem['subtotal'] + $cartItem['tax_amount'];
            } else {
                $cartItem['tax_amount'] = 0;
                $cartItem['total'] = $cartItem['subtotal'];
            }

            return response()->json([
                'success' => true,
                'data' => $cartItem,
                'message' => 'Product added to cart successfully'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Error adding product to cart: ' . $e->getMessage()
            ], 500);
        }
    }
}
