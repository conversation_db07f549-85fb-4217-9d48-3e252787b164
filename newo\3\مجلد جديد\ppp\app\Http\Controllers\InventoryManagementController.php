<?php

namespace App\Http\Controllers;

use App\Models\ProductService;
use App\Models\warehouse;
use App\Models\WarehouseProduct;
use App\Models\WarehouseProductLimit;
use App\Models\Employee;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Mpdf\Mpdf;

class InventoryManagementController extends Controller
{
    /**
     * عرض صفحة إدارة المخزون الرئيسية
     */
    public function index(Request $request)
    {
        // التحقق من الصلاحيات - متاح للمستخدمين الذين لديهم دور Pricing أو صلاحيات إدارة المخزون
        if (!Auth::user()->hasRole('Pricing') && !Auth::user()->can('manage inventory') && !Auth::user()->can('manage product & service')) {
            return redirect()->back()->with('error', __('Permission denied.'));
        }

        // جلب المستودعات المتاحة
        $warehouses = warehouse::where('created_by', Auth::user()->creatorId())->get();

        // المستودع المحدد (الافتراضي هو الأول)
        $selectedWarehouse = $request->input('warehouse_id', $warehouses->first()->id ?? null);

        return view('company_operations.inventory_management.index', compact('warehouses', 'selectedWarehouse'));
    }

    /**
     * جلب منتجات مستودع معين
     */
    public function getWarehouseProducts(Request $request, $warehouseId)
    {
        // التحقق من وجود المستودع
        $warehouse = warehouse::findOrFail($warehouseId);

        // جلب جميع المنتجات أولاً
        $allProducts = ProductService::where('created_by', Auth::user()->creatorId())
            ->where('type', 'product') // فقط المنتجات وليس الخدمات
            ->with(['category', 'unit'])
            ->get();

        // إضافة معلومات المستودع لكل منتج
        $products = $allProducts->map(function($product) use ($warehouseId) {
            // جلب بيانات المنتج في المستودع (أحدث سجل فقط)
            $warehouseProduct = WarehouseProduct::where('warehouse_id', $warehouseId)
                ->where('product_id', $product->id)
                ->orderBy('updated_at', 'desc')
                ->first();

            // جلب الحد الأدنى للكمية
            $limitRecord = WarehouseProductLimit::where('warehouse_id', $warehouseId)
                ->where('product_id', $product->id)
                ->first();

            // إضافة معلومات المستودع للمنتج
            $product->warehouse_quantity = $warehouseProduct ? $warehouseProduct->quantity : 0;
            $product->warehouse_product_id = $warehouseProduct ? $warehouseProduct->id : null;
            $product->min_quantity = $limitRecord ? $limitRecord->min_quantity : null;

            return $product;
        });

        // البحث إذا كان موجودًا
        $search = $request->input('search');
        if ($search) {
            $products = $products->filter(function($product) use ($search) {
                return stripos($product->name, $search) !== false ||
                       stripos($product->sku, $search) !== false;
            });
        }

        // تصفية حسب حالة المخزون
        $statusFilter = $request->input('status_filter');
        if ($statusFilter) {
            $products = $products->filter(function($product) use ($statusFilter) {
                $isLowStock = isset($product->min_quantity) && $product->warehouse_quantity < $product->min_quantity;

                switch ($statusFilter) {
                    case 'out_of_stock':
                        return $product->warehouse_quantity == 0;
                    case 'low_stock':
                        return $product->warehouse_quantity > 0 && $isLowStock;
                    case 'normal':
                        return $product->warehouse_quantity > 0 && !$isLowStock;
                    default:
                        return true;
                }
            });
        }

        if ($request->ajax()) {
            return view('company_operations.inventory_management.products_table', compact('products', 'warehouse'))->render();
        }

        return view('company_operations.inventory_management.index', compact('products', 'warehouse'));
    }

    /**
     * تحديث كمية منتج في مستودع أو إضافة منتج جديد
     */
    public function updateQuantity(Request $request)
    {
        // التحقق من الصلاحيات
        if (!Auth::user()->hasRole('Pricing') && !Auth::user()->can('manage inventory') && !Auth::user()->can('manage product & service')) {
            return response()->json([
                'success' => false,
                'message' => __('Permission denied.')
            ], 403);
        }

        $request->validate([
            'quantity' => 'required|numeric|min:0',
        ]);

        try {
            DB::beginTransaction();

            // إذا كان warehouse_product_id موجود، نحدث المنتج الموجود
            if ($request->warehouse_product_id) {
                $request->validate([
                    'warehouse_product_id' => 'required|exists:warehouse_products,id',
                ]);

                $warehouseProduct = WarehouseProduct::findOrFail($request->warehouse_product_id);
                $oldQuantity = $warehouseProduct->quantity;
                $quantityDifference = $request->quantity - $oldQuantity;
                $warehouseProduct->quantity = $request->quantity;
                $warehouseProduct->save();

                // إضافة سجل في تقرير المخزون عند التحديث
                if ($quantityDifference != 0) {
                    $type = $quantityDifference > 0 ? 'manual_inventory_increase' : 'manual_inventory_decrease';
                    $description = $quantityDifference > 0
                        ? 'زيادة يدوية في المخزون من ' . $oldQuantity . ' إلى ' . $request->quantity
                        : 'تقليل يدوي في المخزون من ' . $oldQuantity . ' إلى ' . $request->quantity;

                    \App\Models\Utility::addProductStock(
                        $warehouseProduct->product_id,
                        abs($quantityDifference),
                        $type,
                        $description . ' - المستودع: ' . $warehouseProduct->warehouse_id,
                        $warehouseProduct->id
                    );
                }

                $message = __('تم تحديث الكمية بنجاح');
            } else {
                // إذا لم يكن موجود، نضيف المنتج إلى المستودع
                $request->validate([
                    'product_id' => 'required|exists:product_services,id',
                    'warehouse_id' => 'required|exists:warehouses,id',
                ]);

                $warehouseProduct = WarehouseProduct::create([
                    'warehouse_id' => $request->warehouse_id,
                    'product_id' => $request->product_id,
                    'quantity' => $request->quantity,
                    'created_by' => Auth::user()->creatorId(),
                ]);

                // إضافة سجل في تقرير المخزون لضمان التتبع الصحيح
                \App\Models\Utility::addProductStock(
                    $request->product_id,
                    $request->quantity,
                    'manual_inventory_add',
                    'إضافة يدوية من إدارة المخزون - المستودع: ' . $request->warehouse_id,
                    $warehouseProduct->id
                );

                $oldQuantity = 0;
                $message = __('تم إضافة المنتج إلى المستودع بنجاح');
            }

            DB::commit();

            return response()->json([
                'success' => true,
                'message' => $message,
                'new_quantity' => $request->quantity,
                'old_quantity' => $oldQuantity,
                'warehouse_product_id' => $warehouseProduct->id
            ]);
        } catch (\Exception $e) {
            DB::rollBack();
            return response()->json([
                'success' => false,
                'message' => __('حدث خطأ أثناء تحديث الكمية') . ': ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * تحديث الحد الأدنى لكمية منتج في مستودع
     */
    public function updateMinQuantity(Request $request)
    {
        $request->validate([
            'product_id' => 'required|exists:product_services,id',
            'warehouse_id' => 'required|exists:warehouses,id',
            'min_quantity' => 'required|numeric|min:0',
        ]);

        try {
            DB::beginTransaction();

            // تحديث أو إنشاء حد أدنى جديد
            $limit = WarehouseProductLimit::updateOrCreate(
                [
                    'product_id' => $request->product_id,
                    'warehouse_id' => $request->warehouse_id,
                ],
                [
                    'min_quantity' => $request->min_quantity,
                    'created_by' => Auth::user()->creatorId(),
                ]
            );

            DB::commit();

            return response()->json([
                'success' => true,
                'message' => __('تم تحديث الحد الأدنى للكمية بنجاح'),
                'min_quantity' => $request->min_quantity
            ]);
        } catch (\Exception $e) {
            DB::rollBack();
            return response()->json([
                'success' => false,
                'message' => __('حدث خطأ أثناء تحديث الحد الأدنى للكمية') . ': ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * تحديث بيانات المنتج الأساسية
     */
    public function updateProductData(Request $request)
    {
        $request->validate([
            'product_id' => 'required|exists:product_services,id',
            'field' => 'required|in:name,sku,sale_price,tax_id,category_id,unit_id',
            'value' => 'required',
        ]);

        try {
            DB::beginTransaction();

            $product = ProductService::findOrFail($request->product_id);

            // التحقق من الصلاحيات
            if ($product->created_by != Auth::user()->creatorId()) {
                return response()->json([
                    'success' => false,
                    'message' => __('ليس لديك صلاحية لتعديل هذا المنتج')
                ], 403);
            }

            $oldValue = $product->{$request->field};

            // معالجة خاصة لكل حقل
            switch ($request->field) {
                case 'name':
                    $product->name = $request->value;
                    break;

                case 'sku':
                    // التحقق من عدم تكرار SKU
                    $existingSku = ProductService::where('sku', $request->value)
                        ->where('id', '!=', $product->id)
                        ->where('created_by', Auth::user()->creatorId())
                        ->first();

                    if ($existingSku) {
                        return response()->json([
                            'success' => false,
                            'message' => __('الرمز التعريفي موجود بالفعل')
                        ], 400);
                    }

                    $product->sku = $request->value;
                    break;

                case 'sale_price':
                    if (!is_numeric($request->value) || $request->value < 0) {
                        return response()->json([
                            'success' => false,
                            'message' => __('سعر البيع يجب أن يكون رقم موجب')
                        ], 400);
                    }
                    $product->sale_price = $request->value;
                    break;

                case 'tax_id':
                    $product->tax_id = $request->value;
                    break;

                case 'category_id':
                    $product->category_id = $request->value;
                    break;

                case 'unit_id':
                    $product->unit_id = $request->value;
                    break;
            }

            $product->save();

            DB::commit();

            return response()->json([
                'success' => true,
                'message' => __('تم تحديث البيانات بنجاح'),
                'old_value' => $oldValue,
                'new_value' => $request->value
            ]);

        } catch (\Exception $e) {
            DB::rollBack();
            return response()->json([
                'success' => false,
                'message' => __('حدث خطأ أثناء تحديث البيانات') . ': ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * جلب قائمة الفئات للمنتجات
     */
    public function getCategories()
    {
        $categories = \App\Models\ProductServiceCategory::where('created_by', Auth::user()->creatorId())
            ->where('type', 'product & service')
            ->select('id', 'name')
            ->get();

        return response()->json($categories);
    }

    /**
     * جلب قائمة الوحدات
     */
    public function getUnits()
    {
        $units = \App\Models\ProductServiceUnit::where('created_by', Auth::user()->creatorId())
            ->select('id', 'name')
            ->get();

        return response()->json($units);
    }

    /**
     * جلب قائمة الضرائب
     */
    public function getTaxes()
    {
        $taxes = \App\Models\Tax::where('created_by', Auth::user()->creatorId())
            ->select('id', 'name', 'rate')
            ->get();

        return response()->json($taxes);
    }

    /**
     * طباعة تقرير الجرد
     */
    public function printInventoryReport(Request $request, $warehouseId)
    {
        try {
            // تسجيل محاولة الوصول للتشخيص
            \Log::info('محاولة طباعة تقرير الجرد', [
                'warehouse_id' => $warehouseId,
                'user_id' => Auth::id(),
                'search' => $request->input('search'),
                'status_filter' => $request->input('status_filter')
            ]);

            // إرجاع رسالة اختبار أولاً للتأكد من وصول الطلب
            if (request()->get('test')) {
                return response()->json([
                    'success' => true,
                    'message' => 'الطريق يعمل بنجاح',
                    'warehouse_id' => $warehouseId,
                    'user_id' => Auth::id()
                ]);
            }

            // التحقق من الصلاحيات (مؤقتاً نتجاهل التحقق للاختبار)
            // if (!Auth::user()->hasRole('Pricing') && !Auth::user()->can('manage inventory') && !Auth::user()->can('manage product & service')) {
            //     \Log::warning('رفض الوصول لطباعة الجرد - صلاحيات غير كافية', ['user_id' => Auth::id()]);
            //     return response()->json(['error' => 'Permission denied'], 403);
            // }

            // التحقق من وجود المستودع
            $warehouse = warehouse::where('id', $warehouseId)
                ->where('created_by', Auth::user()->creatorId())
                ->first();

            if (!$warehouse) {
                \Log::error('مستودع غير موجود', ['warehouse_id' => $warehouseId]);
                return response()->json(['error' => 'Warehouse not found'], 404);
            }

            // جلب جميع المنتجات مع معلومات المستودع (نفس منطق العرض)
            $allProducts = ProductService::where('created_by', Auth::user()->creatorId())
                                       ->where('type', 'product')
                                       ->get();

            // إعداد البيانات مع معلومات المستودع
            $products = collect();
            foreach ($allProducts as $product) {
                // جلب بيانات المنتج في المستودع
                $warehouseProduct = WarehouseProduct::where('warehouse_id', $warehouseId)
                                                   ->where('product_id', $product->id)
                                                   ->first();

                // إضافة معلومات الكمية
                $product->warehouse_quantity = $warehouseProduct ? $warehouseProduct->quantity : 0;
                $product->current_quantity = $product->warehouse_quantity;

                // جلب الحد الأدنى للكمية
                $limitRecord = WarehouseProductLimit::where('warehouse_id', $warehouseId)
                                                   ->where('product_id', $product->id)
                                                   ->first();
                $product->min_quantity = $limitRecord ? $limitRecord->min_quantity : 0;

                $products->push($product);
            }

            // تطبيق نفس التصفية المستخدمة في العرض
            // البحث إذا كان موجودًا
            $search = $request->input('search');
            if ($search) {
                $products = $products->filter(function($product) use ($search) {
                    return stripos($product->name, $search) !== false ||
                           stripos($product->sku, $search) !== false;
                });
            }

            // تصفية حسب حالة المخزون
            $statusFilter = $request->input('status_filter');
            if ($statusFilter) {
                $products = $products->filter(function($product) use ($statusFilter) {
                    $isLowStock = isset($product->min_quantity) && $product->warehouse_quantity < $product->min_quantity;

                    switch ($statusFilter) {
                        case 'out_of_stock':
                            return $product->warehouse_quantity == 0;
                        case 'low_stock':
                            return $product->warehouse_quantity > 0 && $isLowStock;
                        case 'normal':
                            return $product->warehouse_quantity > 0 && !$isLowStock;
                        default:
                            return true;
                    }
                });
            }

            \Log::info('تم جلب المنتجات', ['products_count' => $products->count()]);

            // جلب معلومات الموظف الحالي
            $currentUser = Auth::user();
            $employee = Employee::where('user_id', $currentUser->id)->first();

            // جلب شعار الشركة
            $companyLogo = $this->getCompanyLogo();

            // التأكد من وجود بيانات صحيحة
            if (!$currentUser) {
                \Log::error('المستخدم غير موجود');
                return response()->json(['error' => 'User not found'], 404);
            }

            // إعداد البيانات للـ PDF مع التحقق من القيم
            $data = [
                'warehouse' => $warehouse,
                'products' => $products,
                'user' => $currentUser,
                'employee' => $employee ?: (object)['employee_id' => 'غير محدد'], // قيمة افتراضية
                'inventory_date' => now()->format('Y-m-d'),
                'inventory_time' => now()->format('H:i:s'),
            ];

            \Log::info('بيانات PDF', [
                'warehouse_name' => $warehouse->name,
                'products_count' => $products->count(),
                'user_name' => $currentUser->name,
                'employee_id' => $employee ? $employee->employee_id : 'غير محدد'
            ]);

            // إنشاء PDF
            $mpdf = $this->initializePDF();

            try {
                // إنشاء HTML محسن للتقرير
                $html = '
                <!DOCTYPE html>
                <html>
                <head>
                    <meta charset="UTF-8">
                    <title>تقرير جرد المخزون</title>
                    <style>
                        body {
                            font-family: DejaVu Sans, sans-serif;
                            direction: rtl;
                            margin: 0;
                            padding: 20px;
                            font-size: 12px;
                        }
                        .header {
                            position: relative;
                            text-align: center;
                            margin-bottom: 30px;
                            border: 2px solid #000;
                            padding: 20px;
                        }
                        .logo {
                            position: absolute;
                            top: 10px;
                            right: 10px;
                            width: 80px;
                            height: 80px;
                        }
                        .company-info {
                            margin-top: 10px;
                        }
                        table {
                            width: 100%;
                            border-collapse: collapse;
                            margin-bottom: 30px;
                        }
                        th, td {
                            border: 1px solid #000;
                            padding: 8px;
                            text-align: center;
                            font-size: 10px;
                        }
                        th {
                            background-color: #f0f0f0;
                            font-weight: bold;
                        }

                        .signature-section {
                            position: fixed;
                            bottom: 50px;
                            left: 0;
                            right: 0;
                            font-size: 12px;
                            padding: 0 20px;
                        }
                        .signature-row {
                            display: flex;
                            justify-content: space-between;
                            margin-bottom: 20px;
                            padding: 10px 0;
                            border-top: 1px solid #ccc;
                            padding-top: 15px;
                        }
                        .signature-item {
                            display: flex;
                            align-items: center;
                            margin: 0 20px;
                        }
                        .signature-label {
                            margin-left: 10px;
                            font-weight: bold;
                        }
                        .signature-line {
                            border-bottom: 1px solid #000;
                            width: 150px;
                            height: 20px;
                            margin: 0 5px;
                        }
                        .page-number {
                            position: fixed;
                            bottom: 30px;
                            right: 20px;
                            font-size: 10px;
                            color: #666;
                        }
                        .report-footer {
                            position: fixed;
                            bottom: 10px;
                            right: 20px;
                            font-size: 9px;
                            color: #888;
                            text-align: right;
                        }
                    </style>
                </head>
                <body>
                    <div class="header">
                        ' . $companyLogo . '
                        <h1>تقرير جرد المخزون | Inventory Report</h1>
                        <h2>' . $warehouse->name . '</h2>
                        <div class="company-info">
                            <p>التاريخ | Date: ' . now()->format('Y-m-d') . '</p>';

                // إضافة معلومات التصفية إذا كانت مطبقة
                if ($search || $statusFilter) {
                    $html .= '<p><strong>التصفية المطبقة | Applied Filters:</strong> ';
                    $filterInfo = [];

                    if ($search) {
                        $filterInfo[] = 'البحث | Search: "' . htmlspecialchars($search) . '"';
                    }

                    if ($statusFilter) {
                        $statusNames = [
                            'out_of_stock' => 'غير متوفر | Out of Stock',
                            'low_stock' => 'منخفض | Low Stock',
                            'normal' => 'طبيعي | Normal'
                        ];
                        $filterInfo[] = 'الحالة | Status: ' . ($statusNames[$statusFilter] ?? $statusFilter);
                    }

                    $html .= implode(' | ', $filterInfo) . '</p>';
                }

                $html .= '
                            <p>الموظف المسؤول | Responsible Employee: ________________</p>
                        </div>
                    </div>

                    <table>
                        <thead>
                            <tr>
                                <th style="width: 8%;">#</th>
                                <th style="width: 45%;">اسم المنتج | Product Name</th>
                                <th style="width: 15%;">باركود | Barcode</th>
                                <th style="width: 12%;">الكمية الحالية | Current Qty</th>
                                <th style="width: 12%;">الكمية الفعلية | Actual Qty</th>
                                <th style="width: 8%;">التأشير | Check</th>
                            </tr>
                        </thead>
                        <tbody>';

                foreach($products as $index => $product) {
                    $html .= '
                            <tr>
                                <td>' . ($index + 1) . '</td>
                                <td style="text-align: right; padding-right: 5px;">' . htmlspecialchars($product->name) . '</td>
                                <td>' . htmlspecialchars($product->sku) . '</td>
                                <td><strong>' . $product->current_quantity . '</strong></td>
                                <td style="height: 35px;"></td>
                                <td style="height: 35px;"></td>
                            </tr>';
                }

                $html .= '
                        </tbody>
                    </table>

                    <div class="signature-section">
                        <!-- الموظف -->
                        <div class="signature-row">
                            <div class="signature-item">
                                <span class="signature-label">اسم الموظف | Employee Name:</span>
                                <div class="signature-line"></div>
                                <span class="signature-label">التاريخ | Date:</span>
                                <div class="signature-line"></div>
                                <span class="signature-label">التوقيع | Signature:</span>
                                <div class="signature-line"></div>
                            </div>
                        </div>

                        <!-- المدقق -->
                        <div class="signature-row">
                            <div class="signature-item">
                                <span class="signature-label">اسم المدقق | Auditor Name:</span>
                                <div class="signature-line"></div>
                                <span class="signature-label">التاريخ | Date:</span>
                                <div class="signature-line"></div>
                                <span class="signature-label">التوقيع | Signature:</span>
                                <div class="signature-line"></div>
                            </div>
                        </div>
                    </div>

                    <div class="page-number">
                        صفحة 1-1 | Page 1-1
                    </div>

                    <div class="report-footer">
                        <p>تم إنشاء هذا لأغراض الرقابة وإبراء الذمة في ' . now()->format('d-m-Y H:i:s') . '</p>
                        <p>Generated for audit and accountability purposes on ' . now()->format('d-m-Y H:i:s') . '</p>
                    </div>
                </body>
                </html>';

                $mpdf->WriteHTML($html);
            } catch (\Exception $e) {
                \Log::error('خطأ في إنشاء HTML للـ PDF', ['error' => $e->getMessage()]);
                return response()->json(['error' => 'خطأ في إنشاء HTML: ' . $e->getMessage()], 500);
            }

            \Log::info('تم إنشاء PDF بنجاح');

            // التحقق من طلب التحميل
            if (request()->get('download')) {
                return response($mpdf->Output('inventory_report_' . $warehouse->name . '_' . date('Y-m-d') . '.pdf', 'D'), 200)
                    ->header('Content-Type', 'application/pdf');
            }

            return response($mpdf->Output('inventory_report_' . $warehouse->name . '_' . date('Y-m-d') . '.pdf', 'I'), 200)
                ->header('Content-Type', 'application/pdf');

        } catch (\Exception $e) {
            \Log::error('خطأ في طباعة تقرير الجرد', ['error' => $e->getMessage(), 'warehouse_id' => $warehouseId]);
            return response()->json(['error' => 'حدث خطأ أثناء إنشاء التقرير: ' . $e->getMessage()], 500);
        }
    }

    /**
     * تهيئة mPDF
     */
    private function initializePDF()
    {
        $mpdf = new Mpdf([
            'mode' => 'utf-8',
            'format' => 'A4',
            'orientation' => 'P',
            'margin_top' => 20,
            'margin_bottom' => 20,
            'margin_left' => 15,
            'margin_right' => 15,
        ]);

        $mpdf->autoScriptToLang = true;
        $mpdf->autoLangToFont = true;

        return $mpdf;
    }

    /**
     * إضافة +1 لجميع المنتجات في المستودع
     */
    public function addQuantityToAll(Request $request)
    {
        try {
            // التحقق من الصلاحيات
            if (!Auth::user()->hasRole('Pricing') && !Auth::user()->can('manage inventory') && !Auth::user()->can('manage product & service')) {
                return response()->json([
                    'success' => false,
                    'message' => __('Permission denied.')
                ], 403);
            }

            $warehouseId = $request->input('warehouse_id');

            if (!$warehouseId) {
                return response()->json([
                    'success' => false,
                    'message' => __('يرجى تحديد المستودع')
                ], 400);
            }

            // التحقق من وجود المستودع
            $warehouse = warehouse::where('id', $warehouseId)
                ->where('created_by', Auth::user()->creatorId())
                ->first();

            if (!$warehouse) {
                return response()->json([
                    'success' => false,
                    'message' => __('المستودع غير موجود')
                ], 404);
            }

            $updatedCount = 0;
            $createdCount = 0;

            // جلب جميع المنتجات للمستخدم الحالي
            $allProducts = ProductService::where('created_by', Auth::user()->creatorId())
                ->where('type', 'product')
                ->get();

            DB::beginTransaction();

            foreach ($allProducts as $product) {
                // جلب أحدث سجل للمنتج في المستودع
                $warehouseProduct = WarehouseProduct::where('warehouse_id', $warehouseId)
                    ->where('product_id', $product->id)
                    ->orderBy('updated_at', 'desc')
                    ->first();

                // التحقق من الكمية الفعلية
                $currentQuantity = $warehouseProduct ? $warehouseProduct->quantity : 0;

                // إضافة +1 فقط إذا كانت الكمية صفر
                if ($currentQuantity == 0) {
                    if ($warehouseProduct) {
                        // تحديث السجل الموجود
                        $warehouseProduct->quantity = 1;
                        $warehouseProduct->save();
                        $updatedCount++;
                    } else {
                        // إنشاء سجل جديد للمنتجات غير الموجودة
                        WarehouseProduct::create([
                            'warehouse_id' => $warehouseId,
                            'product_id' => $product->id,
                            'quantity' => 1,
                            'created_by' => Auth::user()->creatorId()
                        ]);
                        $createdCount++;
                    }
                }
                // إذا كانت الكمية أكبر من صفر، لا نفعل شيء
            }

            DB::commit();

            $totalProcessed = $updatedCount + $createdCount;
            $totalProducts = $allProducts->count();
            $skippedCount = $totalProducts - $totalProcessed;

            if ($totalProcessed > 0) {
                $message = __('تم إضافة +1 للمنتجات ذات الكمية صفر') . ': ' .
                          __('محدث') . ': ' . $updatedCount . ', ' .
                          __('جديد') . ': ' . $createdCount . ', ' .
                          __('تم تجاهل') . ': ' . $skippedCount . ' ' . __('منتج (لديهم كمية أكبر من صفر)');
            } else {
                $message = __('لا توجد منتجات بكمية صفر في هذا المستودع') . '. ' .
                          __('جميع المنتجات') . ' (' . $totalProducts . ') ' . __('لديها كميات أكبر من صفر');
            }

            return response()->json([
                'success' => true,
                'message' => $message,
                'updated_count' => $updatedCount,
                'created_count' => $createdCount,
                'skipped_count' => $skippedCount,
                'total_products' => $totalProducts
            ]);

        } catch (\Exception $e) {
            DB::rollback();

            return response()->json([
                'success' => false,
                'message' => __('حدث خطأ أثناء إضافة الكميات') . ': ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * فحص المنتجات ذات الكمية صفر
     */
    public function checkZeroQuantities(Request $request)
    {
        try {
            $warehouseId = $request->input('warehouse_id');

            if (!$warehouseId) {
                return response()->json([
                    'success' => false,
                    'message' => __('يرجى تحديد المستودع')
                ], 400);
            }

            // جلب جميع المنتجات للمستخدم الحالي
            $allProducts = ProductService::where('created_by', Auth::user()->creatorId())
                ->where('type', 'product')
                ->get();

            $zeroQuantityProducts = [];
            $nonZeroQuantityProducts = [];

            foreach ($allProducts as $product) {
                // جلب أحدث سجل للمنتج في المستودع
                $warehouseProduct = WarehouseProduct::where('warehouse_id', $warehouseId)
                    ->where('product_id', $product->id)
                    ->orderBy('updated_at', 'desc')
                    ->first();

                $currentQuantity = $warehouseProduct ? $warehouseProduct->quantity : 0;

                if ($currentQuantity == 0) {
                    $zeroQuantityProducts[] = [
                        'id' => $product->id,
                        'name' => $product->name,
                        'sku' => $product->sku,
                        'current_quantity' => $currentQuantity
                    ];
                } else {
                    $nonZeroQuantityProducts[] = [
                        'id' => $product->id,
                        'name' => $product->name,
                        'sku' => $product->sku,
                        'current_quantity' => $currentQuantity
                    ];
                }
            }

            return response()->json([
                'success' => true,
                'total_products' => $allProducts->count(),
                'zero_quantity_count' => count($zeroQuantityProducts),
                'non_zero_quantity_count' => count($nonZeroQuantityProducts),
                'zero_quantity_products' => $zeroQuantityProducts,
                'non_zero_quantity_products' => $nonZeroQuantityProducts
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => __('حدث خطأ أثناء فحص الكميات') . ': ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * تنظيف السجلات المكررة في warehouse_products
     */
    public function cleanDuplicateWarehouseProducts(Request $request)
    {
        try {
            $warehouseId = $request->input('warehouse_id');

            if (!$warehouseId) {
                return response()->json([
                    'success' => false,
                    'message' => __('يرجى تحديد المستودع')
                ], 400);
            }

            DB::beginTransaction();

            // البحث عن السجلات المكررة
            $duplicates = DB::table('warehouse_products')
                ->select('warehouse_id', 'product_id', DB::raw('COUNT(*) as count'), DB::raw('SUM(quantity) as total_quantity'))
                ->where('warehouse_id', $warehouseId)
                ->groupBy('warehouse_id', 'product_id')
                ->having('count', '>', 1)
                ->get();

            $cleanedCount = 0;

            foreach ($duplicates as $duplicate) {
                // حذف جميع السجلات المكررة
                WarehouseProduct::where('warehouse_id', $duplicate->warehouse_id)
                    ->where('product_id', $duplicate->product_id)
                    ->delete();

                // إنشاء سجل واحد بالكمية الإجمالية
                WarehouseProduct::create([
                    'warehouse_id' => $duplicate->warehouse_id,
                    'product_id' => $duplicate->product_id,
                    'quantity' => $duplicate->total_quantity,
                    'created_by' => Auth::user()->creatorId()
                ]);

                $cleanedCount++;
            }

            DB::commit();

            return response()->json([
                'success' => true,
                'message' => __('تم تنظيف السجلات المكررة بنجاح') . ': ' . $cleanedCount . ' ' . __('منتج'),
                'cleaned_count' => $cleanedCount
            ]);

        } catch (\Exception $e) {
            DB::rollback();

            return response()->json([
                'success' => false,
                'message' => __('حدث خطأ أثناء تنظيف السجلات') . ': ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * جلب شعار الشركة
     */
    private function getCompanyLogo()
    {
        try {
            // محاولة جلب الشعار من إعدادات الشركة
            $settings = \App\Models\Utility::settings();

            if (isset($settings['company_logo']) && !empty($settings['company_logo'])) {
                $logoPath = storage_path('app/public/' . $settings['company_logo']);

                if (file_exists($logoPath)) {
                    $logoData = base64_encode(file_get_contents($logoPath));
                    $logoMimeType = mime_content_type($logoPath);
                    return '<img src="data:' . $logoMimeType . ';base64,' . $logoData . '" class="logo" alt="شعار الشركة" />';
                }
            }

            // إذا لم يوجد شعار، استخدم شعار افتراضي
            return '<div class="logo" style="background-color: #f0f0f0; border: 1px solid #ccc; display: flex; align-items: center; justify-content: center; font-size: 12px; color: #666;">شعار الشركة</div>';

        } catch (\Exception $e) {
            \Log::error('خطأ في جلب شعار الشركة', ['error' => $e->getMessage()]);
            return '<div class="logo" style="background-color: #f0f0f0; border: 1px solid #ccc; display: flex; align-items: center; justify-content: center; font-size: 12px; color: #666;">شعار الشركة</div>';
        }
    }


}
