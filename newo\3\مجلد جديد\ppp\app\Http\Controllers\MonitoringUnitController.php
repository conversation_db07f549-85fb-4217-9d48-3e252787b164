<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use App\Models\Pos;
use App\Models\PosV2;
use App\Models\PosPayment;
use App\Models\PosV2Payment;
use App\Models\Customer;
use App\Models\User;
use App\Models\warehouse;
use App\Models\ProductService;
use App\Models\WarehouseProduct;
use App\Models\ReceiptOrder;
use App\Models\Purchase;
use App\Models\FinancialRecord;
use App\Models\FinancialTransactions;
use App\Models\DeliveryFinancialRecord;
use App\Models\Shift;
use App\Models\Budget;
use Carbon\Carbon;

class MonitoringUnitController extends Controller
{
    /**
     * عرض الصفحة الرئيسية لوحدة المراقبة
     */
    public function index()
    {
        // التحقق من الصلاحيات
        if (!Auth::user()->can('show financial record')) {
            return redirect()->back()->with('error', __('You are not authorized to perform this action'));
        }

        // جلب البيانات الأساسية للعرض
        $warehouses = warehouse::where('created_by', Auth::user()->creatorId())->get();
        $currentYear = Carbon::now()->year;
        $years = range($currentYear - 5, $currentYear + 1); // آخر 5 سنوات + السنة الحالية + السنة القادمة

        return view('financial_operations.monitoring_unit.index', compact('warehouses', 'years', 'currentYear'));
    }

    /**
     * جلب بيانات المبيعات
     */
    public function getSalesData(Request $request)
    {
        try {
            $creatorId = Auth::user()->creatorId();
            $warehouseId = $request->get('warehouse_id');
            $dateFrom = $request->get('date_from', Carbon::now()->startOfMonth());
            $dateTo = $request->get('date_to', Carbon::now()->endOfMonth());

            // إحصائيات المبيعات العامة من جدول pos
            $salesQuery = Pos::where('created_by', $creatorId)
                ->whereBetween('pos_date', [$dateFrom, $dateTo]);

            if ($warehouseId) {
                $salesQuery->where('warehouse_id', $warehouseId);
            }

            $totalSales = $salesQuery->count();

            // حساب إجمالي المبلغ من جدول pos_payments
            $totalAmount = $salesQuery->join('pos_payments', 'pos.id', '=', 'pos_payments.pos_id')
                ->sum('pos_payments.amount');

            // أعلى العملاء شراءً من جدول pos
            $topCustomers = Pos::select(
                    'customers.name',
                    DB::raw('COUNT(DISTINCT pos.id) as total_orders'),
                    DB::raw('SUM(pos_payments.amount) as total_amount')
                )
                ->join('customers', 'pos.customer_id', '=', 'customers.id')
                ->join('pos_payments', 'pos.id', '=', 'pos_payments.pos_id')
                ->where('pos.created_by', $creatorId)
                ->whereBetween('pos.pos_date', [$dateFrom, $dateTo])
                ->when($warehouseId, function($query) use ($warehouseId) {
                    return $query->where('pos.warehouse_id', $warehouseId);
                })
                ->groupBy('customers.id', 'customers.name')
                ->orderBy('total_amount', 'desc')
                ->limit(10)
                ->get();

            // أعلى المستخدمين إصداراً للفواتير من جدول pos
            $topUsers = Pos::select(
                    'users.name',
                    DB::raw('COUNT(DISTINCT pos.id) as total_invoices'),
                    DB::raw('SUM(pos_payments.amount) as total_amount')
                )
                ->join('users', 'pos.created_by', '=', 'users.id')
                ->join('pos_payments', 'pos.id', '=', 'pos_payments.pos_id')
                ->where('pos.created_by', $creatorId)
                ->whereBetween('pos.pos_date', [$dateFrom, $dateTo])
                ->when($warehouseId, function($query) use ($warehouseId) {
                    return $query->where('pos.warehouse_id', $warehouseId);
                })
                ->groupBy('users.id', 'users.name')
                ->orderBy('total_invoices', 'desc')
                ->limit(10)
                ->get();

            // مبيعات المستودعات من جدول pos
            $warehouseSales = Pos::select(
                    'warehouses.name',
                    DB::raw('COUNT(DISTINCT pos.id) as total_sales'),
                    DB::raw('SUM(pos_payments.amount) as total_amount')
                )
                ->join('warehouses', 'pos.warehouse_id', '=', 'warehouses.id')
                ->join('pos_payments', 'pos.id', '=', 'pos_payments.pos_id')
                ->where('pos.created_by', $creatorId)
                ->whereBetween('pos.pos_date', [$dateFrom, $dateTo])
                ->when($warehouseId, function($query) use ($warehouseId) {
                    return $query->where('pos.warehouse_id', $warehouseId);
                })
                ->groupBy('warehouses.id', 'warehouses.name')
                ->orderBy('total_amount', 'desc')
                ->get();

            // تفاصيل إضافية للعملاء مع أسماء واضحة
            $customerDetails = Pos::select(
                    'customers.name as customer_name',
                    'customers.email',
                    'customers.contact',
                    DB::raw('COUNT(DISTINCT pos.id) as total_orders'),
                    DB::raw('SUM(pos_payments.amount) as total_spent'),
                    DB::raw('AVG(pos_payments.amount) as average_order_value'),
                    DB::raw('MAX(pos.pos_date) as last_purchase_date')
                )
                ->join('customers', 'pos.customer_id', '=', 'customers.id')
                ->join('pos_payments', 'pos.id', '=', 'pos_payments.pos_id')
                ->where('pos.created_by', $creatorId)
                ->whereBetween('pos.pos_date', [$dateFrom, $dateTo])
                ->when($warehouseId, function($query) use ($warehouseId) {
                    return $query->where('pos.warehouse_id', $warehouseId);
                })
                ->groupBy('customers.id', 'customers.name', 'customers.email', 'customers.contact')
                ->orderBy('total_spent', 'desc')
                ->limit(15)
                ->get();

            return response()->json([
                'success' => true,
                'data' => [
                    'total_sales' => $totalSales,
                    'total_amount' => number_format($totalAmount, 2),
                    'top_customers' => $topCustomers,
                    'top_users' => $topUsers,
                    'warehouse_sales' => $warehouseSales,
                    'customer_details' => $customerDetails,
                    'average_sale' => $totalSales > 0 ? number_format($totalAmount / $totalSales, 2) : 0,
                    'data_source' => 'pos_table'
                ]
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'حدث خطأ في جلب بيانات المبيعات: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * جلب بيانات إدارة النقد
     */
    public function getCashData(Request $request)
    {
        try {
            $creatorId = Auth::user()->creatorId();
            $year = $request->get('year', Carbon::now()->year);
            $warehouseId = $request->get('warehouse_id');

            // إجمالي النقد من المبيعات (جدول pos)
            $salesCashQuery = PosPayment::join('pos', 'pos_payments.pos_id', '=', 'pos.id')
                ->where('pos.created_by', $creatorId)
                ->whereYear('pos.pos_date', $year);

            if ($warehouseId) {
                $salesCashQuery->where('pos.warehouse_id', $warehouseId);
            }

            $salesCash = $salesCashQuery->where('pos_payments.payment_type', 'cash')->sum('pos_payments.amount');
            $salesNetwork = $salesCashQuery->where('pos_payments.payment_type', 'network')->sum('pos_payments.amount');

            // بيانات من السجلات المالية
            $financialRecordsQuery = FinancialRecord::join('shifts', 'financial_records.shift_id', '=', 'shifts.id')
                ->join('users', 'shifts.created_by', '=', 'users.id')
                ->where('users.created_by', $creatorId)
                ->whereYear('financial_records.created_at', $year);

            if ($warehouseId) {
                $financialRecordsQuery->where('shifts.warehouse_id', $warehouseId);
            }

            $financialRecords = $financialRecordsQuery->get();
            $totalOpeningBalance = $financialRecords->sum('opening_balance');
            $totalCurrentCash = $financialRecords->sum('current_cash');
            $totalOvernetworkCash = $financialRecords->sum('overnetwork_cash');
            $totalDeliveryCash = $financialRecords->sum('delivery_cash');

            // بيانات المعاملات المالية
            $transactionsQuery = FinancialTransactions::join('shifts', 'financial_transactions.shift_id', '=', 'shifts.id')
                ->join('users', 'shifts.created_by', '=', 'users.id')
                ->where('users.created_by', $creatorId)
                ->whereYear('financial_transactions.created_at', $year);

            if ($warehouseId) {
                $transactionsQuery->where('shifts.warehouse_id', $warehouseId);
            }

            $transactionsCash = $transactionsQuery->where('payment_method', 'cash')->sum('cash_amount');
            $transactionsNetwork = $transactionsQuery->where('payment_method', 'bank_transfer')->sum('cash_amount');

            // الإجمالي النهائي
            $totalCash = $salesCash + $totalCurrentCash + $transactionsCash;
            $totalNetwork = $salesNetwork + $totalOvernetworkCash + $transactionsNetwork;
            $totalAmount = $totalCash + $totalNetwork;

            // النقد حسب المستخدمين من المبيعات (جدول pos)
            $userSalesData = PosPayment::select('users.name',
                    DB::raw('SUM(CASE WHEN pos_payments.payment_type = "cash" THEN pos_payments.amount ELSE 0 END) as sales_cash'),
                    DB::raw('SUM(CASE WHEN pos_payments.payment_type = "network" THEN pos_payments.amount ELSE 0 END) as sales_network'))
                ->join('pos', 'pos_payments.pos_id', '=', 'pos.id')
                ->join('users', 'pos.created_by', '=', 'users.id')
                ->where('pos.created_by', $creatorId)
                ->whereYear('pos.pos_date', $year)
                ->when($warehouseId, function($query) use ($warehouseId) {
                    return $query->where('pos.warehouse_id', $warehouseId);
                })
                ->groupBy('users.id', 'users.name')
                ->get();

            // النقد حسب المستخدمين من المعاملات المالية
            $userTransactionsData = FinancialTransactions::select('users.name',
                    DB::raw('SUM(CASE WHEN financial_transactions.payment_method = "cash" THEN financial_transactions.cash_amount ELSE 0 END) as transaction_cash'),
                    DB::raw('SUM(CASE WHEN financial_transactions.payment_method = "bank_transfer" THEN financial_transactions.cash_amount ELSE 0 END) as transaction_network'))
                ->join('shifts', 'financial_transactions.shift_id', '=', 'shifts.id')
                ->join('users', 'financial_transactions.created_by', '=', 'users.id')
                ->where('users.created_by', $creatorId)
                ->whereYear('financial_transactions.created_at', $year)
                ->when($warehouseId, function($query) use ($warehouseId) {
                    return $query->where('shifts.warehouse_id', $warehouseId);
                })
                ->groupBy('users.id', 'users.name')
                ->get();

            // دمج بيانات المستخدمين
            $userCashData = collect();
            $allUsers = $userSalesData->pluck('name')->merge($userTransactionsData->pluck('name'))->unique();

            foreach ($allUsers as $userName) {
                $salesData = $userSalesData->where('name', $userName)->first();
                $transactionData = $userTransactionsData->where('name', $userName)->first();

                $cashAmount = ($salesData->sales_cash ?? 0) + ($transactionData->transaction_cash ?? 0);
                $networkAmount = ($salesData->sales_network ?? 0) + ($transactionData->transaction_network ?? 0);

                $userCashData->push((object)[
                    'name' => $userName,
                    'cash_amount' => number_format($cashAmount, 2),
                    'network_amount' => number_format($networkAmount, 2),
                    'total_amount' => number_format($cashAmount + $networkAmount, 2)
                ]);
            }

            $userCashData = $userCashData->sortByDesc(function($user) {
                return (float)str_replace(',', '', $user->total_amount);
            })->values();

            return response()->json([
                'success' => true,
                'data' => [
                    'total_cash' => number_format($totalCash, 2),
                    'total_network' => number_format($totalNetwork, 2),
                    'total_amount' => number_format($totalAmount, 2),
                    'user_cash_data' => $userCashData,
                    'financial_summary' => [
                        'opening_balance' => number_format($totalOpeningBalance, 2),
                        'current_cash' => number_format($totalCurrentCash, 2),
                        'delivery_cash' => number_format($totalDeliveryCash, 2),
                        'sales_cash' => number_format($salesCash, 2),
                        'sales_network' => number_format($salesNetwork, 2)
                    ],
                    'cash_percentage' => $totalAmount > 0 ? round(($totalCash / $totalAmount) * 100, 2) : 0,
                    'network_percentage' => $totalAmount > 0 ? round(($totalNetwork / $totalAmount) * 100, 2) : 0
                ]
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'حدث خطأ في جلب بيانات النقد: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * جلب بيانات أوامر الاستلام
     */
    public function getReceiptOrdersData(Request $request)
    {
        try {
            $creatorId = Auth::user()->creatorId();
            $warehouseId = $request->get('warehouse_id');
            $dateFrom = $request->get('date_from', Carbon::now()->startOfMonth());
            $dateTo = $request->get('date_to', Carbon::now()->endOfMonth());

            // إحصائيات أوامر الاستلام
            $ordersQuery = ReceiptOrder::where('created_by', $creatorId)
                ->whereBetween('created_at', [$dateFrom, $dateTo]);

            if ($warehouseId) {
                $ordersQuery->where('warehouse_id', $warehouseId);
            }

            $totalOrders = $ordersQuery->count();
            $totalAmount = $ordersQuery->sum('total_amount');

            // أوامر حسب النوع
            $ordersByType = ReceiptOrder::select('order_type', DB::raw('COUNT(*) as count'), DB::raw('SUM(total_amount) as total'))
                ->where('created_by', $creatorId)
                ->whereBetween('created_at', [$dateFrom, $dateTo])
                ->when($warehouseId, function($query) use ($warehouseId) {
                    return $query->where('warehouse_id', $warehouseId);
                })
                ->groupBy('order_type')
                ->get();

            // متوسط الكميات
            $averageQuantity = ReceiptOrder::where('created_by', $creatorId)
                ->whereBetween('created_at', [$dateFrom, $dateTo])
                ->when($warehouseId, function($query) use ($warehouseId) {
                    return $query->where('warehouse_id', $warehouseId);
                })
                ->avg('total_products');

            // معدل دوران الأوامر (أوامر في اليوم)
            $daysDiff = Carbon::parse($dateFrom)->diffInDays(Carbon::parse($dateTo)) + 1;
            $dailyAverage = $daysDiff > 0 ? $totalOrders / $daysDiff : 0;

            return response()->json([
                'success' => true,
                'data' => [
                    'total_orders' => $totalOrders,
                    'total_amount' => number_format($totalAmount, 2),
                    'orders_by_type' => $ordersByType,
                    'average_quantity' => number_format($averageQuantity, 2),
                    'daily_average' => number_format($dailyAverage, 2),
                    'average_order_value' => $totalOrders > 0 ? number_format($totalAmount / $totalOrders, 2) : 0
                ]
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'حدث خطأ في جلب بيانات أوامر الاستلام: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * جلب بيانات المخزون
     */
    public function getInventoryData(Request $request)
    {
        try {
            $creatorId = Auth::user()->creatorId();
            $warehouseId = $request->get('warehouse_id');
            $dateFrom = $request->get('date_from', Carbon::now()->startOfMonth());
            $dateTo = $request->get('date_to', Carbon::now()->endOfMonth());

            // أعلى المنتجات مبيعاً من جدول pos
            $topSellingProducts = DB::table('pos_products')
                ->join('pos', 'pos_products.pos_id', '=', 'pos.id')
                ->join('product_services', 'pos_products.product_id', '=', 'product_services.id')
                ->select('product_services.name', 'product_services.sku',
                    DB::raw('SUM(pos_products.quantity) as total_quantity'),
                    DB::raw('SUM(pos_products.total) as total_amount'),
                    DB::raw('COUNT(DISTINCT pos.id) as total_orders'))
                ->where('pos.created_by', $creatorId)
                ->whereBetween('pos.pos_date', [$dateFrom, $dateTo])
                ->when($warehouseId, function($query) use ($warehouseId) {
                    return $query->where('pos.warehouse_id', $warehouseId);
                })
                ->groupBy('product_services.id', 'product_services.name', 'product_services.sku')
                ->orderBy('total_quantity', 'desc')
                ->limit(15)
                ->get();

            // معدل البيع اليومي
            $daysDiff = Carbon::parse($dateFrom)->diffInDays(Carbon::parse($dateTo)) + 1;
            $dailySalesData = $topSellingProducts->map(function($product) use ($daysDiff) {
                $product->daily_average = $daysDiff > 0 ? round($product->total_quantity / $daysDiff, 2) : 0;
                return $product;
            });

            // إحصائيات المخزون العامة
            $inventoryQuery = WarehouseProduct::join('product_services', 'warehouse_products.product_id', '=', 'product_services.id')
                ->where('product_services.created_by', $creatorId);

            if ($warehouseId) {
                $inventoryQuery->where('warehouse_products.warehouse_id', $warehouseId);
            }

            $totalProducts = $inventoryQuery->count();
            $totalStock = $inventoryQuery->sum('warehouse_products.quantity');
            $lowStockProducts = $inventoryQuery->where('warehouse_products.quantity', '<=', 10)->count();

            // المنتجات منخفضة المخزون
            $lowStockItems = WarehouseProduct::join('product_services', 'warehouse_products.product_id', '=', 'product_services.id')
                ->join('warehouses', 'warehouse_products.warehouse_id', '=', 'warehouses.id')
                ->select('product_services.name', 'product_services.sku', 'warehouses.name as warehouse_name', 'warehouse_products.quantity')
                ->where('product_services.created_by', $creatorId)
                ->where('warehouse_products.quantity', '<=', 10)
                ->when($warehouseId, function($query) use ($warehouseId) {
                    return $query->where('warehouse_products.warehouse_id', $warehouseId);
                })
                ->orderBy('warehouse_products.quantity', 'asc')
                ->limit(10)
                ->get();

            return response()->json([
                'success' => true,
                'data' => [
                    'top_selling_products' => $dailySalesData,
                    'total_products' => $totalProducts,
                    'total_stock' => $totalStock,
                    'low_stock_products' => $lowStockProducts,
                    'low_stock_items' => $lowStockItems,
                    'days_analyzed' => $daysDiff
                ]
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'حدث خطأ في جلب بيانات المخزون: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * جلب بيانات الميزانية
     */
    public function getBudgetData(Request $request)
    {
        try {
            $creatorId = Auth::user()->creatorId();
            $year = $request->get('year', Carbon::now()->year);
            $budgetId = $request->get('budget_id');
            $budgetType = $request->get('budget_type', 'sales'); // sales, purchases, profit, budget

            // جلب قائمة الميزانيات المتاحة
            $budgets = Budget::where('created_by', $creatorId)->get();

            $monthlyData = [];
            $totalAmount = 0;
            $budgetName = '';

            if ($budgetType === 'budget' && $budgetId) {
                // عرض ميزانية محددة
                $budget = Budget::where('id', $budgetId)->where('created_by', $creatorId)->first();

                if ($budget) {
                    $budgetName = $budget->name;
                    $incomeData = json_decode($budget->income_data, true) ?? [];
                    $expenseData = json_decode($budget->expense_data, true) ?? [];

                    for ($month = 1; $month <= 12; $month++) {
                        $monthName = Carbon::create($year, $month, 1)->format('M');
                        $income = $incomeData[$month] ?? 0;
                        $expense = $expenseData[$month] ?? 0;
                        $netAmount = $income - $expense;

                        $monthlyData[] = [
                            'month' => $month,
                            'month_name' => $monthName,
                            'amount' => $netAmount,
                            'income' => $income,
                            'expense' => $expense
                        ];

                        $totalAmount += $netAmount;
                    }
                }
            } else {
                // عرض البيانات الفعلية
                for ($month = 1; $month <= 12; $month++) {
                    $monthData = [
                        'month' => $month,
                        'month_name' => Carbon::create($year, $month, 1)->format('M'),
                        'amount' => 0
                    ];

                    if ($budgetType === 'sales') {
                        // بيانات المبيعات من جدول pos
                        $amount = PosPayment::join('pos', 'pos_payments.pos_id', '=', 'pos.id')
                            ->where('pos.created_by', $creatorId)
                            ->whereYear('pos.pos_date', $year)
                            ->whereMonth('pos.pos_date', $month)
                            ->sum('pos_payments.amount');
                        $budgetName = 'المبيعات الفعلية';
                    } elseif ($budgetType === 'purchases') {
                        // بيانات المشتريات
                        $amount = Purchase::join('purchase_products', 'purchases.id', '=', 'purchase_products.purchase_id')
                            ->where('purchases.created_by', $creatorId)
                            ->whereYear('purchases.purchase_date', $year)
                            ->whereMonth('purchases.purchase_date', $month)
                            ->sum('purchase_products.total');
                        $budgetName = 'المشتريات الفعلية';
                    } else {
                        // الربح (المبيعات - المشتريات) من جدول pos
                        $sales = PosPayment::join('pos', 'pos_payments.pos_id', '=', 'pos.id')
                            ->where('pos.created_by', $creatorId)
                            ->whereYear('pos.pos_date', $year)
                            ->whereMonth('pos.pos_date', $month)
                            ->sum('pos_payments.amount');

                        $purchases = Purchase::join('purchase_products', 'purchases.id', '=', 'purchase_products.purchase_id')
                            ->where('purchases.created_by', $creatorId)
                            ->whereYear('purchases.purchase_date', $year)
                            ->whereMonth('purchases.purchase_date', $month)
                            ->sum('purchase_products.total');

                        $amount = $sales - $purchases;
                        $budgetName = 'الأرباح الفعلية';
                    }

                    $monthData['amount'] = $amount;
                    $totalAmount += $amount;
                    $monthlyData[] = $monthData;
                }
            }

            // إحصائيات إضافية
            $maxMonth = collect($monthlyData)->sortByDesc('amount')->first();
            $minMonth = collect($monthlyData)->sortBy('amount')->first();
            $averageMonthly = $totalAmount / 12;

            return response()->json([
                'success' => true,
                'data' => [
                    'monthly_data' => $monthlyData,
                    'total_amount' => number_format($totalAmount, 2),
                    'average_monthly' => number_format($averageMonthly, 2),
                    'max_month' => $maxMonth,
                    'min_month' => $minMonth,
                    'budget_type' => $budgetType,
                    'budget_name' => $budgetName,
                    'year' => $year,
                    'available_budgets' => $budgets->map(function($budget) {
                        return [
                            'id' => $budget->id,
                            'name' => $budget->name,
                            'period' => $budget->period,
                            'from' => $budget->from,
                            'to' => $budget->to
                        ];
                    })
                ]
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'حدث خطأ في جلب بيانات الميزانية: ' . $e->getMessage()
            ], 500);
        }
    }
}
