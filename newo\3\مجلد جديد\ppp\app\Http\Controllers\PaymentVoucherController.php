<?php

namespace App\Http\Controllers;

use App\Http\Requests\PaymentVoucher as RequestsPaymentVoucher;
use App\Models\PaymentVoucher;
use App\Models\Shift;
use App\Models\User;
use App\Services\FinancialRecordService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Mpdf\Mpdf;

class PaymentVoucherController extends Controller
{

    protected $financialRecordService;

    public function __construct(FinancialRecordService $financialRecordService)
    {
        $this->financialRecordService = $financialRecordService;
    }

    private function initializePDF()
    {

        // Initialize mPDF instance
        $mpdf = new Mpdf([
            'mode' => 'utf-8',  // UTF-8 encoding for Arabic support
            'format' => 'A4',   // Set the page format to A4
            'orientation' => 'P',
            'allow_origin' => '*',
            'margin_top' => '36',
            'margin_bottom' => '25',
        ]);
        // Enable automatic script-to-language conversion and font auto-selection
        $mpdf->autoScriptToLang = true;
        $mpdf->autoLangToFont = true;
        $PDF_header = view('pdf.includes.header')->render();
        $PDF_footer = view('pdf.includes.footer')->render();
        $mpdf->SetHTMLHeader($PDF_header);
        $mpdf->SetHTMLFooter($PDF_footer);
        // app()->setLocale('ar'); debug purpose only delete when finished
        return $mpdf;
    }

    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        //
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        $user      = \Auth::user();
        $users = User::where('created_by', '=', $user->ownerId())->get()->pluck('name', 'id');
        return view('voucher.payment.create', get_defined_vars());
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {

        $user = \Auth::user();
        $openShift = Shift::whereNull('closed_at')
            ->where('warehouse_id', $user->warehouse_id)
            ->first();
        $branchName = $user->warehouse?->name;
        $branchName = isset($branchName) ? $branchName : "الفرع الرئيسى";
        $count  = PaymentVoucher::count();
        $index = $count + 1;
        $custome_id = "PUR-" . $branchName . "-" . $index;
        $request->merge([
            "created_by" => $user->id,
            "custome_id" => $custome_id,
            "warehouse_id"=> $user->warehouse_id,
            "shift_id"=>$openShift->id
        ]);
        PaymentVoucher::create($request->all());

        return redirect()->back()->with('success', __('Payment Voucher has been Created successfully'));
    }

    /**
     * Display the specified resource.
     */
    public function show(int $id)
    {
        $payment = PaymentVoucher::find($id);
        return view('voucher.payment.show', get_defined_vars());
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(string $id)
    {
        $payment = PaymentVoucher::find($id);
        $user      = \Auth::user();
        $users = User::where('created_by', '=', $user->ownerId())->get()->pluck('name', 'id');

        return view('voucher.payment.edit', get_defined_vars());

    }

    /**
     * Update the specified resource in storage.
     */
    public function update(RequestsPaymentVoucher $request, string $id)
    {
        try {
            $validated = $request->validated();
            $payment = PaymentVoucher::find($id);

            if($payment->status == "accepted") {
                return redirect()->back()->with('error', __('Cannot update accepted payment voucher'));
            }

            $payment->update($validated);
            $payment->save();

            return redirect()->back()->with('success', __('Receipt Voucher has been Updated successfully'));

        } catch (\Throwable $e) {
            //throw $e;
            return redirect()->back()->with('error', $e->getMessage());
        }
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(string $id)
    {
        //
    }


    public function showConfirmVoucher(int $id)
    {
        return view('voucher.payment.popup', compact('id'));
    }

    public function confirmVoucher(Request $request)
    {
        $paymentVoucher = PaymentVoucher::where('id', $request->id)->update([
            "status" => "accepted",
            "approved_at" => now()
        ]);
        try {
            $user = Auth::user();
            $paymentVoucher = PaymentVoucher::where('id', $request->id)->first();

            $payment_amount = $paymentVoucher->payment_amount;
            $payment_method = $paymentVoucher->payment_method;

            $financialRecord = $this->financialRecordService->updateCurrentCashOnPaymentVoucher($user, $payment_amount, $payment_method);

            if (!$financialRecord['success']) {
                throw new \Exception($financialRecord['message']);
            }

            $receiptVoucher = PaymentVoucher::where('id', $request->id)->update([
                "status" => "accepted"
            ]);

            return redirect()->back()->with('success', __('Payment Voucher has been Accepted successfully'));
        } catch (\Throwable $e) {
            //throw $e;
            return redirect()->back()->with('error',  $e->getMessage());
        }
    }

    public function exportToPdf(Request $request)
    {
        $voucher_id = $request->voucher_id;
        $user = \Auth::user();
        $paymentVoucher = PaymentVoucher::where('id', $voucher_id)->first();
        $mpdf = $this->initializePDF();

        $html = view('pdf.payment_voucher', compact('user', 'paymentVoucher'))->render();
        $mpdf->WriteHTML($html);

        return response($mpdf->Output('admins.pdf', 'I'), 200)
            ->header('Content-Type', 'application/pdf');
    }
}
