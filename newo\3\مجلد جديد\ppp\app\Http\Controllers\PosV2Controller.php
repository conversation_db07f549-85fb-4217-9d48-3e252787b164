<?php

namespace App\Http\Controllers;

use App\Models\Customer;
use App\Models\PosV2;
use App\Models\PosV2Payment;
use App\Models\PosV2Product;
use App\Models\ProductService;
use App\Models\Shift;
use App\Models\StockReport;
use App\Models\User;
use App\Models\Utility;
use App\Models\warehouse;
use App\Models\WarehouseProduct;
use App\Models\FinancialRecord;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Crypt;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\Storage;

class PosV2Controller extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request, $cid = 0)
    {
        if (Auth::user()->can('manage pos')) {
            $user = Auth::user();
            $settings = Utility::settings();

            $customers = Customer::where('created_by', $user->creatorId())->get()->pluck('name', 'id');
            $customers->prepend('Walk-in-customer', 0);

            $warehouses = warehouse::where('created_by', $user->creatorId())->get()->pluck('name', 'id');

            $lastsegment = 'pos_v2';

            return view('pos_v2.index', compact('customers', 'warehouses', 'cid', 'lastsegment', 'settings'));
        } else {
            return redirect()->back()->with('error', __('Permission denied.'));
        }
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        return redirect()->route('pos_v2.index');
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        if (Auth::user()->can('manage pos')) {
            $sess = session()->get('pos_v2');

            if (isset($sess) && !empty($sess) && count($sess) > 0) {
                $user = Auth::user();
                $settings = Utility::settings();

                $customer = Customer::where('id', '=', $request->vc_name)->where('created_by', $user->creatorId())->first();
                $warehouse = warehouse::where('id', '=', $request->warehouse_name)->where('created_by', $user->creatorId())->first();

                $details = [
                    'pos_id' => $user->posNumberFormat($this->invoicePosV2Number()),
                    'customer' => $customer != null ? $customer->toArray() : [],
                    'warehouse' => $warehouse != null ? $warehouse->toArray() : [],
                    'user' => $user != null ? $user->toArray() : [],
                    'date' => date('Y-m-d'),
                    'pay' => 'show',
                ];

                $mainsubtotal = 0;
                $totalTax = 0;
                $sales = [];

                foreach ($sess as $key => $value) {
                    $product = ProductService::find($value['product_id']);

                    if ($product) {
                        $subtotal = $value['price'] * $value['quantity'];
                        $mainsubtotal += $subtotal;

                        $tax = 0;
                        if (!empty($value['tax'])) {
                            $taxes = explode(',', $value['tax']);
                            foreach ($taxes as $taxRate) {
                                $taxAmount = ($subtotal * $taxRate) / 100;
                                $tax += $taxAmount;
                            }
                        }
                        $totalTax += $tax;

                        $sales['data'][$key]['name'] = $product['name'];
                        $sales['data'][$key]['quantity'] = $value['quantity'];
                        $sales['data'][$key]['price'] = Auth::user()->priceFormat($value['price']);
                        $sales['data'][$key]['discount'] = Auth::user()->priceFormat($value['discount']);
                        $sales['data'][$key]['tax'] = Auth::user()->priceFormat($tax);
                        $sales['data'][$key]['total'] = Auth::user()->priceFormat($subtotal + $tax - $value['discount']);
                    }
                }

                $discount = !empty($request->discount) ? $request->discount : 0;
                $sales['discount'] = Auth::user()->priceFormat($discount);

                // حساب المجموع الفرعي (قبل الضريبة)
                $sales['subtotal'] = $mainsubtotal;
                $sales['sub_total'] = Auth::user()->priceFormat($mainsubtotal);

                // حساب إجمالي الضريبة
                $sales['tax'] = $totalTax;
                $sales['tax_format'] = Auth::user()->priceFormat($totalTax);

                // حساب الإجمالي النهائي (المجموع الفرعي + الضريبة - الخصم)
                $total = $mainsubtotal + $totalTax - $discount;
                // تطبيق تنسيق الأرقام العشرية على القيمة الإجمالية لضمان التطابق مع العرض
                $sales['total'] = round($total, 2);

                return view('pos_v2.show', compact('sales', 'details'));
            } else {
                return response()->json(
                    [
                        'error' => __('Add some products to cart!'),
                    ],
                    '404'
                );
            }
        } else {
            return redirect()->back()->with('error', __('Permission denied.'));
        }
    }

    /**
     * Generate POS V2 invoice number
     */
    public function invoicePosV2Number()
    {
        $latest = PosV2::where('created_by', '=', Auth::user()->creatorId())->latest()->first();
        if (!$latest) {
            return 1;
        }

        return $latest->pos_id + 1;
    }

    /**
     * Add product to cart
     */
    public function addToCart(Request $request)
    {
        $product_id = $request->product_id;
        $session_key = $request->session_key;

        if ($product_id) {
            $product = ProductService::find($product_id);

            if ($product) {
                $productquantity = 0;
                $productprice = $product->sale_price;
                $originalquantity = $product->quantity;

                $cart = session()->get($session_key);

                // Check if product already exists in cart
                if (isset($cart[$product_id])) {
                    $cart[$product_id]['quantity']++;
                    $cart[$product_id]['id'] = $product_id;
                } else {
                    $cart[$product_id] = [
                        "product_id" => $product->id,
                        "name" => $product->name,
                        "quantity" => 1,
                        "price" => $productprice,
                        "id" => $product_id,
                        "tax" => $product->tax_id,
                        "discount" => 0,
                        "originalquantity" => $originalquantity,
                    ];
                }

                session()->put($session_key, $cart);

                $response = [
                    'product' => $cart[$product_id],
                    'carthtml' => $this->cartHtml($cart[$product_id]),
                ];

                return response()->json($response);
            }
        }

        return response()->json(['error' => __('Product not found')], 404);
    }

    /**
     * Generate cart HTML for a product
     */
    private function cartHtml($product)
    {
        $html = '<tr id="product-id-' . $product['id'] . '">';
        $html .= '<td><button class="btn btn-sm btn-danger remove-from-cart" data-id="' . $product['id'] . '"><i class="ti ti-trash"></i></button></td>';
        $html .= '<td class="text-left">' . $product['name'] . '</td>';
        $html .= '<td class="text-center"><input type="number" name="quantity" value="' . $product['quantity'] . '" min="1" class="form-control quantity-input" data-id="' . $product['id'] . '"></td>';
        $html .= '<td>' . Auth::user()->priceFormat(0) . '</td>'; // Tax placeholder
        $html .= '<td class="text-center">' . Auth::user()->priceFormat($product['price']) . '</td>';
        $html .= '<td class="text-center">' . Auth::user()->priceFormat($product['price'] * $product['quantity']) . '</td>';
        $html .= '<td></td>';
        $html .= '</tr>';

        return $html;
    }

    /**
     * Remove product from cart
     */
    public function removeFromCart(Request $request)
    {
        $product_id = $request->product_id;
        $session_key = $request->session_key;

        $cart = session()->get($session_key);

        if (isset($cart[$product_id])) {
            unset($cart[$product_id]);
            session()->put($session_key, $cart);
        }

        return response()->json(['success' => __('Product removed from cart')]);
    }

    /**
     * Update cart quantity
     */
    public function updateCart(Request $request)
    {
        $product_id = $request->product_id;
        $quantity = $request->quantity;
        $session_key = $request->session_key;

        $cart = session()->get($session_key);

        if (isset($cart[$product_id])) {
            $cart[$product_id]['quantity'] = $quantity;
            session()->put($session_key, $cart);
        }

        return response()->json(['success' => __('Cart updated')]);
    }

    /**
     * Empty cart
     */
    public function emptyCart(Request $request)
    {
        $session_key = $request->session_key;
        session()->forget($session_key);

        return response()->json(['success' => __('Cart emptied')]);
    }

    /**
     * Store POS V2 data and create invoice
     */
    public function dataStore(Request $request)
    {
        if (Auth::user()->can('manage pos')) {
            $sess = session()->get('pos_v2');

            if (isset($sess) && !empty($sess) && count($sess) > 0) {
                $user = Auth::user();
                $settings = Utility::settings();

                // Create POS V2 record
                $pos = new PosV2();
                $pos->pos_id = $this->invoicePosV2Number();
                $pos->customer_id = $request->vc_name ?? 0;
                $pos->warehouse_id = $request->warehouse_name ?? 0;
                $pos->pos_date = date('Y-m-d');
                $pos->created_by = $user->creatorId();
                $pos->user_id = $user->id;
                $pos->status = 1;
                $pos->status_type = 'normal';
                $pos->save();

                $mainsubtotal = 0;
                $totalTax = 0;
                $totalDiscount = 0;

                // Create POS V2 products
                foreach ($sess as $key => $value) {
                    $product = ProductService::find($value['product_id']);

                    if ($product) {
                        $posProduct = new PosV2Product();
                        $posProduct->pos_id = $pos->id;
                        $posProduct->product_id = $value['product_id'];
                        $posProduct->quantity = $value['quantity'];
                        $posProduct->price = $value['price'];
                        $posProduct->tax = $value['tax'] ?? '';
                        $posProduct->discount = $value['discount'] ?? 0;
                        $posProduct->total = $value['price'] * $value['quantity'];
                        $posProduct->total_discount = $value['discount'] ?? 0;
                        $posProduct->save();

                        $mainsubtotal += $posProduct->total;
                        $totalDiscount += $posProduct->total_discount;

                        // Calculate tax
                        if (!empty($value['tax'])) {
                            $taxes = explode(',', $value['tax']);
                            foreach ($taxes as $taxRate) {
                                $taxAmount = ($posProduct->total * $taxRate) / 100;
                                $totalTax += $taxAmount;
                            }
                        }

                        // Update warehouse product quantity
                        $warehouseProduct = WarehouseProduct::where('warehouse_id', $pos->warehouse_id)
                            ->where('product_id', $value['product_id'])
                            ->first();

                        if ($warehouseProduct) {
                            $warehouseProduct->quantity -= $value['quantity'];
                            $warehouseProduct->save();
                        }

                        // Create stock report
                        $stockReport = new StockReport();
                        $stockReport->product_id = $value['product_id'];
                        $stockReport->warehouse_id = $pos->warehouse_id;
                        $stockReport->type = 'sale';
                        $stockReport->type_id = $pos->id;
                        $stockReport->quantity = $value['quantity'];
                        $stockReport->created_by = $user->creatorId();
                        $stockReport->save();
                    }
                }

                // Create payment record
                $posPayment = new PosV2Payment();
                $posPayment->pos_id = $pos->id;
                $posPayment->date = date('Y-m-d');
                $posPayment->amount = round($mainsubtotal + $totalTax - $totalDiscount, 2);
                $posPayment->discount = $request->discount ?? 0;
                $posPayment->payment_type = $request->payment_type ?? 'cash';
                $posPayment->created_by = $user->creatorId();

                if ($request->payment_type == 'cash') {
                    $posPayment->cash_amount = $posPayment->amount;
                } elseif ($request->payment_type == 'network') {
                    $posPayment->network_amount = $posPayment->amount;
                    $posPayment->transaction_number = $request->transaction_number ?? '';
                } elseif ($request->payment_type == 'split') {
                    $posPayment->cash_amount = $request->cash_amount ?? 0;
                    $posPayment->network_amount = $request->network_amount ?? 0;
                    $posPayment->transaction_number = $request->transaction_number ?? '';
                }

                $posPayment->save();

                // Clear cart session
                session()->forget('pos_v2');

                return response()->json([
                    'code' => 200,
                    'success' => __('POS V2 invoice created successfully'),
                    'pos_id' => $pos->id
                ]);
            } else {
                return response()->json([
                    'code' => 404,
                    'error' => __('No items in cart')
                ]);
            }
        } else {
            return response()->json([
                'code' => 403,
                'error' => __('Permission denied')
            ]);
        }
    }

    /**
     * Process payment
     */
    public function processPayment(Request $request)
    {
        // This method can be used for additional payment processing
        // For now, it redirects to dataStore
        return $this->dataStore($request);
    }

    /**
     * Thermal print
     */
    public function thermalPrint($id)
    {
        $pos = PosV2::with(['customer', 'warehouse', 'items.product', 'posPayment'])->find($id);

        if (!$pos) {
            return redirect()->back()->with('error', __('POS V2 invoice not found'));
        }

        $settings = Utility::settings();
        $company_logo = asset(Storage::url('uploads/logo/')) . '/' .
            (isset($settings['company_logo']) && !empty($settings['company_logo']) ?
             $settings['company_logo'] : 'logo-dark.png');

        return view('pos_v2.thermal_print', compact('pos', 'settings', 'company_logo'));
    }

    /**
     * Display the specified resource.
     */
    public function show(string $id)
    {
        $pos = PosV2::with(['customer', 'warehouse', 'items.product', 'posPayment'])->find($id);

        if (!$pos) {
            return redirect()->back()->with('error', __('POS V2 invoice not found'));
        }

        return view('pos_v2.view', compact('pos'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(string $id)
    {
        return redirect()->route('pos_v2.index');
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, string $id)
    {
        return redirect()->route('pos_v2.index');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(string $id)
    {
        if (Auth::user()->can('delete pos')) {
            $pos = PosV2::find($id);

            if ($pos) {
                // Delete related records
                PosV2Product::where('pos_id', $id)->delete();
                PosV2Payment::where('pos_id', $id)->delete();

                $pos->delete();

                return redirect()->back()->with('success', __('POS V2 invoice deleted successfully'));
            }
        }

        return redirect()->back()->with('error', __('Permission denied'));
    }
}
