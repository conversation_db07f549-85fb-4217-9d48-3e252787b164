<?php

namespace App\Http\Controllers;

use App\Models\ReceiptOrder;
use App\Models\warehouse;
use App\Models\Vender;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class ReceiptOrderController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        try {
            $receiptOrders = ReceiptOrder::with(['vendor', 'warehouse', 'fromWarehouse', 'creator'])
                ->orderBy('created_at', 'desc')
                ->get();

            $warehouses = warehouse::all();

            // تحويل البيانات للعرض
            $formattedOrders = $receiptOrders->map(function ($order) {
                return [
                    'id' => $order->id,
                    'reference_number' => $order->order_number,
                    'type' => $order->order_type,
                    'vendor_name' => $order->vendor->name ?? 'غير محدد',
                    'warehouse_name' => $order->warehouse->name ?? 'غير محدد',
                    'from_warehouse_name' => $order->fromWarehouse->name ?? null,
                    'creator_name' => $order->creator->name ?? 'غير محدد',
                    'total_products' => $order->total_products,
                    'date' => $order->invoice_date ?? $order->created_at,
                    'created_at' => $order->created_at,
                ];
            });

            return view('receipt_order.index', compact('receiptOrders', 'warehouses'))
                ->with('formattedOrders', $formattedOrders);

        } catch (\Exception $e) {
            Log::error('Receipt Order Index Error: ' . $e->getMessage());
            return view('receipt_order.index')
                ->with('receiptOrders', collect())
                ->with('warehouses', collect())
                ->with('error', 'حدث خطأ في تحميل البيانات');
        }
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        try {
            $warehouses = warehouse::all();
            $vendors = Vender::all();

            return view('receipt_order.create', compact('warehouses', 'vendors'));

        } catch (\Exception $e) {
            Log::error('Receipt Order Create Error: ' . $e->getMessage());
            return redirect()->back()->with('error', 'حدث خطأ في تحميل صفحة الإنشاء');
        }
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        try {
            // تسجيل البيانات المرسلة للتشخيص
            Log::info('Receipt Order Store Request:', $request->all());

            // التحقق من صحة البيانات
            $validated = $request->validate([
                'order_type' => 'required|string|in:استلام بضاعة,نقل بضاعة,أمر إخراج',
                'warehouse_id' => 'required|integer|exists:warehouses,id',
                'vendor_id' => 'nullable|integer|exists:venders,id',
                'from_warehouse_id' => 'nullable|integer|exists:warehouses,id',
                'invoice_number' => 'nullable|string|max:255',
                'invoice_total' => 'nullable|numeric|min:0',
                'invoice_date' => 'nullable|date',
                'notes' => 'nullable|string',
                'exit_reason' => 'nullable|string|in:فقدان,منتهي الصلاحية,تلف/خراب,بيع بالتجزئة',
                'exit_date' => 'nullable|date',
                'responsible_person' => 'nullable|string|max:255',
            ]);

            // إضافة البيانات الإضافية
            $validated['created_by'] = Auth::id();
            $validated['total_products'] = 0;
            $validated['total_amount'] = $validated['invoice_total'] ?? 0;

            // إنشاء رقم الأمر إذا لم يكن موجود
            if (empty($validated['order_number'])) {
                $year = date('Y');
                $count = ReceiptOrder::whereYear('created_at', $year)->count() + 1;
                $validated['order_number'] = 'RO-' . $year . '-' . str_pad($count, 6, '0', STR_PAD_LEFT);
            }

            // حفظ البيانات
            DB::beginTransaction();

            $receiptOrder = ReceiptOrder::create($validated);

            DB::commit();

            Log::info('Receipt Order Created Successfully:', ['id' => $receiptOrder->id]);

            return redirect()->route('receipt-order.index')
                ->with('success', 'تم إنشاء أمر الاستلام بنجاح - رقم الأمر: ' . $receiptOrder->order_number);

        } catch (\Illuminate\Validation\ValidationException $e) {
            Log::error('Receipt Order Validation Error:', $e->errors());
            return redirect()->back()
                ->withErrors($e->errors())
                ->withInput()
                ->with('error', 'يرجى التحقق من البيانات المدخلة');

        } catch (\Exception $e) {
            DB::rollback();
            Log::error('Receipt Order Store Error: ' . $e->getMessage());
            Log::error('Stack Trace: ' . $e->getTraceAsString());

            return redirect()->back()
                ->withInput()
                ->with('error', 'حدث خطأ في حفظ البيانات: ' . $e->getMessage());
        }
    }

    /**
     * Display the specified resource.
     */
    public function show($id)
    {
        try {
            $receiptOrder = ReceiptOrder::with(['vendor', 'warehouse', 'fromWarehouse', 'creator', 'products'])
                ->findOrFail($id);

            return view('receipt_order.show', compact('receiptOrder'));

        } catch (\Exception $e) {
            Log::error('Receipt Order Show Error: ' . $e->getMessage());
            return redirect()->back()->with('error', 'حدث خطأ في عرض البيانات');
        }
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit($id)
    {
        try {
            $receiptOrder = ReceiptOrder::findOrFail($id);
            $warehouses = warehouse::all();
            $vendors = Vender::all();

            return view('receipt_order.edit', compact('receiptOrder', 'warehouses', 'vendors'));

        } catch (\Exception $e) {
            Log::error('Receipt Order Edit Error: ' . $e->getMessage());
            return redirect()->back()->with('error', 'حدث خطأ في تحميل صفحة التعديل');
        }
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, $id)
    {
        try {
            $receiptOrder = ReceiptOrder::findOrFail($id);

            $validated = $request->validate([
                'order_type' => 'required|string|in:استلام بضاعة,نقل بضاعة,أمر إخراج',
                'warehouse_id' => 'required|integer|exists:warehouses,id',
                'vendor_id' => 'nullable|integer|exists:venders,id',
                'from_warehouse_id' => 'nullable|integer|exists:warehouses,id',
                'invoice_number' => 'nullable|string|max:255',
                'invoice_total' => 'nullable|numeric|min:0',
                'invoice_date' => 'nullable|date',
                'notes' => 'nullable|string',
                'exit_reason' => 'nullable|string|in:فقدان,منتهي الصلاحية,تلف/خراب,بيع بالتجزئة',
                'exit_date' => 'nullable|date',
                'responsible_person' => 'nullable|string|max:255',
            ]);

            $validated['total_amount'] = $validated['invoice_total'] ?? $receiptOrder->total_amount;

            $receiptOrder->update($validated);

            return redirect()->route('receipt-order.index')
                ->with('success', 'تم تحديث أمر الاستلام بنجاح');

        } catch (\Exception $e) {
            Log::error('Receipt Order Update Error: ' . $e->getMessage());
            return redirect()->back()
                ->withInput()
                ->with('error', 'حدث خطأ في تحديث البيانات: ' . $e->getMessage());
        }
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy($id)
    {
        try {
            $receiptOrder = ReceiptOrder::findOrFail($id);
            $orderNumber = $receiptOrder->order_number;
            
            $receiptOrder->delete();

            return redirect()->route('receipt-order.index')
                ->with('success', 'تم حذف أمر الاستلام ' . $orderNumber . ' بنجاح');

        } catch (\Exception $e) {
            Log::error('Receipt Order Delete Error: ' . $e->getMessage());
            return redirect()->back()->with('error', 'حدث خطأ في حذف البيانات');
        }
    }
}
