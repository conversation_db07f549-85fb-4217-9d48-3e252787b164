<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use App\Models\ReceiptOrder;
use App\Models\User;
use Mpdf\Mpdf;

class ReceiptOrderPdfController extends Controller
{
    /**
     * Generate PDF for receipt order
     */
    public function generatePdf($id)
    {
        if (Auth::user()->can('manage warehouse')) {
            $user = Auth::user();
            
            try {
                $receiptOrder = ReceiptOrder::with(['vendor', 'warehouse', 'fromWarehouse', 'products.product'])
                    ->where('id', $id)
                    ->where('created_by', $user->creatorId())
                    ->firstOrFail();
                
                // البحث عن المنشئ مع معالجة الأخطاء
                $creator = null;
                if ($receiptOrder->created_by) {
                    $creator = User::find($receiptOrder->created_by);
                }
                
                // إذا لم يوجد المنشئ، استخدم المستخدم الحالي
                if (!$creator) {
                    $creator = $user;
                }

                // إعداد بيانات الشركة
                $companyData = [
                    'name' => \App\Models\Utility::getValByName('company_name') ?: 'اسم الشركة',
                    'address' => \App\Models\Utility::getValByName('company_address') ?: 'عنوان الشركة',
                    'phone' => \App\Models\Utility::getValByName('company_phone') ?: 'هاتف الشركة',
                    'email' => \App\Models\Utility::getValByName('company_email') ?: 'بريد الشركة',
                    'logo' => $this->getCompanyLogo(),
                ];

                // إنشاء محتوى HTML
                $html = view('receipt_order.pdf', compact('receiptOrder', 'creator', 'companyData'))->render();

                // إنشاء PDF باستخدام mPDF
                $mpdf = new Mpdf([
                    'mode' => 'utf-8',
                    'format' => 'A4',
                    'orientation' => 'P',
                    'margin_left' => 15,
                    'margin_right' => 15,
                    'margin_top' => 20,
                    'margin_bottom' => 20,
                    'default_font' => 'Arial',
                    'autoScriptToLang' => true,
                    'autoLangToFont' => true,
                ]);

                // كتابة HTML في PDF
                $mpdf->WriteHTML($html);

                // اسم الملف
                $filename = 'receipt_order_' . $receiptOrder->order_number . '_' . date('Y-m-d') . '.pdf';

                // تحميل PDF
                return response($mpdf->Output($filename, 'D'))
                    ->header('Content-Type', 'application/pdf')
                    ->header('Content-Disposition', 'attachment; filename="' . $filename . '"');
                
            } catch (\Exception $e) {
                \Log::error('Receipt Order PDF Error: ' . $e->getMessage());
                return redirect()->back()->with('error', __('خطأ في إنشاء PDF: ') . $e->getMessage());
            }
        } else {
            return redirect()->back()->with('error', __('Permission denied.'));
        }
    }

    /**
     * Preview PDF in browser
     */
    public function previewPdf($id)
    {
        if (Auth::user()->can('manage warehouse')) {
            $user = Auth::user();
            
            try {
                $receiptOrder = ReceiptOrder::with(['vendor', 'warehouse', 'fromWarehouse', 'products.product'])
                    ->where('id', $id)
                    ->where('created_by', $user->creatorId())
                    ->firstOrFail();
                
                // البحث عن المنشئ مع معالجة الأخطاء
                $creator = null;
                if ($receiptOrder->created_by) {
                    $creator = User::find($receiptOrder->created_by);
                }
                
                // إذا لم يوجد المنشئ، استخدم المستخدم الحالي
                if (!$creator) {
                    $creator = $user;
                }

                // إعداد بيانات الشركة
                $companyData = [
                    'name' => \App\Models\Utility::getValByName('company_name') ?: 'اسم الشركة',
                    'address' => \App\Models\Utility::getValByName('company_address') ?: 'عنوان الشركة',
                    'phone' => \App\Models\Utility::getValByName('company_phone') ?: 'هاتف الشركة',
                    'email' => \App\Models\Utility::getValByName('company_email') ?: 'بريد الشركة',
                    'logo' => $this->getCompanyLogo(),
                ];

                // إنشاء محتوى HTML
                $html = view('receipt_order.pdf', compact('receiptOrder', 'creator', 'companyData'))->render();

                // إنشاء PDF باستخدام mPDF
                $mpdf = new Mpdf([
                    'mode' => 'utf-8',
                    'format' => 'A4',
                    'orientation' => 'P',
                    'margin_left' => 15,
                    'margin_right' => 15,
                    'margin_top' => 20,
                    'margin_bottom' => 20,
                    'default_font' => 'Arial',
                    'autoScriptToLang' => true,
                    'autoLangToFont' => true,
                ]);

                // كتابة HTML في PDF
                $mpdf->WriteHTML($html);

                // عرض PDF في المتصفح
                return response($mpdf->Output('receipt_order_' . $receiptOrder->order_number . '.pdf', 'I'))
                    ->header('Content-Type', 'application/pdf');
                
            } catch (\Exception $e) {
                \Log::error('Receipt Order PDF Preview Error: ' . $e->getMessage());
                return redirect()->back()->with('error', __('خطأ في معاينة PDF: ') . $e->getMessage());
            }
        } else {
            return redirect()->back()->with('error', __('Permission denied.'));
        }
    }

    /**
     * Get company logo path
     */
    private function getCompanyLogo()
    {
        try {
            $logo = \App\Models\Utility::get_file('uploads/logo/');
            $company_logo = \App\Models\Utility::getValByName('company_logo_dark');
            $logoPath = $logo . '/' . (isset($company_logo) && !empty($company_logo) ? $company_logo : 'logo-dark.png');
            
            // التحقق من وجود الملف
            if (file_exists(public_path($logoPath))) {
                return public_path($logoPath);
            }
            
            return null;
        } catch (\Exception $e) {
            \Log::error('Company Logo Error: ' . $e->getMessage());
            return null;
        }
    }
}
