<?php

namespace App\Http\Controllers;

use App\Models\TransactionLines;
use Illuminate\Http\Request;

class TransactionLinesController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        //
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        //
    }

    /**
     * Display the specified resource.
     */
    public function show(TransactionLines $transactionLines)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(TransactionLines $transactionLines)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, TransactionLines $transactionLines)
    {
        //
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(TransactionLines $transactionLines)
    {
        //
    }
}
