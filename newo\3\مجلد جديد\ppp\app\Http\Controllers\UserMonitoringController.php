<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\User;
use App\Models\Shift;
use App\Models\FinancialRecord;
use App\Models\FinancialTransactions;
use App\Models\VoucherReceipt;
use App\Models\Pos;
use App\Models\PosProduct;
use App\Models\warehouse;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;

class UserMonitoringController extends Controller
{
    /**
     * Display the user monitoring dashboard
     */
    public function index(Request $request)
    {
        // Get filter parameters
        $warehouseId = $request->get('warehouse_id');
        $userId = $request->get('user_id');
        $dateFilter = $request->get('date_filter', 'today');
        $paymentMethod = $request->get('payment_method');
        $shiftStatus = $request->get('shift_status');
        
        // Calculate date range based on filter
        $dateRange = $this->getDateRange($dateFilter, $request->get('start_date'), $request->get('end_date'));
        
        // Get users with their monitoring data
        $usersData = $this->getUsersMonitoringData($warehouseId, $userId, $dateRange, $paymentMethod, $shiftStatus);
        
        // Get shifts data
        $shiftsData = $this->getShiftsData($warehouseId, $userId, $dateRange, $shiftStatus);
        
        // Get financial transactions data
        $transactionsData = $this->getFinancialTransactionsData($warehouseId, $userId, $dateRange, $paymentMethod);
        
        // Get POS sales data
        $posData = $this->getPosData($warehouseId, $userId, $dateRange, $paymentMethod);
        
        // Get summary statistics
        $summaryStats = $this->getSummaryStatistics($dateRange, $warehouseId, $userId);
        
        // Get warehouses and users for filters
        $warehouses = warehouse::all();
        $users = User::all();
        
        return view('user-monitoring.index', compact(
            'usersData',
            'shiftsData', 
            'transactionsData',
            'posData',
            'summaryStats',
            'warehouses',
            'users',
            'warehouseId',
            'userId',
            'dateFilter',
            'paymentMethod',
            'shiftStatus'
        ));
    }
    
    /**
     * Get date range based on filter
     */
    private function getDateRange($dateFilter, $startDate = null, $endDate = null)
    {
        $today = Carbon::today();
        
        switch ($dateFilter) {
            case 'today':
                return ['start' => $today, 'end' => $today];
            case 'yesterday':
                $yesterday = Carbon::yesterday();
                return ['start' => $yesterday, 'end' => $yesterday];
            case 'last3days':
                return ['start' => $today->copy()->subDays(2), 'end' => $today];
            case 'last7days':
                return ['start' => $today->copy()->subDays(6), 'end' => $today];
            case 'last30days':
                return ['start' => $today->copy()->subDays(29), 'end' => $today];
            case 'custom':
                return [
                    'start' => $startDate ? Carbon::parse($startDate) : $today,
                    'end' => $endDate ? Carbon::parse($endDate) : $today
                ];
            default:
                return ['start' => $today, 'end' => $today];
        }
    }
    
    /**
     * Get users monitoring data
     */
    private function getUsersMonitoringData($warehouseId, $userId, $dateRange, $paymentMethod, $shiftStatus)
    {
        $query = User::with(['shifts' => function($q) use ($dateRange, $warehouseId, $shiftStatus) {
            $q->whereBetween('created_at', [$dateRange['start'], $dateRange['end']->endOfDay()]);
            if ($warehouseId) {
                $q->where('warehouse_id', $warehouseId);
            }
            if ($shiftStatus === 'open') {
                $q->where('is_closed', false);
            } elseif ($shiftStatus === 'closed') {
                $q->where('is_closed', true);
            }
        }]);
        
        if ($userId) {
            $query->where('id', $userId);
        }
        
        if ($warehouseId) {
            $query->whereHas('shifts', function($q) use ($warehouseId) {
                $q->where('warehouse_id', $warehouseId);
            });
        }
        
        $users = $query->get();
        
        $usersData = [];
        foreach ($users as $user) {
            $userData = [
                'user' => $user,
                'warehouse' => null,
                'current_shift' => null,
                'shift_status' => 'مغلق',
                'current_cash' => 0,
                'current_network' => 0,
                'total_sales' => 0,
                'total_network_sales' => 0,
                'transactions_count' => 0,
                'avg_invoice_value' => 0
            ];
            
            // Get current active shift
            $currentShift = $user->shifts()
                ->where('is_closed', false)
                ->when($warehouseId, function($q) use ($warehouseId) {
                    return $q->where('warehouse_id', $warehouseId);
                })
                ->first();
                
            if ($currentShift) {
                $userData['current_shift'] = $currentShift;
                $userData['shift_status'] = 'مفتوح';
                $userData['warehouse'] = $currentShift->warehouse;
                
                // Get financial record for this shift
                $financialRecord = FinancialRecord::where('shift_id', $currentShift->id)->first();
                if ($financialRecord) {
                    $userData['current_cash'] = $financialRecord->current_cash;
                    $userData['current_network'] = $financialRecord->overnetwork_cash;
                }
            }
            
            // Get sales data for the date range
            $salesData = $this->getUserSalesData($user->id, $dateRange, $warehouseId, $paymentMethod);
            $userData['total_sales'] = $salesData['total_sales'];
            $userData['total_network_sales'] = $salesData['network_sales'];
            $userData['transactions_count'] = $salesData['transactions_count'];
            $userData['avg_invoice_value'] = $salesData['avg_invoice_value'];
            
            $usersData[] = $userData;
        }
        
        return $usersData;
    }
    
    /**
     * Get user sales data
     */
    private function getUserSalesData($userId, $dateRange, $warehouseId, $paymentMethod)
    {
        $query = Pos::where('created_by', $userId)
            ->whereBetween('created_at', [$dateRange['start'], $dateRange['end']->endOfDay()]);

        if ($warehouseId) {
            $query->where('warehouse_id', $warehouseId);
        }

        $sales = $query->get();

        $totalSales = 0;
        $networkSales = 0;
        $transactionsCount = $sales->count();

        foreach ($sales as $sale) {
            // Get total from POS products
            $posProducts = PosProduct::where('pos_id', $sale->id)->get();
            $saleTotal = 0;

            foreach ($posProducts as $product) {
                $saleTotal += ($product->price * $product->quantity);
            }

            $totalSales += $saleTotal;

            // For classic POS, we'll assume network sales based on payment method
            // You can modify this based on your actual payment structure
            if ($sale->payment_method == 'network' || $sale->payment_method == 'bank_transfer') {
                $networkSales += $saleTotal;
            }
        }

        $avgInvoiceValue = $transactionsCount > 0 ? $totalSales / $transactionsCount : 0;

        return [
            'total_sales' => $totalSales,
            'network_sales' => $networkSales,
            'transactions_count' => $transactionsCount,
            'avg_invoice_value' => $avgInvoiceValue
        ];
    }
    
    /**
     * Get shifts data
     */
    private function getShiftsData($warehouseId, $userId, $dateRange, $shiftStatus)
    {
        $query = Shift::with(['creator', 'warehouse', 'financialRecord'])
            ->whereBetween('created_at', [$dateRange['start'], $dateRange['end']->endOfDay()]);
            
        if ($warehouseId) {
            $query->where('warehouse_id', $warehouseId);
        }
        
        if ($userId) {
            $query->where('created_by', $userId);
        }
        
        if ($shiftStatus === 'open') {
            $query->where('is_closed', false);
        } elseif ($shiftStatus === 'closed') {
            $query->where('is_closed', true);
        }
        
        return $query->orderBy('created_at', 'desc')->get();
    }
    
    /**
     * Get financial transactions data
     */
    private function getFinancialTransactionsData($warehouseId, $userId, $dateRange, $paymentMethod)
    {
        $query = FinancialTransactions::with(['creator', 'shift.warehouse'])
            ->whereBetween('created_at', [$dateRange['start'], $dateRange['end']->endOfDay()]);
            
        if ($userId) {
            $query->where('created_by', $userId);
        }
        
        if ($warehouseId) {
            $query->whereHas('shift', function($q) use ($warehouseId) {
                $q->where('warehouse_id', $warehouseId);
            });
        }
        
        if ($paymentMethod) {
            $query->where('payment_method', $paymentMethod);
        }
        
        return $query->orderBy('created_at', 'desc')->get();
    }
    
    /**
     * Get POS data
     */
    private function getPosData($warehouseId, $userId, $dateRange, $paymentMethod)
    {
        $query = Pos::with(['customer', 'createdBy', 'warehouse'])
            ->whereBetween('created_at', [$dateRange['start'], $dateRange['end']->endOfDay()]);

        if ($warehouseId) {
            $query->where('warehouse_id', $warehouseId);
        }

        if ($userId) {
            $query->where('created_by', $userId);
        }

        if ($paymentMethod) {
            if ($paymentMethod === 'cash') {
                $query->where('payment_method', 'cash');
            } elseif ($paymentMethod === 'network') {
                $query->whereIn('payment_method', ['network', 'bank_transfer']);
            }
        }

        return $query->orderBy('created_at', 'desc')->get();
    }
    
    /**
     * Get summary statistics
     */
    private function getSummaryStatistics($dateRange, $warehouseId, $userId)
    {
        // Total cash and network amounts
        $totalCash = FinancialRecord::when($warehouseId, function($q) use ($warehouseId) {
                return $q->whereHas('shift', function($sq) use ($warehouseId) {
                    $sq->where('warehouse_id', $warehouseId);
                });
            })
            ->when($userId, function($q) use ($userId) {
                return $q->whereHas('shift', function($sq) use ($userId) {
                    $sq->where('created_by', $userId);
                });
            })
            ->sum('current_cash');
            
        $totalNetwork = FinancialRecord::when($warehouseId, function($q) use ($warehouseId) {
                return $q->whereHas('shift', function($sq) use ($warehouseId) {
                    $sq->where('warehouse_id', $warehouseId);
                });
            })
            ->when($userId, function($q) use ($userId) {
                return $q->whereHas('shift', function($sq) use ($userId) {
                    $sq->where('created_by', $userId);
                });
            })
            ->sum('overnetwork_cash');
        
        // Total sales - calculate from POS products
        $posQuery = Pos::whereBetween('created_at', [$dateRange['start'], $dateRange['end']->endOfDay()])
            ->when($warehouseId, function($q) use ($warehouseId) {
                return $q->where('warehouse_id', $warehouseId);
            })
            ->when($userId, function($q) use ($userId) {
                return $q->where('created_by', $userId);
            });

        $totalSales = 0;
        $posRecords = $posQuery->get();

        foreach ($posRecords as $pos) {
            $posProducts = PosProduct::where('pos_id', $pos->id)->get();
            foreach ($posProducts as $product) {
                $totalSales += ($product->price * $product->quantity);
            }
        }

        // Transactions count
        $transactionsCount = $posRecords->count();
        
        // Open shifts count
        $openShiftsCount = Shift::where('is_closed', false)
            ->when($warehouseId, function($q) use ($warehouseId) {
                return $q->where('warehouse_id', $warehouseId);
            })
            ->when($userId, function($q) use ($userId) {
                return $q->where('created_by', $userId);
            })
            ->count();
        
        $avgInvoiceValue = $transactionsCount > 0 ? $totalSales / $transactionsCount : 0;
        $cashPercentage = ($totalCash + $totalNetwork) > 0 ? ($totalCash / ($totalCash + $totalNetwork)) * 100 : 0;
        $networkPercentage = 100 - $cashPercentage;
        
        return [
            'total_cash' => $totalCash,
            'total_network' => $totalNetwork,
            'total_sales' => $totalSales,
            'transactions_count' => $transactionsCount,
            'open_shifts_count' => $openShiftsCount,
            'avg_invoice_value' => $avgInvoiceValue,
            'cash_percentage' => $cashPercentage,
            'network_percentage' => $networkPercentage
        ];
    }
}
