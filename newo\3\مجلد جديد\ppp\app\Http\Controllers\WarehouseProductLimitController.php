<?php

namespace App\Http\Controllers;

use App\Models\ProductService;
use App\Models\ProductServiceCategory;
use App\Models\ProductServiceUnit;
use App\Models\Purchase;
use App\Models\PurchaseProduct;
use App\Models\StockReport;
use App\Models\Utility;
use App\Models\Vender;
use App\Models\warehouse;
use App\Models\WarehouseProduct;
use App\Models\WarehouseProductLimit;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;

class WarehouseProductLimitController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index(Request $request)
    {
        // التحقق من الصلاحيات
        if (!Auth::user()->can('manage product & service')) {
            return redirect()->back()->with('error', __('Permission denied.'));
        }

        $warehouses = warehouse::where('created_by', '=', Auth::user()->creatorId())->get();
        // استرجاع فقط فئات المنتجات والخدمات (Product & Service)
        $categories = ProductServiceCategory::where('created_by', '=', Auth::user()->creatorId())
            ->where(function($query) {
                $query->where('type', 'product & service')
                      ->orWhere('type', 'Product & Service');
            })
            ->get();
        $vendors = Vender::where('created_by', '=', Auth::user()->creatorId())->get();

        $warehouse_id = $request->input('warehouse_id', '');
        $category_id = $request->input('category_id', '');
        $vendor_id = $request->input('vendor_id', '');

        // استرجاع المنتجات حسب المعايير المحددة
        $query = ProductService::with(['category', 'unit'])
            ->where('created_by', '=', Auth::user()->creatorId())
            ->where('type', '=', 'product');

        if (!empty($category_id)) {
            $query->where('category_id', $category_id);
        }

        if (!empty($vendor_id)) {
            // Get products from the last purchase of this vendor
            $productIds = PurchaseProduct::whereHas('purchase', function ($q) use ($vendor_id) {
                $q->where('vender_id', $vendor_id);
            })->pluck('product_id')->unique()->toArray();

            $query->whereIn('id', $productIds);
        }

        $products = $query->get();

        // استرجاع جميع المستودعات للمستخدم الحالي
        $allWarehouses = $warehouses;

        // استرجاع كميات المنتجات في كل مستودع - استخدام طريقة أبسط وأكثر مباشرة
        // استخدام مصفوفة لتخزين كميات المنتجات في كل مستودع
        $warehouseProductsData = [];

        // استرجاع جميع سجلات warehouse_products باستخدام الموديل مباشرة
        $warehouseProductsRecords = WarehouseProduct::where('created_by', Auth::user()->creatorId())->get();

        // تحويل البيانات إلى مصفوفة ثنائية الأبعاد للوصول السريع
        foreach ($warehouseProductsRecords as $record) {
            $productId = $record->product_id;
            $warehouseId = $record->warehouse_id;
            $quantity = $record->quantity;

            if (!isset($warehouseProductsData[$productId])) {
                $warehouseProductsData[$productId] = [];
            }

            $warehouseProductsData[$productId][$warehouseId] = $quantity;
        }

        // طباعة معلومات التصحيح
        \Log::info('Total warehouse product records: ' . $warehouseProductsRecords->count());
        \Log::info('Total products with warehouse data: ' . count($warehouseProductsData));

        // تجهيز البيانات للعرض
        $warehouseProducts = collect();

        // Get product limits
        $productLimits = WarehouseProductLimit::where('created_by', Auth::user()->creatorId())
            ->get()
            ->keyBy(function ($item) {
                return $item->warehouse_id . '-' . $item->product_id;
            });

        // إعداد البيانات لكل منتج
        foreach ($products as $product) {
            $productData = new \stdClass();
            $productData->id = $product->id;
            $productData->name = $product->name;
            $productData->category = $product->category;
            $productData->unit = $product->unit;
            $productData->warehouse_quantities = [];

            // الحصول على آخر عملية شراء للمنتج
            $lastPurchase = PurchaseProduct::select('purchase_products.*', 'purchases.purchase_date', 'purchases.vender_id', 'purchases.warehouse_id')
                ->join('purchases', 'purchase_products.purchase_id', '=', 'purchases.id')
                ->where('purchase_products.product_id', $product->id)
                ->where('purchases.created_by', Auth::user()->creatorId())
                ->orderBy('purchases.purchase_date', 'desc')
                ->first();

            $productData->last_purchase_date = $lastPurchase ? $lastPurchase->purchase_date : null;
            $productData->vendor_id = $lastPurchase ? $lastPurchase->vender_id : null;
            $productData->vendor_name = $lastPurchase ? optional(Vender::find($lastPurchase->vender_id))->name : null;
            $productData->last_warehouse_id = $lastPurchase ? $lastPurchase->warehouse_id : null;

            // حساب معدل دوران المخزون (آخر 30 يوم)
            $startDate = Carbon::now()->subDays(30);
            $endDate = Carbon::now();

            // إجمالي الكمية المباعة في آخر 30 يوم
            $totalSold = StockReport::where('product_id', $product->id)
                ->where('created_by', Auth::user()->creatorId())
                ->where('type', 'pos')
                ->where('created_at', '>=', $startDate)
                ->where('created_at', '<=', $endDate)
                ->sum('quantity');

            // إضافة كميات المنتج في كل مستودع - تبسيط طريقة معالجة البيانات
            $totalQuantity = 0;
            $productData->warehouse_quantities = [];

            // تهيئة كميات المنتج في كل مستودع بقيمة 0
            foreach ($allWarehouses as $warehouse) {
                $productData->warehouse_quantities[$warehouse->id] = 0;
            }

            // استرجاع كميات المنتج في كل مستودع مباشرة من قاعدة البيانات
            $productWarehouseRecords = WarehouseProduct::where('product_id', $product->id)
                ->where('created_by', Auth::user()->creatorId())
                ->get();

            // إضافة الكميات من السجلات المسترجعة
            foreach ($productWarehouseRecords as $record) {
                $warehouseId = $record->warehouse_id;
                $quantity = $record->quantity;

                // تخزين الكمية في المصفوفة
                $productData->warehouse_quantities[$warehouseId] = $quantity;

                // إضافة الكمية إلى الإجمالي
                $totalQuantity += $quantity;
            }

            // طباعة معلومات التصحيح
            \Log::info("Product {$product->id} total quantity: {$totalQuantity}");

            $productData->total_quantity = $totalQuantity;

            // حساب معدل دوران المخزون
            $avgInventory = $totalQuantity > 0 ? $totalQuantity : 1;
            $productData->turnover_rate = $avgInventory > 0 ? abs($totalSold) / $avgInventory : 0;

            // حساب مدة بقاء المخزون (الأيام المتبقية حتى نفاد المخزون)
            $dailyConsumption = abs($totalSold) / 30; // متوسط الاستهلاك اليومي
            $productData->stock_duration = $dailyConsumption > 0 ? round($totalQuantity / $dailyConsumption) : null;

            // الحصول على حدود المخزون
            if ($lastPurchase) {
                $limitKey = $lastPurchase->warehouse_id . '-' . $product->id;
                $productData->min_quantity = isset($productLimits[$limitKey]) ? $productLimits[$limitKey]->min_quantity : 0;
                $productData->alert_threshold = isset($productLimits[$limitKey]) ? $productLimits[$limitKey]->alert_threshold : 0;
                $productData->limit_warehouse_id = $lastPurchase->warehouse_id;
            } else {
                $productData->min_quantity = 0;
                $productData->alert_threshold = 0;
                $productData->limit_warehouse_id = null;
            }

            $warehouseProducts->push($productData);
        }

        return view('warehouse-product-limit.index', compact(
            'warehouseProducts',
            'warehouses',
            'categories',
            'vendors',
            'warehouse_id',
            'category_id',
            'vendor_id',
            'allWarehouses'
        ));
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        // التحقق من الصلاحيات
        if (!Auth::user()->can('manage product & service')) {
            return response()->json(['error' => __('Permission denied.')], 401);
        }

        $warehouses = warehouse::where('created_by', '=', Auth::user()->creatorId())->pluck('name', 'id');
        $products = ProductService::where('created_by', '=', Auth::user()->creatorId())
            ->where('type', '=', 'product')
            ->pluck('name', 'id');

        return view('warehouse-product-limit.create', compact('warehouses', 'products'));
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        // التحقق من أن المستخدم لديه دور company أو accountant
        if (!(Auth::user()->type == 'company' || Auth::user()->type == 'accountant')) {
            return redirect()->back()->with('error', __('Permission denied.'));
        }

        // التحقق من الصلاحيات
        if (!Auth::user()->can('manage product & service')) {
            return redirect()->back()->with('error', __('Permission denied.'));
        }

        $validator = \Validator::make(
            $request->all(), [
                'warehouse_id' => 'required',
                'product_id' => 'required',
                'min_quantity' => 'required|numeric|min:0',
                'alert_threshold' => 'required|numeric|min:0',
            ]
        );

        if ($validator->fails()) {
            $messages = $validator->getMessageBag();
            return redirect()->back()->with('error', $messages->first());
        }

        $limit = WarehouseProductLimit::updateOrCreate(
            [
                'warehouse_id' => $request->warehouse_id,
                'product_id' => $request->product_id,
                'created_by' => Auth::user()->creatorId(),
            ],
            [
                'min_quantity' => $request->min_quantity,
                'alert_threshold' => $request->alert_threshold,
            ]
        );

        return redirect()->route('warehouse-product-limit.index')->with('success', __('Product limit successfully saved.'));
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function edit($id)
    {
        // التحقق من الصلاحيات
        if (!Auth::user()->can('manage product & service')) {
            return response()->json(['error' => __('Permission denied.')], 401);
        }

        $warehouseProductLimit = WarehouseProductLimit::find($id);
        if ($warehouseProductLimit->created_by != Auth::user()->creatorId()) {
            return response()->json(['error' => __('Permission denied.')], 401);
        }

        $warehouses = warehouse::where('created_by', '=', Auth::user()->creatorId())->pluck('name', 'id');
        $products = ProductService::where('created_by', '=', Auth::user()->creatorId())
            ->where('type', '=', 'product')
            ->pluck('name', 'id');

        return view('warehouse-product-limit.edit', compact('warehouseProductLimit', 'warehouses', 'products'));
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, $id)
    {
        // التحقق من الصلاحيات
        if (!Auth::user()->can('manage product & service')) {
            return redirect()->back()->with('error', __('Permission denied.'));
        }

        $validator = \Validator::make(
            $request->all(), [
                'min_quantity' => 'required|numeric|min:0',
                'alert_threshold' => 'required|numeric|min:0',
            ]
        );

        if ($validator->fails()) {
            $messages = $validator->getMessageBag();
            return redirect()->back()->with('error', $messages->first());
        }

        $limit = WarehouseProductLimit::find($id);
        if ($limit->created_by == Auth::user()->creatorId()) {
            $limit->min_quantity = $request->min_quantity;
            $limit->alert_threshold = $request->alert_threshold;
            $limit->save();
            return redirect()->route('warehouse-product-limit.index')->with('success', __('Product limit successfully updated.'));
        } else {
            return redirect()->back()->with('error', __('Permission denied.'));
        }
    }

    /**
     * Update multiple product limits via AJAX.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function updateBulk(Request $request)
    {
        // التحقق من الصلاحيات
        if (!Auth::user()->can('manage product & service')) {
            return response()->json(['error' => __('Permission denied.')], 403);
        }

        $warehouse_id = $request->warehouse_id;
        $product_id = $request->product_id;
        $min_quantity = $request->min_quantity;
        $alert_threshold = $request->alert_threshold;

        $limit = WarehouseProductLimit::updateOrCreate(
            [
                'warehouse_id' => $warehouse_id,
                'product_id' => $product_id,
                'created_by' => Auth::user()->creatorId(),
            ],
            [
                'min_quantity' => $min_quantity,
                'alert_threshold' => $alert_threshold,
            ]
        );

        return response()->json(['success' => __('Product limit successfully updated.')]);
    }
}
