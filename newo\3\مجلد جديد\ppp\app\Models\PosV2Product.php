<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class PosV2Product extends Model
{
    protected $table = 'pos_v2_products';
    
    protected $fillable = [
        'product_id',
        'pos_id',
        'quantity',
        'tax',
        'discount',
        'price',
        'total',
        'total_discount',
        'description',
    ];

    public function product()
    {
        return $this->hasOne('App\Models\ProductService', 'id', 'product_id');
    }

    public function posV2()
    {
        return $this->belongsTo('App\Models\PosV2', 'pos_id', 'id');
    }

    /**
     * Get the product directly
     */
    public function getProduct()
    {
        return ProductService::find($this->product_id);
    }
}
