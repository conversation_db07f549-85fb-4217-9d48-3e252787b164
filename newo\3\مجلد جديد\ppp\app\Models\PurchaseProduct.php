<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class PurchaseProduct extends Model
{
    protected $fillable = [
        'product_id',
        'purchase_id',
        'quantity',
        'price',
        'tax',
        'discount',
        'total',
        'description',
    ];

    public function product()
    {
        return $this->hasOne('App\Models\ProductService', 'id', 'product_id');
    }

    public function purchase()
    {
        return $this->belongsTo('App\Models\Purchase', 'purchase_id', 'id');
    }
}
