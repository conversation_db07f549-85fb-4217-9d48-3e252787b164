<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class Shift extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'shift_opening_balance',
        'is_closed',
        'opened_at',
        'closed_at',
        'closed_by',
        'created_by',
        'updated_by',
        'warehouse_id'
    ];

    protected $casts = [
        'is_closed' => 'boolean',
        'opened_at' => 'datetime',
        'closed_at' => 'datetime',
    ];

    /**
     * Get the user who created the shift.
     */
    public function creator()
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    /**
     * Get the user who updated the shift.
     */
    public function updater()
    {
        return $this->belongsTo(User::class, 'updated_by');
    }

    /**
     * Get the user who created the shift.
     */
    public function closer()
    {
        return $this->belongsTo(User::class, 'closed_by');
    }






    /**
     * Scope to get only open shifts.
     */
    public function scopeOpen($query)
    {
        return $query->where('is_closed', false);
    }

    /**
     * Scope to get only closed shifts.
     */
    public function scopeClosed($query)
    {
        return $query->where('is_closed', true);
    }

    /**
     * Close the shift and set the closing timestamp.
     */
    public function closeShift()
    {
        $this->update([
            'is_closed' => true,
            'closed_at' => now(),
        ]);
    }

    public function financialRecord()
    {
        return $this->hasOne(FinancialRecord::class, 'shift_id', 'id');
    }

    public function pos()
    {
        return $this->hasMany(Pos::class, 'shift_id');
    }

    public function purchases()
    {
        return $this->hasMany(Purchase::class,'shift_id');
    }

    /**
     * Get the warehouse associated with the shift.
     */
    public function warehouse()
    {
        return $this->belongsTo(\App\Models\warehouse::class, 'warehouse_id');
    }
}
