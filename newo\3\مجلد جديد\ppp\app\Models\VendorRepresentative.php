<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class VendorRepresentative extends Model
{
    protected $fillable = [
        'name',
        'phone',
        'vendor_id',
        'category_id',
        'is_active',
        'notes',
        'created_by',
    ];

    protected $casts = [
        'is_active' => 'boolean',
    ];

    /**
     * Get the vendor that owns the representative.
     */
    public function vendor(): BelongsTo
    {
        return $this->belongsTo(Vender::class, 'vendor_id');
    }

    /**
     * Get the category that owns the representative.
     */
    public function category(): BelongsTo
    {
        return $this->belongsTo(ProductServiceCategory::class, 'category_id');
    }

    /**
     * Get the user who created this representative.
     */
    public function creator(): BelongsTo
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    /**
     * Scope a query to only include active representatives.
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope a query to only include inactive representatives.
     */
    public function scopeInactive($query)
    {
        return $query->where('is_active', false);
    }

    /**
     * Scope a query to filter by vendor.
     */
    public function scopeByVendor($query, $vendorId)
    {
        return $query->where('vendor_id', $vendorId);
    }

    /**
     * Scope a query to filter by category.
     */
    public function scopeByCategory($query, $categoryId)
    {
        return $query->where('category_id', $categoryId);
    }

    /**
     * Get formatted phone number.
     */
    public function getFormattedPhoneAttribute()
    {
        return $this->phone;
    }

    /**
     * Get status badge HTML.
     */
    public function getStatusBadgeAttribute()
    {
        if ($this->is_active) {
            return '<span class="badge bg-success">نشط</span>';
        } else {
            return '<span class="badge bg-danger">غير نشط</span>';
        }
    }
}
