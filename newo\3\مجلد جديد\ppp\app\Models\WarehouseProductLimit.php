<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class WarehouseProductLimit extends Model
{
    use HasFactory;

    protected $fillable = [
        'warehouse_id',
        'product_id',
        'min_quantity',
        'alert_threshold',
        'created_by',
    ];

    /**
     * Get the warehouse that owns the limit.
     */
    public function warehouse()
    {
        return $this->belongsTo(warehouse::class, 'warehouse_id');
    }

    /**
     * Get the product that owns the limit.
     */
    public function product()
    {
        return $this->belongsTo(ProductService::class, 'product_id');
    }
}
