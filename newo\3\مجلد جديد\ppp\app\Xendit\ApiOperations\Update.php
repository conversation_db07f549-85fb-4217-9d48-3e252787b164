<?php

/**
 * Update.php
 * php version 7.2.0
 *
 * @category Trait
 * @package  Xendit
 * <AUTHOR> <<EMAIL>>
 * @license  https://opensource.org/licenses/MIT MIT License
 * @link     https://api.xendit.co
 */

namespace App\Xendit\ApiOperations;

/**
 * Trait Update
 *
 * @category Trait
 * @package  Xendit\ApiOperations
 * <AUTHOR> <<EMAIL>>
 * @license  https://opensource.org/licenses/MIT MIT License
 * @link     https://api.xendit.co
 */
trait Update
{
    /**
     * Send an update request
     *
     * @param string $id     data ID
     * @param array  $params user's params
     *
     * @return array
     */
    public static function update($id, $params = [])
    {
        self::validateParams($params, static::updateReqParams());

        $url = static::classUrl() . '/' . $id;

        return static::_request('PATCH', $url, $params);
    }
}
