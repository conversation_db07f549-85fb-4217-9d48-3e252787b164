<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

class AddAllowNegativeStockSetting extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        // إضافة إعداد للسماح بالكميات السالبة
        DB::table('settings')->insert([
            'name' => 'allow_negative_stock',
            'value' => 'on', // تفعيل البيع على الكميات السالبة افتراضياً
            'created_by' => 1,
            'created_at' => now(),
            'updated_at' => now(),
        ]);
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        // حذف الإعداد
        DB::table('settings')->where('name', 'allow_negative_stock')->delete();
    }
}
