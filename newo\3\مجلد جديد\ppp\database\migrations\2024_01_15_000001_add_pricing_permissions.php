<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        try {
            // إنشاء الصلاحيات الخاصة بالتسعير مباشرة في قاعدة البيانات
            $permissions = [
                [
                    "name" => "manage pricing",
                    "guard_name" => "web",
                    "created_at" => now(),
                    "updated_at" => now(),
                ],
                [
                    "name" => "show pricing", 
                    "guard_name" => "web",
                    "created_at" => now(),
                    "updated_at" => now(),
                ],
                [
                    "name" => "edit pricing",
                    "guard_name" => "web", 
                    "created_at" => now(),
                    "updated_at" => now(),
                ],
                [
                    "name" => "manage inventory",
                    "guard_name" => "web",
                    "created_at" => now(), 
                    "updated_at" => now(),
                ],
            ];

            // إدراج الصلاحيات إذا لم تكن موجودة
            foreach ($permissions as $permission) {
                $exists = DB::table('permissions')
                    ->where('name', $permission['name'])
                    ->where('guard_name', $permission['guard_name'])
                    ->exists();
                
                if (!$exists) {
                    DB::table('permissions')->insert($permission);
                }
            }

            \Log::info('Pricing permissions created successfully');

        } catch (\Exception $e) {
            \Log::error('Error creating pricing permissions: ' . $e->getMessage());
            // لا نرمي الخطأ لتجنب فشل الهجرة
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        try {
            // حذف الصلاحيات
            $permissionNames = ['manage pricing', 'show pricing', 'edit pricing', 'manage inventory'];
            
            DB::table('permissions')
                ->whereIn('name', $permissionNames)
                ->where('guard_name', 'web')
                ->delete();

            \Log::info('Pricing permissions removed successfully');

        } catch (\Exception $e) {
            \Log::error('Error removing pricing permissions: ' . $e->getMessage());
        }
    }
};
