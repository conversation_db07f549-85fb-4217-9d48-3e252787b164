<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        if (!Schema::hasColumn('product_services', 'expiry_date')) {
            Schema::table('product_services', function (Blueprint $table) {
                $table->date('expiry_date')->nullable()->after('description');
            });
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        if (Schema::hasColumn('product_services', 'expiry_date')) {
            Schema::table('product_services', function (Blueprint $table) {
                $table->dropColumn('expiry_date');
            });
        }
    }
};
