<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddShowInPosToProductServiceCategoriesTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        // First check if the column already exists to avoid errors
        if (!Schema::hasColumn('product_service_categories', 'show_in_pos')) {
            Schema::table('product_service_categories', function (Blueprint $table) {
                // Add show_in_pos field to control visibility on POS screen
                // Default is true (1) to maintain backward compatibility
                $table->boolean('show_in_pos')->default(1)->after('color');
            });
        }
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        // Only drop the column if it exists
        if (Schema::hasColumn('product_service_categories', 'show_in_pos')) {
            Schema::table('product_service_categories', function (Blueprint $table) {
                $table->dropColumn('show_in_pos');
            });
        }
    }
}
