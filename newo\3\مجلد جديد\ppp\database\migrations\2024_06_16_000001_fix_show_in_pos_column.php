<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

class FixShowInPosColumn extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        // Check if the column already exists
        $hasColumn = false;
        try {
            $hasColumn = Schema::hasColumn('product_service_categories', 'show_in_pos');
        } catch (\Exception $e) {
            // If there's an error checking, assume column doesn't exist
            $hasColumn = false;
        }

        // Only add the column if it doesn't exist
        if (!$hasColumn) {
            // Use direct SQL to add the column to ensure it works
            DB::statement('ALTER TABLE `product_service_categories` ADD COLUMN `show_in_pos` TINYINT(1) NOT NULL DEFAULT 1 AFTER `color`');
            
            // Update existing records
            DB::statement("UPDATE `product_service_categories` SET `show_in_pos` = 1 WHERE `type` = 'product & service'");
            DB::statement("UPDATE `product_service_categories` SET `show_in_pos` = 0 WHERE `type` != 'product & service'");
        }
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        // Check if the column exists before trying to drop it
        if (Schema::hasColumn('product_service_categories', 'show_in_pos')) {
            Schema::table('product_service_categories', function (Blueprint $table) {
                $table->dropColumn('show_in_pos');
            });
        }
    }
}
