<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('pos_v2', function (Blueprint $table) {
            $table->bigIncrements('id');
            $table->unsignedBigInteger('pos_id')->default('0');
            $table->unsignedBigInteger('customer_id')->default('0');
            $table->unsignedBigInteger('warehouse_id')->default('0');
            $table->date('pos_date')->nullable();
            $table->integer('category_id')->default('0');
            $table->integer('status')->default('0');
            $table->string('status_type')->default('normal'); // normal, returned, cancelled
            $table->integer('shipping_display')->default('1');
            $table->unsignedBigInteger('created_by')->default('0');
            $table->boolean('is_payment_set')->default(false);
            $table->unsignedBigInteger('user_id')->nullable();
            $table->unsignedBigInteger('shift_id')->nullable();
            $table->timestamps();

            // Foreign key constraints
            $table->foreign('customer_id')->references('id')->on('customers')->onDelete('cascade');
            $table->foreign('warehouse_id')->references('id')->on('warehouses')->onDelete('cascade');
            $table->foreign('user_id')->references('id')->on('users')->onDelete('set null');
            $table->foreign('shift_id')->references('id')->on('shifts')->onDelete('set null');
            $table->foreign('created_by')->references('id')->on('users')->onDelete('cascade');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('pos_v2');
    }
};
