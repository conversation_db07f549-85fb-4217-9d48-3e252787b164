<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('financial_records', function (Blueprint $table) {
            $table->id();
            $table->decimal('opening_balance', 15, 2); // الرصيد أول المدة
            $table->decimal('current_cash', 15, 2)->default(0); // النقد الحالي
            $table->decimal('overnetwork_cash', 15, 2)->default(0); // المبالغ المحصلة عبر الشبكة
            $table->decimal('delivery_cash', 15, 2)->default(0); // النقد لدى طلبات التوصيل
            $table->decimal('total_cash', 15, 2)->default(0); // إجمالي النقد
            $table->decimal('deficit', 15, 2)->nullable()->default(0); // العجز
            $table->decimal('received_advance', 15, 2)->nullable()->default(0); // العهدة المستلمة
            $table->foreignId('shift_id')->constrained('shifts')->cascadeOnDelete();
            $table->foreignId('created_by')->constrained('users')->cascadeOnDelete();
            $table->foreignId('updated_by')->nullable()->constrained('users')->nullOnDelete();

            $table->timestamp('deleted_at')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('financial_records');
    }
};
