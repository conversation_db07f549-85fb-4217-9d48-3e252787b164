<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('pos_payments', function (Blueprint $table) {
            $table->string('payment_type')->default('cash')->after('amount'); // cash, network, split
            $table->decimal('cash_amount', 15, 2)->nullable()->default(0)->after('payment_type');
            $table->decimal('network_amount', 15, 2)->nullable()->default(0)->after('cash_amount');
            $table->string('transaction_number')->nullable()->after('network_amount');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('pos_payments', function (Blueprint $table) {
            $table->dropColumn('payment_type');
            $table->dropColumn('cash_amount');
            $table->dropColumn('network_amount');
            $table->dropColumn('transaction_number');
        });
    }
};
