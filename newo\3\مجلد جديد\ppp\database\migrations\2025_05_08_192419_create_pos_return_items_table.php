<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('pos_return_items', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('pos_return_id');
            $table->unsignedBigInteger('pos_product_id');
            $table->unsignedBigInteger('product_id');
            $table->integer('quantity');
            $table->float('price');
            $table->string('tax')->nullable();
            $table->text('return_reason')->nullable();
            $table->timestamps();

            $table->foreign('pos_return_id')->references('id')->on('pos_returns')->onDelete('cascade');
            $table->foreign('pos_product_id')->references('id')->on('pos_products')->onDelete('cascade');
            $table->foreign('product_id')->references('id')->on('product_services')->onDelete('cascade');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('pos_return_items');
    }
};
