{"__meta": {"id": "X013fd03168e72518bb6f84c6b88c3018", "datetime": "2025-06-17 13:20:50", "utime": **********.486622, "method": "GET", "uri": "/product-categories", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1750166449.875808, "end": **********.486646, "duration": 0.6108379364013672, "duration_str": "611ms", "measures": [{"label": "Booting", "start": 1750166449.875808, "relative_start": 0, "end": **********.358449, "relative_end": **********.358449, "duration": 0.48264098167419434, "duration_str": "483ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.35846, "relative_start": 0.482651948928833, "end": **********.486649, "relative_end": 3.0994415283203125e-06, "duration": 0.1281890869140625, "duration_str": "128ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 49310352, "peak_usage_str": "47MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET product-categories", "middleware": "web, verified, auth, XSS, category.access", "controller": "App\\Http\\Controllers\\ProductServiceCategoryController@getProductCategories", "namespace": null, "prefix": "", "where": [], "as": "product.categories", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FHttp%2FControllers%2FProductServiceCategoryController.php&line=203\" onclick=\"\">app/Http/Controllers/ProductServiceCategoryController.php:203-229</a>"}, "queries": {"nb_statements": 6, "nb_failed_statements": 0, "accumulated_duration": 0.02512, "accumulated_duration_str": "25.12ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 16 limit 1", "type": "query", "params": [], "bindings": ["16"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.403763, "duration": 0.01702, "duration_str": "17.02ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 67.755}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.432922, "duration": 0.0010500000000000002, "duration_str": "1.05ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 67.755, "width_percent": 4.18}, {"sql": "select `permissions`.*, `model_has_permissions`.`model_id` as `pivot_model_id`, `model_has_permissions`.`permission_id` as `pivot_permission_id`, `model_has_permissions`.`model_type` as `pivot_model_type` from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `model_has_permissions`.`model_id` = 16 and `model_has_permissions`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["16", "App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 326}, {"index": 21, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 566}], "start": **********.455797, "duration": 0.0007199999999999999, "duration_str": "720μs", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:326", "source": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php:326", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=326", "ajax": false, "filename": "HasPermissions.php", "line": "326"}, "connection": "kdmkjkqknb", "start_percent": 71.935, "width_percent": 2.866}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (16) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 312}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 26, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}], "start": **********.459156, "duration": 0.00065, "duration_str": "650μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 74.801, "width_percent": 2.588}, {"sql": "select `product_service_categories`.*, COUNT(pu.category_id) product_services from `product_service_categories` left join `product_services` as `pu` on `product_service_categories`.`id` = `pu`.`category_id` where `product_service_categories`.`created_by` = 15 and `product_service_categories`.`type` = 'product & service' group by `product_service_categories`.`id` order by `product_service_categories`.`id` desc", "type": "query", "params": [], "bindings": ["15", "product &amp; service"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/ProductServiceCategory.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Models\\ProductServiceCategory.php", "line": 116}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/ProductServiceCategoryController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Http\\Controllers\\ProductServiceCategoryController.php", "line": 206}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.4662979, "duration": 0.0035800000000000003, "duration_str": "3.58ms", "memory": 0, "memory_str": null, "filename": "ProductServiceCategory.php:116", "source": "app/Models/ProductServiceCategory.php:116", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FModels%2FProductServiceCategory.php&line=116", "ajax": false, "filename": "ProductServiceCategory.php", "line": "116"}, "connection": "kdmkjkqknb", "start_percent": 77.389, "width_percent": 14.252}, {"sql": "select count(*) as aggregate from `product_services` left join `product_service_categories` as `c` on `c`.`id` = `product_services`.`category_id` where `product_services`.`type` = 'product' and `product_services`.`created_by` = 15", "type": "query", "params": [], "bindings": ["product", "15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/ProductServiceCategoryController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Http\\Controllers\\ProductServiceCategoryController.php", "line": 209}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.473965, "duration": 0.0021000000000000003, "duration_str": "2.1ms", "memory": 0, "memory_str": null, "filename": "ProductServiceCategoryController.php:209", "source": "app/Http/Controllers/ProductServiceCategoryController.php:209", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FHttp%2FControllers%2FProductServiceCategoryController.php&line=209", "ajax": false, "filename": "ProductServiceCategoryController.php", "line": "209"}, "connection": "kdmkjkqknb", "start_percent": 91.64, "width_percent": 8.36}]}, "models": {"data": {"App\\Models\\ProductServiceCategory": {"value": 26, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FModels%2FProductServiceCategory.php&line=1", "ajax": false, "filename": "ProductServiceCategory.php", "line": "?"}}, "App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 28, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 1, "messages": [{"message": "[ability => create purchase, result => true, user => 16, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1503542900 data-indent-pad=\"  \"><span class=sf-dump-note>create purchase</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"15 characters\">create purchase</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>16</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1503542900\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.46473, "xdebug_link": null}]}, "session": {"_token": "ixL6BN3l9tyyUscqj0hv99yKIy0daqhD43fAAWcL", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "16"}, "request": {"path_info": "/product-categories", "status_code": "<pre class=sf-dump id=sf-dump-2040872976 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-2040872976\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-1864004122 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1864004122\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1950764121 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1950764121\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1921859024 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">ixL6BN3l9tyyUscqj0hv99yKIy0daqhD43fAAWcL</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1937 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=1pubyvl%7C2%7Cfwu%7C0%7C1993; _clsk=25xk9k%7C1750165707106%7C8%7C1%7Cl.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6ImRkaVFkdnk3Q2trQURsdDB0Q2lHdFE9PSIsInZhbHVlIjoiWHVJOGZmNGJXcUZWWkhkK1VKbWNrblc5V1Qyd3lWSGtTaExuMUVGVTVZVUF5SVBmN0pKcDhWZWRDSVBEa2pZWTZsTWUrQ0ZZT0NDU2NyS1o1SE4rNjRiRFFtN25MMU1SaEpBcmFLMmppazd6SHdjU25WRDBZSGc3ZEpaV1FLZnE1UmRoeUM3MzkwanJVOFVGN1AvbTN2Q0NTLzVXK3VYWWx2VXlvQUVCM2ZQMmRNWW40dTRzV2xOdGRGc3Vic2plSkNKZnMwWG4zMlZjZWdNZFp4bW1lcGl4b0JScTB0clZFZ0lKUlF1NmpDTVdkM3dSS2dQa210OUxtb0UwOFc1MWhtY2lsU042REVCd0pZZmdVa2ZoZ0N6cmlib2FyaGh2d085cWJTditMOUdtRlVyU2NOZ3lvNXhGbXhXalgyNmt5YlBIZzgrenZrVU95RzczMHdkb3MvQWROb1JVN0tlT3I0RkppcUJ0eDMvSXdPYXo4MG1NT0xKVTY2L1pRdjRMdU5sWlh0Mm1Db2JuaFc4a1RodytzMmN0L2krT3ltVkYzZU5pTlZWMUduQnJwSy9tVDRuaVBKQXpkbzFhdnc2RGlKMWVtMXovMHd3azFEWlkrZjFhRGpLSnh6by9jbmtSdnMwTkhIMnBUN3VHOThzcGdEdUxTWS9aWjBoUnM1VFIiLCJtYWMiOiJhMGE3ZjcxNmVlZDRmZWVmNGIxZWE2YzhjYzQ2MzNlODc0NTE2ZjAxYjNmNzRlMDIzNmFmYzk1ZDUwNWY5NzczIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6Ink5akllaTRxd3A4NUw3OXd4RkoxSnc9PSIsInZhbHVlIjoieDV1OUhkU3RHNXpNaDJqSVg3S05nOWV3QzRTd2VFM01TaFBEVU1BdklmTVRPTm9GU1ExZS95bm9SMC9QV0pVUHZpUGxiS3MrRUFDRkN1SjhRYUo3ZFBXMGt0emYrMGxiU1ozQWlGRWxtVzBTc1lYS3ZjSUsrQ2k5T2s0VFZqb0RJenRKOTNXSUhMcktVMXp1cTRhRmNUQXQ3czZaZnpuS3ZYditPeDBrM09yVlhQVHpST2RNUGtVZnBhU25rakRlNGREWDkvK0Y0VzFHeTdveGFDWW1wWC9iMGt3aXlvdGwwbEh5NVNBRUZMTnV1LzcvcVUrZXJvaFBCU2p0UjBLUDRuODRsM0hJQnFWemZ5aWRuZjU1Q2dsV3g4MTN3WStLUFpzcVExSTVoUDIrMDNaVVJIOWtwWmJidjF1UmcrcGYwWE9DYkUwRGZHbkZaa3lqV05JeitMODVGSGF3ZW9mRnR5dnRGclJXVzJXSHliV013OUZlMTV1Qmx6dUZyMXRJQnY1eHQwNG5ZUStUSXRaNDRpNE1lbG1SQkFEQ0lsWFFRaXFaakM2eEpYYktZTTNDcDZJOGtNRXdESXFuY1NJd0JSTUJZYTlXQjJxNDlUbjFSMkc1ZUFXSjNRbExZeTRXNTUrVXZkYVN5OXlnSVUxY3Vabk1MY1RGdElNNytSRlkiLCJtYWMiOiJhMTk0NDQxZTA2ZDY5NTQ4ZTZjOWY0NzA3NTYwNDQxZTY5ZGJjMTAyODE1YWIwZDc3NjJmOTk1ZDkzMGNiNjA0IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1921859024\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-269463893 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">ixL6BN3l9tyyUscqj0hv99yKIy0daqhD43fAAWcL</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">coJ97Z8YyNCeFgF4h0jUL1PrpYBjauguIcJbJbm8</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-269463893\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1896999713 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 17 Jun 2025 13:20:50 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IkowaGQ2T3dmUmcyUWxrM2JCTDdYcmc9PSIsInZhbHVlIjoiMkp0bXp5ZW1MODVyekRTb09KQy9aZnZUc0F0bjRZTURwV0ppTjdGakIzZERKS1M1WXVmS25SMGduVzBsOW5CQWVrMjJ3NmtBa1IydHFON0lKVEJQaEVnVTJ1NU5LeElGL2pZMVNaS2FHY1JySmhBYmFQRGZKbU5SNTlpQlFJOSsxU21QZEFMeGVPVG15WUtrWkNlaUVheXZ4S3dCNWRDbEltZ0RCb24rd1BPOWFvR2xLOENZcWZlSDlOUFlSUHA4ckhHM1I2enNjdXN1dzRrSS93OERsL0ZUQlp1SDRDNEdFWnoxbnN0UGswODgxdXBFMUN1K2g1WmpXWTI5OG4zUllFLzJuV2x1WHhzdFVrc1IrVFdvWVk4a1U0dVBWZkZ1ajZsdzV5ZlUrektPSE82KzJiMWFUT2tTMnFMMVgzdlFCd3p4WWZZNUYzMjBBZHZvRnZuSWMydWtmMUVBbktlWjZQaGhUWFdRZktPVEVrVEVHak5JWjR2RVQ1eHN1Zk9XWVZlUlNtQmJIR3Y4bmZwVlFQWXNGMjhYRG9hZzVuYVpqSzY0L2ZYcks0UVp1WTE3TEZsZ2dRNVZlL0VuWUtjdjd5cmhXZnN6TE5lYmgrRWgyVUc0MWVvUVh3NVFzQXpOZ0hTTXVoMzZIT1hhQ2xONXBIMEhiUk9QdGViZlIrSTIiLCJtYWMiOiI0N2YwN2QwOGU2Nzk0YmZlNWIzZTc3MmMxZTY3MDQ5ZjUzYjg1ZGFlMjljZjkxZmUyNWMwZThiNTRiYjE4NzRmIiwidGFnIjoiIn0%3D; expires=Tue, 17 Jun 2025 15:20:50 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6ImRRSWI2WVZ1amFnZ3hRSUdLT1E4bUE9PSIsInZhbHVlIjoibnRxR0ZCbE9hWVFKOEIwZG9lMU13dTQ2d2gzbFVQaU8rNk5RYlFvY0h4eFNDY05SL2FseTdpZnF3YUQreWhMeXAwVUFuRWZoanZwSzN6UnZSWnpqNHdjWHhoSExKa3k3UDBIcmFybE5tMkZnSXVxTzl1TC9BMUFVVkpDTTVBclpqTURBRDZUUEs3M0dzT2JPc0xocEk2SmN4Z25kdjRkNWFuWEYwV2REalhEYjUwTTBmdG9Gc0F6SDJVME1yallJaXBwdXRtQVhRVjJMYmdheC83OStTZnlvRm1ZYXhCODdweW55TUNwRVpvUHUreE0rcXllc0VEQ0VZR0xsd3Z3RnQ5WEZEZi9VNUliOXg1c3FKaDZsYmlmeWhPaXNLSW42RzNzSnpXbS9QdTgrR0ZkN3VKQkREdEZKdHY4cXYza2h2TENINWFWdGJ4ZjZIZ3dIU0RibjlWRmg2c3oyZXNvQThJb2FZdHhPUXMxY2tBQjdMRzllcDIzczZjTlpLTEpPRW9ucFM4YldjRXBVT0p5dVVGSDZpdU1FT2RmVXhCY1B1VlZ0Z3ZNWU42R0Jvd2s5WWRNd1ZOYnFDZmRka3dVTitJS1hpZEJGc0E4OHlLSHpXdVIzZFRFaW1hbk5oQ05xR0lJZzhUdzdscy9aTVNqZnp0OW5mUHh6R2lLZHdkZmUiLCJtYWMiOiIwZTJhNzg2ODljNmEzNDM5MzAzNTY5YjYwMTRmOWQwZTc4NmNhNTRkNzA2NDA4YzI5MDI4ZDUwZDRkOWQ0ZDBkIiwidGFnIjoiIn0%3D; expires=Tue, 17 Jun 2025 15:20:50 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IkowaGQ2T3dmUmcyUWxrM2JCTDdYcmc9PSIsInZhbHVlIjoiMkp0bXp5ZW1MODVyekRTb09KQy9aZnZUc0F0bjRZTURwV0ppTjdGakIzZERKS1M1WXVmS25SMGduVzBsOW5CQWVrMjJ3NmtBa1IydHFON0lKVEJQaEVnVTJ1NU5LeElGL2pZMVNaS2FHY1JySmhBYmFQRGZKbU5SNTlpQlFJOSsxU21QZEFMeGVPVG15WUtrWkNlaUVheXZ4S3dCNWRDbEltZ0RCb24rd1BPOWFvR2xLOENZcWZlSDlOUFlSUHA4ckhHM1I2enNjdXN1dzRrSS93OERsL0ZUQlp1SDRDNEdFWnoxbnN0UGswODgxdXBFMUN1K2g1WmpXWTI5OG4zUllFLzJuV2x1WHhzdFVrc1IrVFdvWVk4a1U0dVBWZkZ1ajZsdzV5ZlUrektPSE82KzJiMWFUT2tTMnFMMVgzdlFCd3p4WWZZNUYzMjBBZHZvRnZuSWMydWtmMUVBbktlWjZQaGhUWFdRZktPVEVrVEVHak5JWjR2RVQ1eHN1Zk9XWVZlUlNtQmJIR3Y4bmZwVlFQWXNGMjhYRG9hZzVuYVpqSzY0L2ZYcks0UVp1WTE3TEZsZ2dRNVZlL0VuWUtjdjd5cmhXZnN6TE5lYmgrRWgyVUc0MWVvUVh3NVFzQXpOZ0hTTXVoMzZIT1hhQ2xONXBIMEhiUk9QdGViZlIrSTIiLCJtYWMiOiI0N2YwN2QwOGU2Nzk0YmZlNWIzZTc3MmMxZTY3MDQ5ZjUzYjg1ZGFlMjljZjkxZmUyNWMwZThiNTRiYjE4NzRmIiwidGFnIjoiIn0%3D; expires=Tue, 17-Jun-2025 15:20:50 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6ImRRSWI2WVZ1amFnZ3hRSUdLT1E4bUE9PSIsInZhbHVlIjoibnRxR0ZCbE9hWVFKOEIwZG9lMU13dTQ2d2gzbFVQaU8rNk5RYlFvY0h4eFNDY05SL2FseTdpZnF3YUQreWhMeXAwVUFuRWZoanZwSzN6UnZSWnpqNHdjWHhoSExKa3k3UDBIcmFybE5tMkZnSXVxTzl1TC9BMUFVVkpDTTVBclpqTURBRDZUUEs3M0dzT2JPc0xocEk2SmN4Z25kdjRkNWFuWEYwV2REalhEYjUwTTBmdG9Gc0F6SDJVME1yallJaXBwdXRtQVhRVjJMYmdheC83OStTZnlvRm1ZYXhCODdweW55TUNwRVpvUHUreE0rcXllc0VEQ0VZR0xsd3Z3RnQ5WEZEZi9VNUliOXg1c3FKaDZsYmlmeWhPaXNLSW42RzNzSnpXbS9QdTgrR0ZkN3VKQkREdEZKdHY4cXYza2h2TENINWFWdGJ4ZjZIZ3dIU0RibjlWRmg2c3oyZXNvQThJb2FZdHhPUXMxY2tBQjdMRzllcDIzczZjTlpLTEpPRW9ucFM4YldjRXBVT0p5dVVGSDZpdU1FT2RmVXhCY1B1VlZ0Z3ZNWU42R0Jvd2s5WWRNd1ZOYnFDZmRka3dVTitJS1hpZEJGc0E4OHlLSHpXdVIzZFRFaW1hbk5oQ05xR0lJZzhUdzdscy9aTVNqZnp0OW5mUHh6R2lLZHdkZmUiLCJtYWMiOiIwZTJhNzg2ODljNmEzNDM5MzAzNTY5YjYwMTRmOWQwZTc4NmNhNTRkNzA2NDA4YzI5MDI4ZDUwZDRkOWQ0ZDBkIiwidGFnIjoiIn0%3D; expires=Tue, 17-Jun-2025 15:20:50 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1896999713\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1179525168 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">ixL6BN3l9tyyUscqj0hv99yKIy0daqhD43fAAWcL</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>16</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1179525168\", {\"maxDepth\":0})</script>\n"}}