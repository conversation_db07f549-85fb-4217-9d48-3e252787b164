{"__meta": {"id": "X01af3e9b8bd44435499be0a747e940dd", "datetime": "2025-06-17 13:55:02", "utime": **********.126686, "method": "POST", "uri": "/chats/getContacts", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1750168501.522069, "end": **********.12672, "duration": 0.6046509742736816, "duration_str": "605ms", "measures": [{"label": "Booting", "start": 1750168501.522069, "relative_start": 0, "end": **********.023816, "relative_end": **********.023816, "duration": 0.5017471313476562, "duration_str": "502ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.023832, "relative_start": 0.501763105392456, "end": **********.126724, "relative_end": 4.0531158447265625e-06, "duration": 0.10289192199707031, "duration_str": "103ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 46115800, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/getContacts", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getContacts", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "contacts.get", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=333\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:333-390</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.01972, "accumulated_duration_str": "19.72ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 16 limit 1", "type": "query", "params": [], "bindings": ["16"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.0683079, "duration": 0.01743, "duration_str": "17.43ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 88.387}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.102063, "duration": 0.00085, "duration_str": "850μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 88.387, "width_percent": 4.31}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (16) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.113947, "duration": 0.0014399999999999999, "duration_str": "1.44ms", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 92.698, "width_percent": 7.302}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "16", "_previous": "array:1 [\n  \"url\" => \"http://localhost/hrm-dashboard\"\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/getContacts", "status_code": "<pre class=sf-dump id=sf-dump-1619321105 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1619321105\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1301247235 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1301247235\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-70360376 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-70360376\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-2122441880 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"30 characters\">http://localhost/hrm-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1936 characters\">_clck=2gyfy%7C2%7Cfwu%7C0%7C1994; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clsk=1ti4td2%7C1750168386913%7C7%7C1%7Cl.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6ImRRMHJZUjdOYnphbG8ydEVKNmxrVUE9PSIsInZhbHVlIjoiWm8wSkdxTVZwbjRFWVBGeEpLajROYWJ6RURHMzNWRFNKMTFrbFdYZE9QZjF3NEJyN2p5Y3phcHhuQ3JzaG5WQnJiWUdOVXNqdjNyVk5RRzh2dnIxUmxYazFoT2p4YlQ5WVFtS1VpZVlqMnRjbFhVNGRlbW5QM1lSY3kzOGZXN1hKaENuR3JHSFl6VHczclNIT1E5Nmt6ZXV2L2paQ1hYVXMwZlR0V1JBWldOS1NNTnpidnR2SEY2UVNTZlAzd1B3Mk9nQUx2R29ZWjNOTDdWRzRFbWlZUXhZNHpwWlVmdzl0dWZvMDBXWGIzVkp2N3grQXZGM2FsczA4MHlJUGFFWUZKYzl3cG5oMXJwV2tBendsaGZXaUplQjYwOTdPUDJReWpWZGE0MVlDV0RnMHpreW1NVERUS0ZWdWU0QXVNWW9lVjc2WDduV2VpQmtpVEJtOW93cGxGVUx1c24zZGUxVWtzdkc4MktBRkMxczdyQWxZY0lSMVpGWm9oeVVZTC9iUktnWjZFajR4UlkwdHdaMGdnaElTS3pwZTR0WmtwNUxRdVpZWk1xaFZaSytXSnZxcklGWndheGJhcGJaL2FoMlZMTnRJU0REQnE1ZHhxVVhMYnFRRFBnWWk5SXhIVTZKMm55Znlic2hXbVgwclVEcUp6S1BlN1hQVTZla3BGd2UiLCJtYWMiOiIxOWJkN2I3MGQwZTBlM2I4YzYzMzI3NzE4YmE3OGEyMjc5NzMwYjE5NGNhZGEwZTQ2YTRlNzk3YmJmN2RjODc3IiwidGFnIjoiIn0%3D; test_session=eyJpdiI6IkdiZjJQVlNPdVQvTlBob1BZWFdMZWc9PSIsInZhbHVlIjoib0RlOEZlTFA0enZ5Tm9zNTNDbklCbk9sSCsvY3lSUHZJUEtBMVFtbFd1bHQ2VldsVGNnUEFFZFdWdWlGamRYa2MxcmY3ZytCM01yaEhhaFExM3kxQWQzcDFoc2pIelB5ZUx3WGhTb1NJY3ovRUR6OU8zM0ttSkxQamVBQ1FMNnZ1aXFVTElPYjBoQUpYdDlmZlFtdG9zNXE3aVpIYk5RTmd3dmh3ZENsWVFYa0JPY2tEYTFhVjhIeW5DcjVsRWFXanMzcTRIN2pyUmZBSUd5aU1yWU5xRFFIbFhkTFBHeXhORGJoZURRZDJXeXJnZTYwWjVWOVVIMlk5Nlh5YklQYk5kejllSDlxODh5YVhZRzlUYWQzZVZBRGVyK2F0dFVNMXduOWRaa1duSWZWVnRTUGRZNERsdUZtZmx5RGV5cVp0eGpFdzhnT3F2ektHcm5rbjBITHhzVHRqVU9jdU14QUQ0QWR0cFFraCsrRmJTdmpBOW03VmNvRUV2L29QWW1nOW1kS3dVd0ExTHFHcjhNMk1YdWdNaUFKQUtTRHhtaDRvaUF2Y3dsbHVCSjZLVVBhcW9HR3BQN3dpbytKRVpEcHpuS3d2Q2s2K0M1ajFlVitVaUtOeGZ1NHVUcW9GaWdzTlVFRkNsMElSUm00M29abDdvWW5mQXN0ajVmVVRydlUiLCJtYWMiOiIwMDUyZTU3NDFhMjgzYzYyYjhjNWY5MTY5NjgyZDBkMTQ1MWQ2OTcxZDBjM2Q0ZDU4NjhiOGFmNzk1MmUzZjM5IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2122441880\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-594559426 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">VloaUQCBjpDKrel7nHeAzB1sTLdTMwteSos1MmAN</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-594559426\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-582388836 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 17 Jun 2025 13:55:02 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IlJPaXZNU0YrZ0NIK3NNNkhVbHM5MkE9PSIsInZhbHVlIjoiZEE0ZmtRZjEwTzJaRHdmMUp0aWZneWtJa1lqaG5VTEZ0eXI3TnJmaTk2NTdPUEY3ZGxyMDQvZ0d5c2pWaUlMd2oyRlhrUENsU0M5Z05NWlMwU3JjQlZLcGs2N1cybUh2ck5uNnFELzg4anJNclpXWEdKZ3dwQXRFMGZKU3JGeGNJd1VuZWQ4Undjbm1KVFZ4eC91UnROR0Jjd3J1bjlONmJiUUNJSDhmU3NmeFlYektXNWo1dU8rR2ZYVkthWTFZVHdlYUF2VkRBc1NSMDFxc0dNNW9idTZ4bXRnaGtEampCSk82U0YzeTN6UU0zMXhHVWpkMHNyeE1aWDRXRi9Xc29HSENsVkI3OXRwODl3U2dleUdJVGhyMDM0dG1xUTVZUm8za3p5THMrc0dPTkYzcVd4dzdMZGJPZUpyQ2dWWlExMXdhTFc3WktYZ05JNkl4QjRrUStkOHNpcDdKUUUwZ0Jwb2hZS3hvQUtLbEU1eFkva2NNcXFyYkpEQkU1ckxnMVJML0FaaDQ2SW9SMGxmYy8xT0tOTWZ6UllYOXgxRGs4UlpQRUJmaGw4cWV6anAwNzhXbHRSRnNJYU5ROUZqNjlwRkNPNjVWKzdiQW9IWjVnTDRhaHgxYUJuOUlxd1VDUUtUcHBLMTNzekxnMmthY0tabDQ4WXEwdlBmR1Z4UDMiLCJtYWMiOiIzOWJlMDhhN2EyZWVhMjRlOTQ4ZmI5OTFjNTQ5MjQyYTljY2VlNGJlZjBjYTIyNjU3ZmY0NDJmYWY5YjY5OTJlIiwidGFnIjoiIn0%3D; expires=Tue, 17 Jun 2025 15:55:02 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6InRvMlJibWJHcFFEeHhsblRoSCtmbVE9PSIsInZhbHVlIjoid2hKYUxpSlkvQnhsc3A4SHBGMEdRTFZZRCtjQUZvZnphV3BWQzZkZ1dyY2g3cW9mbituQ1FWS2M4Zm00WU5XajlSSjI2Z3IrK25uQTNYYkdnRTVIaGVBZjR2aHhSUmw1bDRVc2hYdVkyWHkxZVN4a3RnTVhHTzVSaExvYnBvYkluS1RGQ1FDLzl0eHhXVms2b1VwOTNpa1p0ZnhKanJLM2FPdHdRVTZOblduYXI1L2ordnEzUDBiK1ovUVJQQWN3M3ArZUp5SEsrdnF0ZVZ4TGp4U2d1MGlBeXRiK0Z4SnBsZ0xJMU1NUG42SUtEQk9GUDllMll5WGk1SWtkUVdHTlg3Y2dGYXZScUpxWmk5SDFjVmI0ZlpyZ0tRTTJSbFZyTWJTYSszV29XWTlhTWhxbTNUc2lLSEp2eXp5V25pbnFqc0phL1htTldrcTlBWkVCS3VaZkxXKzljNXFublY5eWhUV3preEdyRlo1RjJqaldrcFQzaTd5K0M5YWFyY2llY0I4QkFIZEFQYXlIbllxRkwyYzRiZ3VRbDFRQTlUOHlHbTdpK0VnS2tjNUNTWlNCNWJsUXYyY2tjK3Q0MnkxYS9RUGNkQ2RGa0lQdHJmU0dyS3kwek9VT0ZVNlcwMGFsNjFKL3FDa2c5L2srMXowaVZ4Q1pnS3ZlWE9CWXB5YUUiLCJtYWMiOiJjMGY1OWRkNGIwMjk4NjRiZTBjMWU2MTBhM2NlNWIyMzgyMTZkYjcwNGRlNTkxMjgzMGNiZDkyZDQ5YTY2MmRmIiwidGFnIjoiIn0%3D; expires=Tue, 17 Jun 2025 15:55:02 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IlJPaXZNU0YrZ0NIK3NNNkhVbHM5MkE9PSIsInZhbHVlIjoiZEE0ZmtRZjEwTzJaRHdmMUp0aWZneWtJa1lqaG5VTEZ0eXI3TnJmaTk2NTdPUEY3ZGxyMDQvZ0d5c2pWaUlMd2oyRlhrUENsU0M5Z05NWlMwU3JjQlZLcGs2N1cybUh2ck5uNnFELzg4anJNclpXWEdKZ3dwQXRFMGZKU3JGeGNJd1VuZWQ4Undjbm1KVFZ4eC91UnROR0Jjd3J1bjlONmJiUUNJSDhmU3NmeFlYektXNWo1dU8rR2ZYVkthWTFZVHdlYUF2VkRBc1NSMDFxc0dNNW9idTZ4bXRnaGtEampCSk82U0YzeTN6UU0zMXhHVWpkMHNyeE1aWDRXRi9Xc29HSENsVkI3OXRwODl3U2dleUdJVGhyMDM0dG1xUTVZUm8za3p5THMrc0dPTkYzcVd4dzdMZGJPZUpyQ2dWWlExMXdhTFc3WktYZ05JNkl4QjRrUStkOHNpcDdKUUUwZ0Jwb2hZS3hvQUtLbEU1eFkva2NNcXFyYkpEQkU1ckxnMVJML0FaaDQ2SW9SMGxmYy8xT0tOTWZ6UllYOXgxRGs4UlpQRUJmaGw4cWV6anAwNzhXbHRSRnNJYU5ROUZqNjlwRkNPNjVWKzdiQW9IWjVnTDRhaHgxYUJuOUlxd1VDUUtUcHBLMTNzekxnMmthY0tabDQ4WXEwdlBmR1Z4UDMiLCJtYWMiOiIzOWJlMDhhN2EyZWVhMjRlOTQ4ZmI5OTFjNTQ5MjQyYTljY2VlNGJlZjBjYTIyNjU3ZmY0NDJmYWY5YjY5OTJlIiwidGFnIjoiIn0%3D; expires=Tue, 17-Jun-2025 15:55:02 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6InRvMlJibWJHcFFEeHhsblRoSCtmbVE9PSIsInZhbHVlIjoid2hKYUxpSlkvQnhsc3A4SHBGMEdRTFZZRCtjQUZvZnphV3BWQzZkZ1dyY2g3cW9mbituQ1FWS2M4Zm00WU5XajlSSjI2Z3IrK25uQTNYYkdnRTVIaGVBZjR2aHhSUmw1bDRVc2hYdVkyWHkxZVN4a3RnTVhHTzVSaExvYnBvYkluS1RGQ1FDLzl0eHhXVms2b1VwOTNpa1p0ZnhKanJLM2FPdHdRVTZOblduYXI1L2ordnEzUDBiK1ovUVJQQWN3M3ArZUp5SEsrdnF0ZVZ4TGp4U2d1MGlBeXRiK0Z4SnBsZ0xJMU1NUG42SUtEQk9GUDllMll5WGk1SWtkUVdHTlg3Y2dGYXZScUpxWmk5SDFjVmI0ZlpyZ0tRTTJSbFZyTWJTYSszV29XWTlhTWhxbTNUc2lLSEp2eXp5V25pbnFqc0phL1htTldrcTlBWkVCS3VaZkxXKzljNXFublY5eWhUV3preEdyRlo1RjJqaldrcFQzaTd5K0M5YWFyY2llY0I4QkFIZEFQYXlIbllxRkwyYzRiZ3VRbDFRQTlUOHlHbTdpK0VnS2tjNUNTWlNCNWJsUXYyY2tjK3Q0MnkxYS9RUGNkQ2RGa0lQdHJmU0dyS3kwek9VT0ZVNlcwMGFsNjFKL3FDa2c5L2srMXowaVZ4Q1pnS3ZlWE9CWXB5YUUiLCJtYWMiOiJjMGY1OWRkNGIwMjk4NjRiZTBjMWU2MTBhM2NlNWIyMzgyMTZkYjcwNGRlNTkxMjgzMGNiZDkyZDQ5YTY2MmRmIiwidGFnIjoiIn0%3D; expires=Tue, 17-Jun-2025 15:55:02 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-582388836\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1758302513 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>16</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"30 characters\">http://localhost/hrm-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1758302513\", {\"maxDepth\":0})</script>\n"}}