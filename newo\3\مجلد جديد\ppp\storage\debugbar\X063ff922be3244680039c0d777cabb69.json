{"__meta": {"id": "X063ff922be3244680039c0d777cabb69", "datetime": "2025-06-17 13:55:09", "utime": **********.835059, "method": "POST", "uri": "/warehouse-empty-cart", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.189958, "end": **********.835087, "duration": 0.6451289653778076, "duration_str": "645ms", "measures": [{"label": "Booting", "start": **********.189958, "relative_start": 0, "end": **********.726941, "relative_end": **********.726941, "duration": 0.5369830131530762, "duration_str": "537ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.726955, "relative_start": 0.5369968414306641, "end": **********.835091, "relative_end": 4.0531158447265625e-06, "duration": 0.10813617706298828, "duration_str": "108ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 46326408, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST warehouse-empty-cart", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\ProductServiceController@warehouseemptyCart", "namespace": null, "prefix": "", "where": [], "as": "warehouse-empty-cart", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FHttp%2FControllers%2FProductServiceController.php&line=1663\" onclick=\"\">app/Http/Controllers/ProductServiceController.php:1663-1673</a>"}, "queries": {"nb_statements": 2, "nb_failed_statements": 0, "accumulated_duration": 0.025050000000000003, "accumulated_duration_str": "25.05ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 16 limit 1", "type": "query", "params": [], "bindings": ["16"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.780604, "duration": 0.02389, "duration_str": "23.89ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 95.369}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.820143, "duration": 0.00116, "duration_str": "1.16ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 95.369, "width_percent": 4.631}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "16", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]"}, "request": {"path_info": "/warehouse-empty-cart", "status_code": "<pre class=sf-dump id=sf-dump-1880064048 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1880064048\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-1856868012 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1856868012\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-178878771 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>session_key</span>\" => \"<span class=sf-dump-str title=\"3 characters\">pos</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-178878771\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1526884024 data-indent-pad=\"  \"><span class=sf-dump-note>array:19</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">15</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1936 characters\">_clck=2gyfy%7C2%7Cfwu%7C0%7C1994; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clsk=1ti4td2%7C1750168503247%7C8%7C1%7Cl.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6InVVNXFPV0ZqOW1SR1hzN1d6V2Y4V2c9PSIsInZhbHVlIjoicGV4MW85dzV0T0JjUG9BNGc4Vmd6Um9tYUVNcW5JWHhYMHB4d0FlL0R6RHNIT2dQMExrT1crRTFicDlIanlIRFhObFIybUdFMzdBMzhEUGpHY3FzNFVvdXhxZ1pCenh3V0pmZ1E5bXJ0ZXlKd0U0RlhOYzRWM2h0cGZ4S050WmZnMkFwV2FDN3A4VUtiV2lyM09SQ3pjTnlVUHhuVlUydHMycitiUkJKdEVZZXlkSnJVMjBObUxzTGZqSURZY2tPNjUxMEd2eFUwV3VRalN1clR3SGxmSjRoNEl0ekpnQTZ2ZzJHYVlTSVV1Q3Zmb0RjSUJhdm9Rd3ZBRERnVUhiM3cyc1NLYXhrYkprbXF3VnBJa2lrWVg0NHp6dFFmQ0tBc0NDZ3pYZTJBOHk1TlBoN0JrdGUwS0xnMG5FRkgxZHJxYXl4elREK29NdGFJbVVzZmlkcXN0dDB0cVRjRWkwQnN0QzdKRTdaamh4S2NkcUIybXNZUEdENUwzYjFhMXpiVVljODBHMTdPK0M0b1lqRUVwOHJOMkVmQnl0cG9kcHgzWXROZmRwY2xzME5WWHlxQUpUSHpkTytBWlJEOGZhWE1KaHNPUXBHeC9KL0VMYUhLQTJPenI1dGJtS3JuUWZjaGxzOGhOTitJQzRidVZlMUtNUS9RS1oydFR6dGxJWFciLCJtYWMiOiIyODdmOWZlOGUzZWUzNGFhOGI3OTlkNGE0M2ExZjAwNDY0MTcwNmRjNmEwNzliOWZlY2U2NDEwYWVkNGExZjlkIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6InhneDBqbmI0dDZHL0g1dFVOTHBjMHc9PSIsInZhbHVlIjoiMVRrYzR2aGtDOGFvdzZyb3kwVVpXWU5tNGdCSElkSmpHUVZoWTN1dXc1ek4zaHBDeERxZFBqdmJkRUROZ3haZHltZ0JXMUQza1liWnp1MjIxYk45blhrYkZmM2hzMHBTQWpRbXBzaGRsY0MwVjN4Umw1UFJuaGxabzY4bWxSdWFRRjNzTDNUejNMQVQ5UGhobWdSN3lubUhVQnUwMTJxSnNOSVl3LytJUG1Yek5MRVZ3dmFRSGxMNmprSk5YRmdOMVhZekVhanRIR0RMeUVHbTdUVUc1c2xLUnRoY1FkeEhLa0x0aDVNZmZhTTJjY3psWjJEZkhWRkJlN2NvOW1xNEgrYStFRjJCUysxZThXeFZkK2JBSXAyaE96NGlNd2pNLzJybnQ4UWVOdFlHOTZHVG9qTHArMmdWdDlWeTFhaEZaNEJqWHZRcHBRcjVqM3o5N0NGZFFQcFJ3Sm5leEpyVVZhWnl4aElOa0grU3pkMXRabTJQcjBPaUZGL1F1MmFnNUN4Y0hqQmVlTURoOS9ueEdlUHNLRXdZcktjZ0V2Y1pMWm5XSFBTaUsvNFgyK3BFNkY0OHVBbC80Q0ZzSU1QK1hCamRRVFBJQzI4RElzcS9pY01mSEVzWmlZYUxpWkJKRXZnNHBBYlErM2txdWZteVA2VlRvUldGTUc1SW1VdmYiLCJtYWMiOiJkYWJmOWNkM2I0MjE1OGNiMzg1NWRiOTNkODVjYTE1NDgwMWQwYzMzMTc4MzczMTYzYzlmZDhlMmZjNjcyMGMwIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1526884024\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1846353294 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">VloaUQCBjpDKrel7nHeAzB1sTLdTMwteSos1MmAN</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1846353294\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-2125662351 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 17 Jun 2025 13:55:09 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IjNWTmFBUFJhUlE1VHh4czFkdnBGdHc9PSIsInZhbHVlIjoiY1puOVphd0pQcEFKZmoyZHUrRDRtMVRTWCtlYWFnNzhsWHR6ZEVPM1hmOERQWnczdHN6MUZoMDhHaXpKTFFQUTltaHVFYmlnZmhIWlVvUnpOcUFveFVwTzF2c2VjVkVJM0pCVlZZZ2pmMlB0dlE0azU0RHZJRlZjNEphckp0dVR6N2JVRWEyM081QTNiWTBWbWt1Kzg0SmNUUjE5MVhJc281ODlNK0xKOHMxTlJUWnpua3FhUDhoVlR2QnZZZC8xdU5kOWVHcGN1c0xJWDY5cTNwUXNXVFdTcmkyK3BqUjdYWGg3UVk3Ym9zVzFJOEszekRTL3BweG5pUms5a2ZnL3UxWFlRd1JZdVVEQkxYenZkTlJqM1Rlc2RGY0VsRVJiL3hyM2NLa2w4ZEJKekdnUXZuQ2s4T3lQejZhYnNQdnZoVm9SMzREUngzQ1BFQ3UzODMrbkdWS2duZDdlakFBYmRoODVZa0hYZUVFSUpURVl4bzZWQ1p3cGlhNXZ1Si9XdFdNVC95aDVnK2wra3M4eDJDQ2VqbFJQVVN1dU5pUHBqc3REUHM3SDlnMlZFdE5JZTFwWUJSNHZBZlRXWTZqVVNzYnZtMitoQlYxKzRkMFRoQ1V1QVUxZ3JSYVVzZUZHOFVUbUFlR3MrWWVIdGh0cjQ0T1lsRVJGSGQyTHQ0MlMiLCJtYWMiOiIzZjZlMWQwNDQyYmZkNWU0M2JmNGE2YzM2MmY1MjliY2UzODg1OGM1ZDk4MjU4YjIzYzZjNGZkOGZjMmMwZjdjIiwidGFnIjoiIn0%3D; expires=Tue, 17 Jun 2025 15:55:09 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6InI4ayt0Q05WanErRlBIci9wSXc0ZVE9PSIsInZhbHVlIjoiMnF6bXhSMDFsSmNpL3lZTHpZcFJveWc1cU11QXRsZjdrMDlvMFdwWm9vY3ZHenFHcUxlbTkyUzdjaFBrTmthSzBnL1NqbHo3K0dnVnEwVzJyV3JSTGxGaFJMeVhzekVFVjcyWXBTZ3BIUHB6VWxWZjZzditLSDZ5VmtWRDUrNk5DNERpdjRrUXZGTXd1T0JKM2dkaWxhQS9GSlN6a2piK1VkRTh6UlBkQ0NDWERFMmtKSkNlOTJZU3hWL01FRHY3Tndyem40SCtWTWF1VkVLUVVuOEJZWHo3K1BBRXVVcVpla0haRlo4RVJ0ZXlwM01aVS9YN0JkNDlCZUM0N3cvRWhRMmxIaXNLQ0wrMTZsVTlqcERtT2prMVlhYitKQ3gwb1B5WTFpWVBFRkNkbGpTL3ovUmJlZzJUdU1zSlFPekwxMThNVDVsK2FmVFM0cWM4Mi9QbWFnV1Z6bnd6UVFaWWJqd2IzUHdzaWU0TnhJY1daVldmZklMWnNWV1piYUZuTkg1ck80QmJHOTJ4Yk1nTXlxOXVOWWFIY0x6VGRxVlR3dnE4REV6R2pqelJPTmgxN1MzZXIrOHl5aXFHK1FsRFdIODB5WTEveWlQU2ovWWJhVFdIZUYyUWsveGtQVjdZT2NqRzdKMGV6Y0pYMVZadEhGSlZTVmV0dFplelRBRXMiLCJtYWMiOiIwODRkZDg3NTA0NjM2ZTcwZWRjOGU5ZDcxMzcwMTc0ZGQ3NjczNjcyMzE5NGVlZWViYWQ4MzY5ODJiNjBiNTg3IiwidGFnIjoiIn0%3D; expires=Tue, 17 Jun 2025 15:55:09 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IjNWTmFBUFJhUlE1VHh4czFkdnBGdHc9PSIsInZhbHVlIjoiY1puOVphd0pQcEFKZmoyZHUrRDRtMVRTWCtlYWFnNzhsWHR6ZEVPM1hmOERQWnczdHN6MUZoMDhHaXpKTFFQUTltaHVFYmlnZmhIWlVvUnpOcUFveFVwTzF2c2VjVkVJM0pCVlZZZ2pmMlB0dlE0azU0RHZJRlZjNEphckp0dVR6N2JVRWEyM081QTNiWTBWbWt1Kzg0SmNUUjE5MVhJc281ODlNK0xKOHMxTlJUWnpua3FhUDhoVlR2QnZZZC8xdU5kOWVHcGN1c0xJWDY5cTNwUXNXVFdTcmkyK3BqUjdYWGg3UVk3Ym9zVzFJOEszekRTL3BweG5pUms5a2ZnL3UxWFlRd1JZdVVEQkxYenZkTlJqM1Rlc2RGY0VsRVJiL3hyM2NLa2w4ZEJKekdnUXZuQ2s4T3lQejZhYnNQdnZoVm9SMzREUngzQ1BFQ3UzODMrbkdWS2duZDdlakFBYmRoODVZa0hYZUVFSUpURVl4bzZWQ1p3cGlhNXZ1Si9XdFdNVC95aDVnK2wra3M4eDJDQ2VqbFJQVVN1dU5pUHBqc3REUHM3SDlnMlZFdE5JZTFwWUJSNHZBZlRXWTZqVVNzYnZtMitoQlYxKzRkMFRoQ1V1QVUxZ3JSYVVzZUZHOFVUbUFlR3MrWWVIdGh0cjQ0T1lsRVJGSGQyTHQ0MlMiLCJtYWMiOiIzZjZlMWQwNDQyYmZkNWU0M2JmNGE2YzM2MmY1MjliY2UzODg1OGM1ZDk4MjU4YjIzYzZjNGZkOGZjMmMwZjdjIiwidGFnIjoiIn0%3D; expires=Tue, 17-Jun-2025 15:55:09 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6InI4ayt0Q05WanErRlBIci9wSXc0ZVE9PSIsInZhbHVlIjoiMnF6bXhSMDFsSmNpL3lZTHpZcFJveWc1cU11QXRsZjdrMDlvMFdwWm9vY3ZHenFHcUxlbTkyUzdjaFBrTmthSzBnL1NqbHo3K0dnVnEwVzJyV3JSTGxGaFJMeVhzekVFVjcyWXBTZ3BIUHB6VWxWZjZzditLSDZ5VmtWRDUrNk5DNERpdjRrUXZGTXd1T0JKM2dkaWxhQS9GSlN6a2piK1VkRTh6UlBkQ0NDWERFMmtKSkNlOTJZU3hWL01FRHY3Tndyem40SCtWTWF1VkVLUVVuOEJZWHo3K1BBRXVVcVpla0haRlo4RVJ0ZXlwM01aVS9YN0JkNDlCZUM0N3cvRWhRMmxIaXNLQ0wrMTZsVTlqcERtT2prMVlhYitKQ3gwb1B5WTFpWVBFRkNkbGpTL3ovUmJlZzJUdU1zSlFPekwxMThNVDVsK2FmVFM0cWM4Mi9QbWFnV1Z6bnd6UVFaWWJqd2IzUHdzaWU0TnhJY1daVldmZklMWnNWV1piYUZuTkg1ck80QmJHOTJ4Yk1nTXlxOXVOWWFIY0x6VGRxVlR3dnE4REV6R2pqelJPTmgxN1MzZXIrOHl5aXFHK1FsRFdIODB5WTEveWlQU2ovWWJhVFdIZUYyUWsveGtQVjdZT2NqRzdKMGV6Y0pYMVZadEhGSlZTVmV0dFplelRBRXMiLCJtYWMiOiIwODRkZDg3NTA0NjM2ZTcwZWRjOGU5ZDcxMzcwMTc0ZGQ3NjczNjcyMzE5NGVlZWViYWQ4MzY5ODJiNjBiNTg3IiwidGFnIjoiIn0%3D; expires=Tue, 17-Jun-2025 15:55:09 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2125662351\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-88117402 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>16</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-88117402\", {\"maxDepth\":0})</script>\n"}}