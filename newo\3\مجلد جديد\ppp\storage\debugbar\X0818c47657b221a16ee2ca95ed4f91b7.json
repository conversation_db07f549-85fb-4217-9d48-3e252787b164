{"__meta": {"id": "X0818c47657b221a16ee2ca95ed4f91b7", "datetime": "2025-06-17 13:50:07", "utime": **********.158569, "method": "GET", "uri": "/customer/check/delivery?customer_id=10", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1750168206.588804, "end": **********.158591, "duration": 0.5697870254516602, "duration_str": "570ms", "measures": [{"label": "Booting", "start": 1750168206.588804, "relative_start": 0, "end": **********.083273, "relative_end": **********.083273, "duration": 0.49446892738342285, "duration_str": "494ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.083285, "relative_start": 0.49448108673095703, "end": **********.158594, "relative_end": 2.86102294921875e-06, "duration": 0.07530879974365234, "duration_str": "75.31ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 44962128, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET customer/check/delivery", "middleware": "web, verified", "controller": "App\\Http\\Controllers\\CustomerController@checkDelivery", "namespace": null, "prefix": "", "where": [], "as": "customer.check.delivery", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FHttp%2FControllers%2FCustomerController.php&line=365\" onclick=\"\">app/Http/Controllers/CustomerController.php:365-378</a>"}, "queries": {"nb_statements": 2, "nb_failed_statements": 0, "accumulated_duration": 0.0173, "accumulated_duration_str": "17.3ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 16 limit 1", "type": "query", "params": [], "bindings": ["16"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/AuthManager.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\AuthManager.php", "line": 57}, {"index": 23, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 33}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.1240718, "duration": 0.016399999999999998, "duration_str": "16.4ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 94.798}, {"sql": "select * from `customers` where `customers`.`id` = '10' limit 1", "type": "query", "params": [], "bindings": ["10"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/CustomerController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Http\\Controllers\\CustomerController.php", "line": 368}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.146529, "duration": 0.0009, "duration_str": "900μs", "memory": 0, "memory_str": null, "filename": "CustomerController.php:368", "source": "app/Http/Controllers/CustomerController.php:368", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FHttp%2FControllers%2FCustomerController.php&line=368", "ajax": false, "filename": "CustomerController.php", "line": "368"}, "connection": "kdmkjkqknb", "start_percent": 94.798, "width_percent": 5.202}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\Customer": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FModels%2FCustomer.php&line=1", "ajax": false, "filename": "Customer.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "16", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]"}, "request": {"path_info": "/customer/check/delivery", "status_code": "<pre class=sf-dump id=sf-dump-1205555681 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1205555681\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-1587761340 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>customer_id</span>\" => \"<span class=sf-dump-str title=\"2 characters\">10</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1587761340\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1921546737 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1921546737\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1613505636 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1936 characters\">_clck=2gyfy%7C2%7Cfwu%7C0%7C1994; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clsk=1ti4td2%7C1750167218299%7C2%7C1%7Cl.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6ImxuMktQRUZHUHpNV3FJemFkMUtSbUE9PSIsInZhbHVlIjoiU3QxNWpzZXhwTTBCMkVxbUhRaWcydkRPYVhEMk1Qa0h3QmttK3EzNnY5SDFvWXVJUm5DZ0RCREt2UnRhYUgvOXR4Y1dFKy9jMkhsNjlhdFpyWG1WSG1RejIwNEkraElyL3hsUVVGNnk3QytGM09SNnFGOVBkRHZ2NFF6SENsMWdzV1E5V1cvb2dkNkdUT2VKN3BUUGNRQVlFTHJvQ2pLemdnL2JLa0M4STFJdXBVU0Eyb1NESlV3RS9ZdEZWUFpHb0pDUU5yQnZ5YzFYWlI3TlJuL1BvTVJyckV5NVFjNXFyWmhtc3E5OVdXMFBpYzZ6RHJiM2hNUUJmNnpMcTk3MDgya1E1VVRnU0ZLRTNjVDh0QXZXSllpbkRiREE4OG83emNBSkxWMEZRWlBmLzl1czNxRjh1cGdTRDV6TzROQmlrSmxpSWdpWWVmR3VNKzdNdVVEWXdyUDJkaEpmM051U0hHaElrRGJ4RnVnQlpGUlkrMythZjRkV1VDNjVxc1ljVyt0WjlTcHNGT204QkEwd2FmTlY5VnFFc3h2eE5nMFFPN2FSSjQ1Y1FwQVpma1hXenJqRUVOeDR4ay9xc011MXNFN3UxSXRlY2RFWmZWelBLa293VVhhWEJURWJ2TnFWd3NydFh0eUp2clo2QmY0Q2xsK0ozL1B4cnREMkRlelEiLCJtYWMiOiJmNDMzMWQ2ZDA0MDMwZDYxNDllYTc1YmM4NGNkOGEwMDRjYjg0M2E2YTZmMGQ4ZWQ4MTIzYTg3ZGQ5ZDc5NmNjIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6IkZLQ3VRR2dSeGlwVDl5UjI1T0gyQUE9PSIsInZhbHVlIjoiUzJTQlBHTGJrTkJEeTVacTRQNS9MU01ubm5KSHJRc3F0cmJFc09McWVmZ1hqUDFSSksxTGo3L29MeVFuTXo3Vk5RRndLdExOUGJXWENLNDg5dlczM1B1REVobzBybGFKMW5FQW1ENnRtMUwwRUxNSXE2WVdLQ1N6YlUyRkFPcGZpZWNJVStxSjBJT1hlSVMxZkNiWUllV0ZjaG1jQ1ZieWRMcFJNcnU3ZjN5dURDb2lPR3RzWkc0NTVHT2pmbDhWMHhxQmx5czFDeVFXUG14R2VrbEdqdmVrSTcyUkRzdTJpOENGOVM3RVlObHVKOGdBUmE5SEEwMGJVeWVvdkJCaUt6N0RZWGw1MG56clFtRDEzdklMbjZoNll6N1FiZG9rTkJFay9WN1hRMk1pSDJwQ2xzVzAvdEtlaW1RUEd3bWdoV1VrSElqYTZ2SVh6Y1lEZHI3YXJsaDhiTmYyNm1ZczBSQytwZUZBZUN2NVh5bFVJUFZxVzVlUGFDb1gveGVBQy9zYTFIaHQ5Y0lJSld2NUlJRUExSE11cHhoNDNBb0c4bE13WjV3MDhzb2lndFMwRVFTUTNTK1Z2YzJSSkJmc25XbGtWano1ZmxwRVhkNFlFdDV4SFNHemNkcVY5a3l2aEY3K2ZsbTRuNlpYZTZ4eFN5NlpoLzh3cTRPbWZGcmIiLCJtYWMiOiIwMjYxODkxN2RkNGYzYzdmNGJhZjM0ZjA0OWI1ZGI3YzQzMWU5N2E5ZWQzZWE1MGU0MmI3Y2FjNThhZWRmYzQ2IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1613505636\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1859699349 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">VloaUQCBjpDKrel7nHeAzB1sTLdTMwteSos1MmAN</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1859699349\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1843587081 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 17 Jun 2025 13:50:07 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IlZGaGI4SStQUk1xbmVNSDNHU2ZoSFE9PSIsInZhbHVlIjoiQzF4RlZLbmVwV2J3NjhqT3RYNE0rM0w2QjIxSTVOc0dZVDVlSjRRREpDRE1XUlVtZ045Zk9qR2puY3R0TnZ3Q1o4SEE4WkhxMDlZRFgwRktjVmpYUjkwTmRUT3ZpdFBoTE5hTzVVWFdzZy9KZXJkNmVxb2FHYnp5YndhY2R1blZBRDB1UTVETWlvR2NDKytLT24xZ01BZW15WWlGL3ZiNWFmYkJIMjEvNWVGRTBsL1FxcmNqVUhiVGlETHljTGlKaGxTd2pRSXhxRkE5ZUhrN29rS0tMajVmd0NoMVZwQlV0REJWU1Vsc0pSZnZOOXVMUWxwbVRlclJtcW4xSjZOMUd3VzY5aHR4UFFVMlZkbEl6TXVwY0NjSDJ6U3BxUUlFYVJSbXRUSVVjMDB3eFZmc05IUjNROWcrTHR4d29GbnltZ3dHaUdlMFRlb0NXOVVqZ20vU05rMTRtQUdlTnBjOGl3VERQd28xZENrR3NIYlBudTF6ZktHczBMNHlDSU9OczV5ZEg3Z2VwZ25KdzNMRWkrVVdzQUxIZmxHbnQvdU52dTR4MmM4Sjh4TzdCdDFDL1dkNjNYVWRBcFVWek95ZjgyWGtxbmdhcXRSYmRHR1AwenJNb3IvM3JqUlIwcnBoS0lQNUNCWVlVd0JCazhSOXY4R0ZlRndKNWZERjkxWlMiLCJtYWMiOiJhNGIwN2RkNzg4ODBhNjE2NzVlNzE4ZGQ0OGZlNTMyMTk1MzEwOTE1YTc5ZTE5Zjg0N2RlNjQzMDBlNzhmMThhIiwidGFnIjoiIn0%3D; expires=Tue, 17 Jun 2025 15:50:07 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6IkxPWG5LMXU0SlRDb003U3ZwRzJ4MWc9PSIsInZhbHVlIjoiL3hIRkVwcHNGbElrRFVOSXhUSHNCdUl0RWdFT1ErREFnc1RxbERrZkw0b1ljQ05HZGtmZHdqL1dwcXlReFc2RUt6Wnp5Q3RWS1VZWEpNNlp6ejdVYVNFN21KNGFTRVM0MTNQaks3amdrSFlqc1dlNVIvYmg5ZzcxcGdwUkRNK2xhaU4yajBLdCtRRVRVbTNZUkRJUStHc1d0NzhlbFZ5K05OWlNhNzdVMzFySm1xZno3bzFOWlQvVWxnWTRMbFNjejh6UHNGUFUwQ3J2U2N4RUowZk5aQm93YWJ4dHYvNkQ2K0toQWplaHBWbWszcXY2eTJpZDJFdTdJbWtSMTRyQ01NSXI2V2xFUU1LbmhPR3lreXF6ajZLR3dtSUlCV2prVUd6aStCaS8wOVQ5TTVrT2F0ZHBScXVMOU05cXE4Rm53ZGdMZ2VZMm9HLzI0TkpONE01dWQxZHdXQzZSZmdnMGhkbzhwVW5TckJOWWJ4YzRMTkFBU25XRTJ0UWpvL2RxOVZVNDhBS3RSYjFXRlNlaHp5bVIvempVZ0RLblBPdkV2SThkZmtxVjlScWNzZStJY1U4WW9rTmVXeGZxQUxSdjR0UUdqZlAyMEVZczBvRlhlU05aS2sxR25sYzJDeE4rYU5MVEloTmVmZDhNQS9lL3lMMTYyZVcyMzA4b0pFZjAiLCJtYWMiOiJmZTYxYjc4ZDBiNDYxNDgzNTM4MjMzMjI1ZjIyYzY4YWZiNzQxODhmOTc2ZjhkYjkzZjJlOWE2YzIxNDRjNGJkIiwidGFnIjoiIn0%3D; expires=Tue, 17 Jun 2025 15:50:07 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IlZGaGI4SStQUk1xbmVNSDNHU2ZoSFE9PSIsInZhbHVlIjoiQzF4RlZLbmVwV2J3NjhqT3RYNE0rM0w2QjIxSTVOc0dZVDVlSjRRREpDRE1XUlVtZ045Zk9qR2puY3R0TnZ3Q1o4SEE4WkhxMDlZRFgwRktjVmpYUjkwTmRUT3ZpdFBoTE5hTzVVWFdzZy9KZXJkNmVxb2FHYnp5YndhY2R1blZBRDB1UTVETWlvR2NDKytLT24xZ01BZW15WWlGL3ZiNWFmYkJIMjEvNWVGRTBsL1FxcmNqVUhiVGlETHljTGlKaGxTd2pRSXhxRkE5ZUhrN29rS0tMajVmd0NoMVZwQlV0REJWU1Vsc0pSZnZOOXVMUWxwbVRlclJtcW4xSjZOMUd3VzY5aHR4UFFVMlZkbEl6TXVwY0NjSDJ6U3BxUUlFYVJSbXRUSVVjMDB3eFZmc05IUjNROWcrTHR4d29GbnltZ3dHaUdlMFRlb0NXOVVqZ20vU05rMTRtQUdlTnBjOGl3VERQd28xZENrR3NIYlBudTF6ZktHczBMNHlDSU9OczV5ZEg3Z2VwZ25KdzNMRWkrVVdzQUxIZmxHbnQvdU52dTR4MmM4Sjh4TzdCdDFDL1dkNjNYVWRBcFVWek95ZjgyWGtxbmdhcXRSYmRHR1AwenJNb3IvM3JqUlIwcnBoS0lQNUNCWVlVd0JCazhSOXY4R0ZlRndKNWZERjkxWlMiLCJtYWMiOiJhNGIwN2RkNzg4ODBhNjE2NzVlNzE4ZGQ0OGZlNTMyMTk1MzEwOTE1YTc5ZTE5Zjg0N2RlNjQzMDBlNzhmMThhIiwidGFnIjoiIn0%3D; expires=Tue, 17-Jun-2025 15:50:07 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6IkxPWG5LMXU0SlRDb003U3ZwRzJ4MWc9PSIsInZhbHVlIjoiL3hIRkVwcHNGbElrRFVOSXhUSHNCdUl0RWdFT1ErREFnc1RxbERrZkw0b1ljQ05HZGtmZHdqL1dwcXlReFc2RUt6Wnp5Q3RWS1VZWEpNNlp6ejdVYVNFN21KNGFTRVM0MTNQaks3amdrSFlqc1dlNVIvYmg5ZzcxcGdwUkRNK2xhaU4yajBLdCtRRVRVbTNZUkRJUStHc1d0NzhlbFZ5K05OWlNhNzdVMzFySm1xZno3bzFOWlQvVWxnWTRMbFNjejh6UHNGUFUwQ3J2U2N4RUowZk5aQm93YWJ4dHYvNkQ2K0toQWplaHBWbWszcXY2eTJpZDJFdTdJbWtSMTRyQ01NSXI2V2xFUU1LbmhPR3lreXF6ajZLR3dtSUlCV2prVUd6aStCaS8wOVQ5TTVrT2F0ZHBScXVMOU05cXE4Rm53ZGdMZ2VZMm9HLzI0TkpONE01dWQxZHdXQzZSZmdnMGhkbzhwVW5TckJOWWJ4YzRMTkFBU25XRTJ0UWpvL2RxOVZVNDhBS3RSYjFXRlNlaHp5bVIvempVZ0RLblBPdkV2SThkZmtxVjlScWNzZStJY1U4WW9rTmVXeGZxQUxSdjR0UUdqZlAyMEVZczBvRlhlU05aS2sxR25sYzJDeE4rYU5MVEloTmVmZDhNQS9lL3lMMTYyZVcyMzA4b0pFZjAiLCJtYWMiOiJmZTYxYjc4ZDBiNDYxNDgzNTM4MjMzMjI1ZjIyYzY4YWZiNzQxODhmOTc2ZjhkYjkzZjJlOWE2YzIxNDRjNGJkIiwidGFnIjoiIn0%3D; expires=Tue, 17-Jun-2025 15:50:07 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1843587081\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1924800765 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>16</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1924800765\", {\"maxDepth\":0})</script>\n"}}