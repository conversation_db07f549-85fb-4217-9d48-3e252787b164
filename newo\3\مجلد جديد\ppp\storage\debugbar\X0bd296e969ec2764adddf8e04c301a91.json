{"__meta": {"id": "X0bd296e969ec2764adddf8e04c301a91", "datetime": "2025-06-17 13:53:19", "utime": **********.469266, "method": "GET", "uri": "/customer/check/warehouse?customer_id=10&warehouse_id=8", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1750168398.877816, "end": **********.46929, "duration": 0.5914740562438965, "duration_str": "591ms", "measures": [{"label": "Booting", "start": 1750168398.877816, "relative_start": 0, "end": **********.377762, "relative_end": **********.377762, "duration": 0.49994611740112305, "duration_str": "500ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.377775, "relative_start": 0.49995899200439453, "end": **********.469293, "relative_end": 3.0994415283203125e-06, "duration": 0.09151816368103027, "duration_str": "91.52ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 46235912, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET customer/check/warehouse", "middleware": "web, verified, auth, XSS, revalidate", "controller": "App\\Http\\Controllers\\CustomerController@checkWarehouse", "namespace": null, "prefix": "", "where": [], "as": "customer.check.warehouse", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FHttp%2FControllers%2FCustomerController.php&line=383\" onclick=\"\">app/Http/Controllers/CustomerController.php:383-397</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.018820000000000003, "accumulated_duration_str": "18.82ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 16 limit 1", "type": "query", "params": [], "bindings": ["16"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.420112, "duration": 0.016390000000000002, "duration_str": "16.39ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 87.088}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.449564, "duration": 0.0011899999999999999, "duration_str": "1.19ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 87.088, "width_percent": 6.323}, {"sql": "select * from `customers` where `customers`.`id` = '10' limit 1", "type": "query", "params": [], "bindings": ["10"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/CustomerController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Http\\Controllers\\CustomerController.php", "line": 388}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.456853, "duration": 0.00124, "duration_str": "1.24ms", "memory": 0, "memory_str": null, "filename": "CustomerController.php:388", "source": "app/Http/Controllers/CustomerController.php:388", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FHttp%2FControllers%2FCustomerController.php&line=388", "ajax": false, "filename": "CustomerController.php", "line": "388"}, "connection": "kdmkjkqknb", "start_percent": 93.411, "width_percent": 6.589}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\Customer": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FModels%2FCustomer.php&line=1", "ajax": false, "filename": "Customer.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "16", "_previous": "array:1 [\n  \"url\" => \"http://localhost/hrm-dashboard\"\n]", "pos": "array:1 [\n  2044 => array:9 [\n    \"name\" => \"تجربية\"\n    \"quantity\" => 1\n    \"price\" => \"10.00\"\n    \"id\" => \"2044\"\n    \"tax\" => 0\n    \"subtotal\" => 10.0\n    \"originalquantity\" => 1\n    \"product_tax\" => \"-\"\n    \"product_tax_id\" => 0\n  ]\n]"}, "request": {"path_info": "/customer/check/warehouse", "status_code": "<pre class=sf-dump id=sf-dump-1155890209 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1155890209\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-1562153419 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>customer_id</span>\" => \"<span class=sf-dump-str title=\"2 characters\">10</span>\"\n  \"<span class=sf-dump-key>warehouse_id</span>\" => \"<span class=sf-dump-str>8</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1562153419\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-71716925 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1936 characters\">_clck=2gyfy%7C2%7Cfwu%7C0%7C1994; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clsk=1ti4td2%7C1750168386913%7C7%7C1%7Cl.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IjZ1NUw0RUtvYTJYT0RKeXhxZmd5VXc9PSIsInZhbHVlIjoieWlDTklxSHAwV3U2eVZJYmt0V29wbzdGeFFvNXI4VmpJTkZVd3IxUGJuV1h4Q0JHUTJZNXRHd3o2ek1RcnpxdXdJdk1hRTBDM013TkxjUVZ1YnJqMUh6UmNIMGxLcURwTzVkb3Urb1FrQkM0bXhQY2FSbjBNcmRYV2dlTGtYTzlyVHEvNEM2QXpYc0FTZGUvQW5DRUZ1bi81UVB1WER3WnBWcGJPK1FDdE1BZzJRMHNrRER4VlIvSDB6UndHZjJUc0tncG9IN255VHlVa25mV0V3UWl0Qk1VU0FFT3pOcVk3V0pGQXptRWJTUUg3WXdQRnVhZU8rTjk5b3FCUk1uTS9ub2t6UURSUTI3R3NGYytTMU9ic0FtOS90MmdxZXZzS2kvRUJ4TzZOVEVSY1ZQU21rRXJFaElFanI5TXdYWlVKWU5RQkhaU1ZkRnoyNnVaZmQyNEJacTJrS1pRMEdTaTloc2JxTFUrVTIybkpWSEJ2UzhuVkFzcmg1Vzd3dU5ybmhxVzhXVVVUT3ZFOUlIbTFxbTNJSUNHeDdNYVVJR3VwN2x6UUtZT2RlNEt2OWtmdUY4eEtENTU1K25SYnBuUjdzdENMTTJxcHBYT0ZwVnVCajVlbjMxa1UycTNSY2dneFY5eWsvM1lYenhiS2s1am9GUjhOc3VkbXFUWGR3T3ciLCJtYWMiOiJiYTFmMTI1MWYwNjc5YTg4OGQ3MzE0YTNjNWEzMWY1MjljODNmYTc4Y2ZkMzFjMjhiODEzNzgzZTgwMTljZTk1IiwidGFnIjoiIn0%3D; test_session=eyJpdiI6Ijg5LzNmV2pVQy8wV1RKenBQR3JIVnc9PSIsInZhbHVlIjoiSFREbExLR3lCYmtYODNrejQvK3lVQjRXZjZ5RmlpZ1lraEt0cXRoYUxtRFZZRXB3WEtwb2VJb2JvMW4zOG5zSXdGdDkxRWdLaUhXS0o2eTl6NmVrL1FIWEJFWXZuTVJWK2s1QUlwTEtxUnJYM1RFeG0wRk1RWFN5Q1gxclhvT0tLTy9SckNPOVdLR0Y4WGJaa3RpSDhQTFpDeXlwSnl3am1ndU5Oc0k1b094WVpzazFjVUgybHBhOSs2K3hxRTJwK3paQU1BSnlMQ0N3bndzSVU2ZTlqSDlpL0ZqNXFqOTI3YnNqdTkzQU1uREFEemsrdThVZGpnREwraGFxdVRYMlZuVXFIejlnZXpLRDZCN1U0SFJqbE5HbFQ2M0Z1NDFzV1hvSDVUVXdTNkdzUzVNU0VQU1B0TnA5d1ZSYThYUFRWMVg2MjNTeGJIU3hZVkFydFUxSVdIWDNCV2pNamlYNHBZY1J4QVpmQ0FMeEVsZE1BUDV3Y2lYenk2Tmp2cDBQR2ljMHB6eUdadm0waXRvRnVYYUFXZm51VHlVcTBmM3hOVVhuVGkzcGR1czFUNUNQWjl2MUlSUHRPbnlTSEhYWEUzaS9kQXp3bVFBODdNSWo5RFV3cVhSK25ZTEVIdGtGVlg1VVFkb0ZuYlBjWElXb3JNU2ZiWDNkSk9IaDl1bXkiLCJtYWMiOiIwN2RmODQ2NmU1YWQ3ZDJiODBhYzdhOGQ2YzQ0ZjZmYjdmZTJhYmI4YmUwZDU3Yjc5YTYwN2M2MmQ3ZGNiMjYzIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-71716925\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-152519621 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">VloaUQCBjpDKrel7nHeAzB1sTLdTMwteSos1MmAN</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-152519621\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-987499129 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 17 Jun 2025 13:53:19 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-methods</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">POST, GET, OPTIONS, PUT, DELETE</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-headers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"80 characters\">Content-Type, Accept, Authorization, X-Requested-With, Application, X-CSRF-TOKEN</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Ink3NVhHUXJXMGJ2Sm5aZG9GcU1Nc3c9PSIsInZhbHVlIjoieTgyRlRoeHE3ZDlUbGw2VWZ2Tkx2VlNqaGF1Z3hmUnBza2Q5Wm1NclV4ZDZZaFZUbXB3M1NubXE5VHc5QlFLRE1aVENBU3ZmNHVwV2dGenZqZy9KQmNHVzhrZE9DTkJNSklTa3p4SzV5WlhQR0U5SUQzWERXcExjakVjKzhWMFdFNWdTSm9QdnhSMEtpRmk2OEhTZ09kWmdWQVZ6WlRlZXQ3VnRZSTFneXlOWEUwRFRxZmtxby9DS3czaXMzQlQ0dmVqT3JzNFF5VUhTNkRSQ3FLSlA3T0w5SjVNTmtxTzFkMXNVNzJpTHRDSk42NFhwNXJNWE1IYzFZbkdBZ1l0Q1M2aHdzYWF3QkZkREIwK0orUlFSNnE3MlAwTzhtWXVKdncxcEJpd01iRUhoY1d5TmJxYmZGYWc4R2JvL29FRFAwNnkwQWE0QnJuOTBsTjY4T1Z1WThhWmk2TkI2elhkZHJQbkVoa0dGRlhLL2thSTA4L3RkbzJZdU1DVjIxUlBZK3pid2Q2VmxGSk1JRlEyTTQ0UWlTNFYyVndaMXA3MjRaNkI3WGpXZXVhT3ZQanpLMFNzMW5ML21TeDVoMkZKbXlhR2RlaEpWY2xsd29UWHlJZEZvY0JleENjTHpVWWNlN3duKzlPKzdjT3RQa1F0UElrOE0zQnNMNjdMOVl4dVQiLCJtYWMiOiJiNjk4NmI2NjQ3YzBhZjUzOWE4MTUyMGM1YzUwZjcwNjljY2QwMzUyZTY5ZmIzMGE4MWQ1ZTJiMGEyM2QwYjM0IiwidGFnIjoiIn0%3D; expires=Tue, 17 Jun 2025 15:53:19 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6IkIzUjRkMHdTNkhJcGd4SUlKaXduRXc9PSIsInZhbHVlIjoiYzBqUGxtYTRobmQ4RXRiSEpvdjBDVXNNY2thRDJuam1SUTBNZ0xDMSt2N2VhdWxIS2VTb1o4VWszRkE5ekE5MGFCZHN0aEp4alIvdDNvcC9yd1liaUpWWkFLNE5HNDIvWm1yM1BkdTBWOStxanhEbHVkdWlUUUtkNlIwTXVFMVBMdDdrdVhGYnM2dy81L2ZaaFFGOU9MclBFaWlqMWxBV2Y0UkNaRDJCT1BnSkpCcndGR3Y1TE1zMWdQQzIxczF4cFp2STdBT1ZKQW1GS3ZnanVUc0pKYW83QTBoczUrTy9PRVpXU3ROd3Y0RGtLc3Fyam1jVzRhU1RvZHkwYWNGYXBjK3pTZFp5SzNOT0xCNGhMNk05T1dvM1lQRnRZbXhJYXc4cGhYT1dpQUE3R29HZ1BoR0xtV1hjWXZUY2dsRnkyRkF6NXAzZGhxOXRiR2R6VldTcDJCRHlCLy9GbjFaZVVTRVZVcncxTzRZVE9QZ1dSU0NOTFRMdmpUWXNTN2VZVlY3WGJSVDVGdm9KMXFlWnpJTHpUZWJRaWRIUXgrOG5FY0g0L0pqei9vSTk2QzBvc2Q1T0dLRVowQlZ2NXMxcm1CZnN3OE95YzdWVHo5M3BwVWNtdGhKb01TMS80eURWNWs3ZEpnMFpFY3VwSmNqcEcrSGRsc2xKSW8vdXR2K0ciLCJtYWMiOiJlNzRmODIxNGU0ZWNmNjM0YmMwMDNiYTMyODMzMzRmMDM0OTdlMzk3MGMyNjE2ZTczM2MzYjFmMjIxNzc2OWM2IiwidGFnIjoiIn0%3D; expires=Tue, 17 Jun 2025 15:53:19 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Ink3NVhHUXJXMGJ2Sm5aZG9GcU1Nc3c9PSIsInZhbHVlIjoieTgyRlRoeHE3ZDlUbGw2VWZ2Tkx2VlNqaGF1Z3hmUnBza2Q5Wm1NclV4ZDZZaFZUbXB3M1NubXE5VHc5QlFLRE1aVENBU3ZmNHVwV2dGenZqZy9KQmNHVzhrZE9DTkJNSklTa3p4SzV5WlhQR0U5SUQzWERXcExjakVjKzhWMFdFNWdTSm9QdnhSMEtpRmk2OEhTZ09kWmdWQVZ6WlRlZXQ3VnRZSTFneXlOWEUwRFRxZmtxby9DS3czaXMzQlQ0dmVqT3JzNFF5VUhTNkRSQ3FLSlA3T0w5SjVNTmtxTzFkMXNVNzJpTHRDSk42NFhwNXJNWE1IYzFZbkdBZ1l0Q1M2aHdzYWF3QkZkREIwK0orUlFSNnE3MlAwTzhtWXVKdncxcEJpd01iRUhoY1d5TmJxYmZGYWc4R2JvL29FRFAwNnkwQWE0QnJuOTBsTjY4T1Z1WThhWmk2TkI2elhkZHJQbkVoa0dGRlhLL2thSTA4L3RkbzJZdU1DVjIxUlBZK3pid2Q2VmxGSk1JRlEyTTQ0UWlTNFYyVndaMXA3MjRaNkI3WGpXZXVhT3ZQanpLMFNzMW5ML21TeDVoMkZKbXlhR2RlaEpWY2xsd29UWHlJZEZvY0JleENjTHpVWWNlN3duKzlPKzdjT3RQa1F0UElrOE0zQnNMNjdMOVl4dVQiLCJtYWMiOiJiNjk4NmI2NjQ3YzBhZjUzOWE4MTUyMGM1YzUwZjcwNjljY2QwMzUyZTY5ZmIzMGE4MWQ1ZTJiMGEyM2QwYjM0IiwidGFnIjoiIn0%3D; expires=Tue, 17-Jun-2025 15:53:19 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6IkIzUjRkMHdTNkhJcGd4SUlKaXduRXc9PSIsInZhbHVlIjoiYzBqUGxtYTRobmQ4RXRiSEpvdjBDVXNNY2thRDJuam1SUTBNZ0xDMSt2N2VhdWxIS2VTb1o4VWszRkE5ekE5MGFCZHN0aEp4alIvdDNvcC9yd1liaUpWWkFLNE5HNDIvWm1yM1BkdTBWOStxanhEbHVkdWlUUUtkNlIwTXVFMVBMdDdrdVhGYnM2dy81L2ZaaFFGOU9MclBFaWlqMWxBV2Y0UkNaRDJCT1BnSkpCcndGR3Y1TE1zMWdQQzIxczF4cFp2STdBT1ZKQW1GS3ZnanVUc0pKYW83QTBoczUrTy9PRVpXU3ROd3Y0RGtLc3Fyam1jVzRhU1RvZHkwYWNGYXBjK3pTZFp5SzNOT0xCNGhMNk05T1dvM1lQRnRZbXhJYXc4cGhYT1dpQUE3R29HZ1BoR0xtV1hjWXZUY2dsRnkyRkF6NXAzZGhxOXRiR2R6VldTcDJCRHlCLy9GbjFaZVVTRVZVcncxTzRZVE9QZ1dSU0NOTFRMdmpUWXNTN2VZVlY3WGJSVDVGdm9KMXFlWnpJTHpUZWJRaWRIUXgrOG5FY0g0L0pqei9vSTk2QzBvc2Q1T0dLRVowQlZ2NXMxcm1CZnN3OE95YzdWVHo5M3BwVWNtdGhKb01TMS80eURWNWs3ZEpnMFpFY3VwSmNqcEcrSGRsc2xKSW8vdXR2K0ciLCJtYWMiOiJlNzRmODIxNGU0ZWNmNjM0YmMwMDNiYTMyODMzMzRmMDM0OTdlMzk3MGMyNjE2ZTczM2MzYjFmMjIxNzc2OWM2IiwidGFnIjoiIn0%3D; expires=Tue, 17-Jun-2025 15:53:19 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-987499129\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1583104371 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>16</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"30 characters\">http://localhost/hrm-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pos</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-key>2044</span> => <span class=sf-dump-note>array:9</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"6 characters\">&#1578;&#1580;&#1585;&#1576;&#1610;&#1577;</span>\"\n      \"<span class=sf-dump-key>quantity</span>\" => <span class=sf-dump-num>1</span>\n      \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"5 characters\">10.00</span>\"\n      \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"4 characters\">2044</span>\"\n      \"<span class=sf-dump-key>tax</span>\" => <span class=sf-dump-num>0</span>\n      \"<span class=sf-dump-key>subtotal</span>\" => <span class=sf-dump-num>10.0</span>\n      \"<span class=sf-dump-key>originalquantity</span>\" => <span class=sf-dump-num>1</span>\n      \"<span class=sf-dump-key>product_tax</span>\" => \"<span class=sf-dump-str>-</span>\"\n      \"<span class=sf-dump-key>product_tax_id</span>\" => <span class=sf-dump-num>0</span>\n    </samp>]\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1583104371\", {\"maxDepth\":0})</script>\n"}}