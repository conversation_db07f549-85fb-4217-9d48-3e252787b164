{"__meta": {"id": "X0d117158c85776aa9da5c9682c54ef81", "datetime": "2025-06-17 13:21:10", "utime": **********.228689, "method": "POST", "uri": "/warehouse-empty-cart", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1750166469.463013, "end": **********.228715, "duration": 0.7657020092010498, "duration_str": "766ms", "measures": [{"label": "Booting", "start": 1750166469.463013, "relative_start": 0, "end": **********.113046, "relative_end": **********.113046, "duration": 0.6500329971313477, "duration_str": "650ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.113056, "relative_start": 0.6500430107116699, "end": **********.228719, "relative_end": 4.0531158447265625e-06, "duration": 0.11566305160522461, "duration_str": "116ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 46341080, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST warehouse-empty-cart", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\ProductServiceController@warehouseemptyCart", "namespace": null, "prefix": "", "where": [], "as": "warehouse-empty-cart", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FHttp%2FControllers%2FProductServiceController.php&line=1663\" onclick=\"\">app/Http/Controllers/ProductServiceController.php:1663-1673</a>"}, "queries": {"nb_statements": 2, "nb_failed_statements": 0, "accumulated_duration": 0.01316, "accumulated_duration_str": "13.16ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 16 limit 1", "type": "query", "params": [], "bindings": ["16"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.180207, "duration": 0.0121, "duration_str": "12.1ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 91.945}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.210746, "duration": 0.00106, "duration_str": "1.06ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 91.945, "width_percent": 8.055}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "ixL6BN3l9tyyUscqj0hv99yKIy0daqhD43fAAWcL", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "16"}, "request": {"path_info": "/warehouse-empty-cart", "status_code": "<pre class=sf-dump id=sf-dump-907379245 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-907379245\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-424496415 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-424496415\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1208456082 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>session_key</span>\" => \"<span class=sf-dump-str title=\"3 characters\">pos</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1208456082\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1515205861 data-indent-pad=\"  \"><span class=sf-dump-note>array:19</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">15</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">ixL6BN3l9tyyUscqj0hv99yKIy0daqhD43fAAWcL</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1937 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=1pubyvl%7C2%7Cfwu%7C0%7C1993; _clsk=25xk9k%7C1750165707106%7C8%7C1%7Cl.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IkowaGQ2T3dmUmcyUWxrM2JCTDdYcmc9PSIsInZhbHVlIjoiMkp0bXp5ZW1MODVyekRTb09KQy9aZnZUc0F0bjRZTURwV0ppTjdGakIzZERKS1M1WXVmS25SMGduVzBsOW5CQWVrMjJ3NmtBa1IydHFON0lKVEJQaEVnVTJ1NU5LeElGL2pZMVNaS2FHY1JySmhBYmFQRGZKbU5SNTlpQlFJOSsxU21QZEFMeGVPVG15WUtrWkNlaUVheXZ4S3dCNWRDbEltZ0RCb24rd1BPOWFvR2xLOENZcWZlSDlOUFlSUHA4ckhHM1I2enNjdXN1dzRrSS93OERsL0ZUQlp1SDRDNEdFWnoxbnN0UGswODgxdXBFMUN1K2g1WmpXWTI5OG4zUllFLzJuV2x1WHhzdFVrc1IrVFdvWVk4a1U0dVBWZkZ1ajZsdzV5ZlUrektPSE82KzJiMWFUT2tTMnFMMVgzdlFCd3p4WWZZNUYzMjBBZHZvRnZuSWMydWtmMUVBbktlWjZQaGhUWFdRZktPVEVrVEVHak5JWjR2RVQ1eHN1Zk9XWVZlUlNtQmJIR3Y4bmZwVlFQWXNGMjhYRG9hZzVuYVpqSzY0L2ZYcks0UVp1WTE3TEZsZ2dRNVZlL0VuWUtjdjd5cmhXZnN6TE5lYmgrRWgyVUc0MWVvUVh3NVFzQXpOZ0hTTXVoMzZIT1hhQ2xONXBIMEhiUk9QdGViZlIrSTIiLCJtYWMiOiI0N2YwN2QwOGU2Nzk0YmZlNWIzZTc3MmMxZTY3MDQ5ZjUzYjg1ZGFlMjljZjkxZmUyNWMwZThiNTRiYjE4NzRmIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6ImRRSWI2WVZ1amFnZ3hRSUdLT1E4bUE9PSIsInZhbHVlIjoibnRxR0ZCbE9hWVFKOEIwZG9lMU13dTQ2d2gzbFVQaU8rNk5RYlFvY0h4eFNDY05SL2FseTdpZnF3YUQreWhMeXAwVUFuRWZoanZwSzN6UnZSWnpqNHdjWHhoSExKa3k3UDBIcmFybE5tMkZnSXVxTzl1TC9BMUFVVkpDTTVBclpqTURBRDZUUEs3M0dzT2JPc0xocEk2SmN4Z25kdjRkNWFuWEYwV2REalhEYjUwTTBmdG9Gc0F6SDJVME1yallJaXBwdXRtQVhRVjJMYmdheC83OStTZnlvRm1ZYXhCODdweW55TUNwRVpvUHUreE0rcXllc0VEQ0VZR0xsd3Z3RnQ5WEZEZi9VNUliOXg1c3FKaDZsYmlmeWhPaXNLSW42RzNzSnpXbS9QdTgrR0ZkN3VKQkREdEZKdHY4cXYza2h2TENINWFWdGJ4ZjZIZ3dIU0RibjlWRmg2c3oyZXNvQThJb2FZdHhPUXMxY2tBQjdMRzllcDIzczZjTlpLTEpPRW9ucFM4YldjRXBVT0p5dVVGSDZpdU1FT2RmVXhCY1B1VlZ0Z3ZNWU42R0Jvd2s5WWRNd1ZOYnFDZmRka3dVTitJS1hpZEJGc0E4OHlLSHpXdVIzZFRFaW1hbk5oQ05xR0lJZzhUdzdscy9aTVNqZnp0OW5mUHh6R2lLZHdkZmUiLCJtYWMiOiIwZTJhNzg2ODljNmEzNDM5MzAzNTY5YjYwMTRmOWQwZTc4NmNhNTRkNzA2NDA4YzI5MDI4ZDUwZDRkOWQ0ZDBkIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1515205861\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-280291969 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">ixL6BN3l9tyyUscqj0hv99yKIy0daqhD43fAAWcL</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">coJ97Z8YyNCeFgF4h0jUL1PrpYBjauguIcJbJbm8</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-280291969\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1539894507 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 17 Jun 2025 13:21:10 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Imh0UFVzQ1p4dTlhWlF1ZVlxUjNwbnc9PSIsInZhbHVlIjoieE9yNHhJSFAxWk4yKzdVbjhoa2ZpTE1rTUtrUGQvRVlHTitkVHFXVHRNYmhkd253MGs2dHYvdWt4UGRZemVyQTQ2MG5LVXZrV0xFYmszM081K0RSS0ZEMlkwYjljS1dZcFBBd20xTWY4R2hGOUo3bXloQm1TMU41b29Yc0YzMGh3YkNVMXZVVG5yTlA2NmdGT2syeVlHSFlkMS9Rc2hFa2hRb0pramxGcUdyWU1ZSUpQOFMvYmdsT1ZZMGNjcUJadE1TWDVBbkdjS3hVdHBDbnhrenMxNDB2ZTlSUFJJOWJJN09wa3lVL2VnWVFWOWR6ZE8xQjZRblJmbFFxZVJvNmhyeHN5T0xoM2ZqYnFtaU1qR0Q5S081ZHpaYW9jVStpMzBCekJQQVNEaWtlZWNMQ1BwdFM3b29qNzFxaUZHYUNJSTM5OTBKb1F5LzhtZ3gxNHdKOEJCYW5BZlB1aklkRSt6MU12N0QySUZCTCtpVFRuSVpUOThtVzBoM2IzTHd4a0MrdEd2REFoU0VpaUY2V1AvZ2g0V3ZyNjU2WDMzQTJzZnVrT01IS0ppTGVubE5YZWV0clVWUnhETGhON0c4bDZ5SlBSM211VDNydVZsZk1KaXN3bVVnd3pacXFXUGlubC9qZDd4cmtqMU9wK0U5Wkl6VTRMV1ptd3lTdG5CbTIiLCJtYWMiOiI3MDM0YjgzNDYxM2UzZTM4ODcyODIyYTk0Nzk2YmY5MWFmMjQxMTRlYTgyOWMxZDI5N2NlY2Y5Y2Q2NjFmZDgzIiwidGFnIjoiIn0%3D; expires=Tue, 17 Jun 2025 15:21:10 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6IllZMElpL3VFUCtURit0VTFhV0dvQ0E9PSIsInZhbHVlIjoiazRsQWRBeXU1alJueVo1VU9zQ2FGODhkRmRlc2JpRDk3ZklGbVJ6UG9ER1FXOFhKUk4wUHlsdUhkVjhqeHNEWEh4bklMMHBkVUorN3kzNDFocFhza2J0TWR1ME1yWnJVSDVMQTdKQlFvMFA5MzBSNzYwR21iK2RDTWN6eDdEWG5WME1OWHdTU05mdktCVHNlYUpBTlV0N1VTMGZKU3hCSWVKa2NDU2ZNQjNQZmNMNjc4MlFHcnZzN0xPVmxKbzhuSEpOM3R1RlRDRG54K1JUSGYyWWNtMktidm85bVdzZ0NwNEhnSTg2aGZVWGJxUWduWjJlTXBoV3dPSDFZZTFlTUQ0cGlOeUxSdld0TUl1ZzE2Nmo2T3lzUEpEQVNXRllyV2JPQkZQSXltSExGRXpYdXR6V2xXdWdIUWlHdkxNM1gxdjRqV1hIdU5BbzVIRWJOSEN4a1VqVHE5VHM0bXBqZWxVME8wOEVUSTJpZHBtMEZSblU4YU5ZZ0MrSzczcTVtano5SFdNT285cE04ZWhiL1BQNDlPM1I2bnJiSkNIanIvT3BvS0VlL1JaMUs2d0YvZFdtU0pvWExjb0FUTWJ5eEtpUVllVXoyU0dFbDFXUnZtbHgrUVpWWVE0UExRVlRKWUcwa1I1ZWh2cGZva01kS08vdEZ2cXJqYm8wc3J1cWwiLCJtYWMiOiI0NDJhNGI4ZDA3YjY1YTk0NzA0NjI0YjNhN2Y1ODIxOTU4MWRmOGE2ZjFlMDE5M2JkYjYzNzg3MTBjYmViNDU3IiwidGFnIjoiIn0%3D; expires=Tue, 17 Jun 2025 15:21:10 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Imh0UFVzQ1p4dTlhWlF1ZVlxUjNwbnc9PSIsInZhbHVlIjoieE9yNHhJSFAxWk4yKzdVbjhoa2ZpTE1rTUtrUGQvRVlHTitkVHFXVHRNYmhkd253MGs2dHYvdWt4UGRZemVyQTQ2MG5LVXZrV0xFYmszM081K0RSS0ZEMlkwYjljS1dZcFBBd20xTWY4R2hGOUo3bXloQm1TMU41b29Yc0YzMGh3YkNVMXZVVG5yTlA2NmdGT2syeVlHSFlkMS9Rc2hFa2hRb0pramxGcUdyWU1ZSUpQOFMvYmdsT1ZZMGNjcUJadE1TWDVBbkdjS3hVdHBDbnhrenMxNDB2ZTlSUFJJOWJJN09wa3lVL2VnWVFWOWR6ZE8xQjZRblJmbFFxZVJvNmhyeHN5T0xoM2ZqYnFtaU1qR0Q5S081ZHpaYW9jVStpMzBCekJQQVNEaWtlZWNMQ1BwdFM3b29qNzFxaUZHYUNJSTM5OTBKb1F5LzhtZ3gxNHdKOEJCYW5BZlB1aklkRSt6MU12N0QySUZCTCtpVFRuSVpUOThtVzBoM2IzTHd4a0MrdEd2REFoU0VpaUY2V1AvZ2g0V3ZyNjU2WDMzQTJzZnVrT01IS0ppTGVubE5YZWV0clVWUnhETGhON0c4bDZ5SlBSM211VDNydVZsZk1KaXN3bVVnd3pacXFXUGlubC9qZDd4cmtqMU9wK0U5Wkl6VTRMV1ptd3lTdG5CbTIiLCJtYWMiOiI3MDM0YjgzNDYxM2UzZTM4ODcyODIyYTk0Nzk2YmY5MWFmMjQxMTRlYTgyOWMxZDI5N2NlY2Y5Y2Q2NjFmZDgzIiwidGFnIjoiIn0%3D; expires=Tue, 17-Jun-2025 15:21:10 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6IllZMElpL3VFUCtURit0VTFhV0dvQ0E9PSIsInZhbHVlIjoiazRsQWRBeXU1alJueVo1VU9zQ2FGODhkRmRlc2JpRDk3ZklGbVJ6UG9ER1FXOFhKUk4wUHlsdUhkVjhqeHNEWEh4bklMMHBkVUorN3kzNDFocFhza2J0TWR1ME1yWnJVSDVMQTdKQlFvMFA5MzBSNzYwR21iK2RDTWN6eDdEWG5WME1OWHdTU05mdktCVHNlYUpBTlV0N1VTMGZKU3hCSWVKa2NDU2ZNQjNQZmNMNjc4MlFHcnZzN0xPVmxKbzhuSEpOM3R1RlRDRG54K1JUSGYyWWNtMktidm85bVdzZ0NwNEhnSTg2aGZVWGJxUWduWjJlTXBoV3dPSDFZZTFlTUQ0cGlOeUxSdld0TUl1ZzE2Nmo2T3lzUEpEQVNXRllyV2JPQkZQSXltSExGRXpYdXR6V2xXdWdIUWlHdkxNM1gxdjRqV1hIdU5BbzVIRWJOSEN4a1VqVHE5VHM0bXBqZWxVME8wOEVUSTJpZHBtMEZSblU4YU5ZZ0MrSzczcTVtano5SFdNT285cE04ZWhiL1BQNDlPM1I2bnJiSkNIanIvT3BvS0VlL1JaMUs2d0YvZFdtU0pvWExjb0FUTWJ5eEtpUVllVXoyU0dFbDFXUnZtbHgrUVpWWVE0UExRVlRKWUcwa1I1ZWh2cGZva01kS08vdEZ2cXJqYm8wc3J1cWwiLCJtYWMiOiI0NDJhNGI4ZDA3YjY1YTk0NzA0NjI0YjNhN2Y1ODIxOTU4MWRmOGE2ZjFlMDE5M2JkYjYzNzg3MTBjYmViNDU3IiwidGFnIjoiIn0%3D; expires=Tue, 17-Jun-2025 15:21:10 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1539894507\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1307597997 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">ixL6BN3l9tyyUscqj0hv99yKIy0daqhD43fAAWcL</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>16</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1307597997\", {\"maxDepth\":0})</script>\n"}}