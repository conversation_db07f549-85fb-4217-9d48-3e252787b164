{"__meta": {"id": "X10edcd4082808c68f377e9d211a82904", "datetime": "2025-06-17 13:33:54", "utime": **********.993948, "method": "GET", "uri": "/customer/check/delivery?customer_id=10", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.254, "end": **********.993987, "duration": 0.7399871349334717, "duration_str": "740ms", "measures": [{"label": "Booting", "start": **********.254, "relative_start": 0, "end": **********.902123, "relative_end": **********.902123, "duration": 0.648123025894165, "duration_str": "648ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.902137, "relative_start": 0.648137092590332, "end": **********.993991, "relative_end": 3.814697265625e-06, "duration": 0.09185385704040527, "duration_str": "91.85ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 44978408, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET customer/check/delivery", "middleware": "web, verified", "controller": "App\\Http\\Controllers\\CustomerController@checkDelivery", "namespace": null, "prefix": "", "where": [], "as": "customer.check.delivery", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FHttp%2FControllers%2FCustomerController.php&line=365\" onclick=\"\">app/Http/Controllers/CustomerController.php:365-378</a>"}, "queries": {"nb_statements": 2, "nb_failed_statements": 0, "accumulated_duration": 0.02625, "accumulated_duration_str": "26.25ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 16 limit 1", "type": "query", "params": [], "bindings": ["16"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/AuthManager.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\AuthManager.php", "line": 57}, {"index": 23, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 33}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.949832, "duration": 0.02549, "duration_str": "25.49ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 97.105}, {"sql": "select * from `customers` where `customers`.`id` = '10' limit 1", "type": "query", "params": [], "bindings": ["10"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/CustomerController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Http\\Controllers\\CustomerController.php", "line": 368}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.9818618, "duration": 0.00076, "duration_str": "760μs", "memory": 0, "memory_str": null, "filename": "CustomerController.php:368", "source": "app/Http/Controllers/CustomerController.php:368", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FHttp%2FControllers%2FCustomerController.php&line=368", "ajax": false, "filename": "CustomerController.php", "line": "368"}, "connection": "kdmkjkqknb", "start_percent": 97.105, "width_percent": 2.895}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\Customer": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FModels%2FCustomer.php&line=1", "ajax": false, "filename": "Customer.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "16", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]", "pos": "array:1 [\n  2044 => array:9 [\n    \"name\" => \"تجربية\"\n    \"quantity\" => 1\n    \"price\" => \"10.00\"\n    \"id\" => \"2044\"\n    \"tax\" => 0\n    \"subtotal\" => 10.0\n    \"originalquantity\" => 1\n    \"product_tax\" => \"-\"\n    \"product_tax_id\" => 0\n  ]\n]"}, "request": {"path_info": "/customer/check/delivery", "status_code": "<pre class=sf-dump id=sf-dump-1350533683 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1350533683\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-1787525572 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>customer_id</span>\" => \"<span class=sf-dump-str title=\"2 characters\">10</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1787525572\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1936 characters\">_clck=2gyfy%7C2%7Cfwu%7C0%7C1994; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clsk=1ti4td2%7C1750167218299%7C2%7C1%7Cl.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IlBSU1dpWU9VUVEycmNFSitlVzQveUE9PSIsInZhbHVlIjoiK1JSZm5lOVZ4THp6enFVclhuYWpKWlFJUE9Yc2gzQU5QeWdJQUJ6OHkweVJSTkwzZjJ1SXVuNjU3Q2c5SEtUbDN3NUVRRUdmRzJnMUFQa3crVkQ4bE5jUWNqcW0rMDJrMjlmTzI5U1FXTysrRXN4RG51bXlUM0JHUDZ5dzdhN1cvdXdXSzBUK1hrdklING9uVTdPZDhpM0lTeTEzZFduSkR4NUI1ZjkyOStqRnB4bTF5UWx3cVBWa0dmYVZxSi9EQjdwVlkzeTZaU0padTFaMzNyUUpmTzdob1JTRXZKUHhpRUI5R25yQU14M2JEQzltWUhTa3I1Q3lnd0VmWEx6ajFTbXB3YTRacElnQ3dkam01Ylhha05tWkZxZUV1SUJzT084WUR6Q2FLWTN6WWM2OEpieG5JeEs3aVVxdHUwSUx1TzN3cUlud3liTlVBcmFuQzE4YVBnWG5pNmZpcFgwZnEzL0RaTWI2NWNXa0NJeUdadHcveGVzZGVGUnA3TVovYS96bmxibnNnbGRyR2xLb3Q2L3V4aE1yM1ViUXlOV2R1Wkd2Sjl0WUVycmRLNXdrM0FxbytwNW1oMlNab3RhRmZCaXd0SGRpdnhMNUtGRXlPS0gvc25kdU5UUEM0eTBhQWdNSmJTUTRmYlFhVmtmNEdveGpkNnJxeWJEUkV0UTIiLCJtYWMiOiI2OGVmMWEyODZiYzkxOTZmYThlZmI4MzA0OGVhYjhlYWJmOWZkY2YxMzg5NmM2NDU4ZTgzYTI1NDM3ODRhYzgxIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6InV0VWJYakxIYjF1K1NRbUZBQ29qc1E9PSIsInZhbHVlIjoiYTZDdStENDhaNFNhK0hpVTN3VVc1L3Y1Wnpod1pxRk0xTVAwc29aZFoyRVJwRTNiZ3Bpbk9HT2RjcE55ak9IdTBRdElWeUxMcXV3V0lOYktRbDhkblh5bUg4RmxwTFRPTnpMSHJiT2pmaTEwRGwzS1NrOE5GeTg1aGwwTE9wNXMzQUZwMk9wSFdHOGRJZXVmME43dXFIL3lzS2RVeVNpK2R5aGxSV1pTZ3Rxd1RHTEcvS2VIVVV0WmtQS3cxZGVTUFZkejcySGlDRml2TFRzVGhaNDJKUVBQWjZDaTlQdDZEWURQdnhOc2JoMWtSMlA2cmVnUklDdDZjZ2ZFRkFzMHpoa2Y2R3BDeUc2MmdqM2ZIR2lyNUpxT3pFdVU5WXY1VGpCUlQzbktpSy9jdGN5R21kQ0RsVnExQ1Q5WjlqbW5iSHJ2ZmxzRnhsYXlFUGJUK0Y0dXkxUGY3b3duQ05wcnllRDk5eGdpWkU4RXZDREEzME9GYURpUSt6RlczZVFQd2VQWjUwWjZDSGh1S1ZCT0d0Vmd6OXhnVEdTVFhlSE50YURuRE9uUkFpd3JsN1NXYjlSamRhMDdCOTRCL2NQakxQM3pUTXorQnRpS1J0ejloL05OYWEycGI5Nk5KNjhCYkxlWWdUTGRtSk5oS2lrcXNQV1lzdUZPSEF3b05kQ1giLCJtYWMiOiI1N2Y0OGNlZmZjNTZlNGRkMjgzZDU2OGYwZWEyMTQxNDUyNzJiM2NkYTU3ZTkyYzNmZTdiMWU0YzUzMDExZDMxIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-88709803 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">VloaUQCBjpDKrel7nHeAzB1sTLdTMwteSos1MmAN</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-88709803\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1140071333 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 17 Jun 2025 13:33:54 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IjZ4NEpBb25zODQ1YWNGeUhJM3dZWWc9PSIsInZhbHVlIjoiaUVjVUpUNDh6UHQ4bVFLTWpzSTBMa0JxQ1dVUjExUFlWRGdLYkRnQ0kxVllyR2xUT1NUdzV0V0hibGcyU0N1aTVMZUtDU1RIOHM2S3pHajhUcTMvZGpuNXJFWDZnVnA2dUQ5NWlxV0dXRElETGpudkp5WEV5WGJkcUZSWVd0M1BML3d3SlF1K2dtaXN6M1NWRkxnTDdFOS8zVjBUR0RyWVJqTklUY0ZPYVJZRllzYWFDQmdjL05rbVBnaWgwdEc1R2dpOVNQMWVFclNZZ1hvQWRqOS9VSzRmNW5IYmR2VzlUN3RVNFNIRWFmWkt1YktPVUhtRS9hQzVMZW1KMWthbEN2bU5wYWwyQlZUVjQ1YnU2alJRM0d0WllzR2I2c21ibm05cStYNVdDZlhaUTEwYVJSZjU4MGdMR3ZEa3VVTkcyQVBwSkJPWnA2NGRFakRiaERTNjFxUXQ1V3JFVVJkUThzNmlDTHhGRENkTC8rQUprVlZCSElyZTgzUmJpV1FnRUJqY3NrdWJ3TzhKREEweXBnUGN0c3hmRzNQSGgzaDVrY0hXQXEzL1p5YkVxdld3U3V4VGZlZm03Wk5TU20wWG1pd1ZmWjgwRUs3R2wzeWZEODQ4c0hUb2dodkdhQVBWRlVFd3V3bTdJZGkyN2xINFJ4Q0FGQ1RpV0VrTG9WaTgiLCJtYWMiOiJhNjdkODE1MGRhYzFmM2IwMTRkODNjZGE2NDQyYjdjNTY1NmU0ZjZhYzQ0ZTZiMWNhNWJjMjU2OTkxNzgwYzUyIiwidGFnIjoiIn0%3D; expires=Tue, 17 Jun 2025 15:33:54 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6IjVhbTNLM0d1ZVRhVUVqMHZENVpYanc9PSIsInZhbHVlIjoiMVdDM0pIRnBuanAzOGxURDVOaHh3TFF2QTVGcnRHV2VkVkJ2N1BpcnlHN0FtK3NxZkFRM2ZETWJQUVk1OS9RNUwwemNNR21vTStVdTFhaFYvVEdrRERWbDh6cDlocEtSaVFVTVdLUzdVRnpVVERoRFIxamswQTcvdHRIOU5NZ2xzTnZsNnJMN01nbXA4VXJuR3hIb0F3NUxXQ3dOaDFoV1BudnRYTWI2aTRZbHp4d2ZDUytsTzVQRXlETG1laGs4ZmhtWHJyb2tYM0l4T3VNUjNLVUtHa3VEM3cxZHRHYjFOby9YcmVKcWw3WWd6azhkSGFVbTRDRlhwa1IySHpjYjRpSTV4ano3bG9sRjF5M0dDMVVVVjlxaEFmY2s1Y2pIM2RqNGcwZ0o3VU81c3VEcDBQZU0wSE1aRUhRamZuMmpYK3NNdnZxU0FzbXFoL2wwV2U1eUx2UVFGbWMxY3BWVzlINEVtNXVzODZ5c1lNV292RkpSVWYrNXNhdTNIdnVHUTJuM0FMNGg2MFYxdXJ1OEdQc1JNNVNaNVc1U01QVEZsTVUzWDdNWUZUMUhWSm1OVWoyK054aVRYazhmUER5T0RibDhxMHFnbVlBbVNEaXFQNmlabWx0SUV3N012SWJINlZqV0k5QWhqWFg4REI4YlBGbUlIa01jSGRCcEtta1YiLCJtYWMiOiI2ZGYzZjc1ZGUzNmZjYjA3YjgyNTI3OGM5NzQ2MmY5NTE0YmM5YzY0YTBlZDVhMDNiYjUwM2ZiNGE3NjA4ZmVjIiwidGFnIjoiIn0%3D; expires=Tue, 17 Jun 2025 15:33:54 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IjZ4NEpBb25zODQ1YWNGeUhJM3dZWWc9PSIsInZhbHVlIjoiaUVjVUpUNDh6UHQ4bVFLTWpzSTBMa0JxQ1dVUjExUFlWRGdLYkRnQ0kxVllyR2xUT1NUdzV0V0hibGcyU0N1aTVMZUtDU1RIOHM2S3pHajhUcTMvZGpuNXJFWDZnVnA2dUQ5NWlxV0dXRElETGpudkp5WEV5WGJkcUZSWVd0M1BML3d3SlF1K2dtaXN6M1NWRkxnTDdFOS8zVjBUR0RyWVJqTklUY0ZPYVJZRllzYWFDQmdjL05rbVBnaWgwdEc1R2dpOVNQMWVFclNZZ1hvQWRqOS9VSzRmNW5IYmR2VzlUN3RVNFNIRWFmWkt1YktPVUhtRS9hQzVMZW1KMWthbEN2bU5wYWwyQlZUVjQ1YnU2alJRM0d0WllzR2I2c21ibm05cStYNVdDZlhaUTEwYVJSZjU4MGdMR3ZEa3VVTkcyQVBwSkJPWnA2NGRFakRiaERTNjFxUXQ1V3JFVVJkUThzNmlDTHhGRENkTC8rQUprVlZCSElyZTgzUmJpV1FnRUJqY3NrdWJ3TzhKREEweXBnUGN0c3hmRzNQSGgzaDVrY0hXQXEzL1p5YkVxdld3U3V4VGZlZm03Wk5TU20wWG1pd1ZmWjgwRUs3R2wzeWZEODQ4c0hUb2dodkdhQVBWRlVFd3V3bTdJZGkyN2xINFJ4Q0FGQ1RpV0VrTG9WaTgiLCJtYWMiOiJhNjdkODE1MGRhYzFmM2IwMTRkODNjZGE2NDQyYjdjNTY1NmU0ZjZhYzQ0ZTZiMWNhNWJjMjU2OTkxNzgwYzUyIiwidGFnIjoiIn0%3D; expires=Tue, 17-Jun-2025 15:33:54 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6IjVhbTNLM0d1ZVRhVUVqMHZENVpYanc9PSIsInZhbHVlIjoiMVdDM0pIRnBuanAzOGxURDVOaHh3TFF2QTVGcnRHV2VkVkJ2N1BpcnlHN0FtK3NxZkFRM2ZETWJQUVk1OS9RNUwwemNNR21vTStVdTFhaFYvVEdrRERWbDh6cDlocEtSaVFVTVdLUzdVRnpVVERoRFIxamswQTcvdHRIOU5NZ2xzTnZsNnJMN01nbXA4VXJuR3hIb0F3NUxXQ3dOaDFoV1BudnRYTWI2aTRZbHp4d2ZDUytsTzVQRXlETG1laGs4ZmhtWHJyb2tYM0l4T3VNUjNLVUtHa3VEM3cxZHRHYjFOby9YcmVKcWw3WWd6azhkSGFVbTRDRlhwa1IySHpjYjRpSTV4ano3bG9sRjF5M0dDMVVVVjlxaEFmY2s1Y2pIM2RqNGcwZ0o3VU81c3VEcDBQZU0wSE1aRUhRamZuMmpYK3NNdnZxU0FzbXFoL2wwV2U1eUx2UVFGbWMxY3BWVzlINEVtNXVzODZ5c1lNV292RkpSVWYrNXNhdTNIdnVHUTJuM0FMNGg2MFYxdXJ1OEdQc1JNNVNaNVc1U01QVEZsTVUzWDdNWUZUMUhWSm1OVWoyK054aVRYazhmUER5T0RibDhxMHFnbVlBbVNEaXFQNmlabWx0SUV3N012SWJINlZqV0k5QWhqWFg4REI4YlBGbUlIa01jSGRCcEtta1YiLCJtYWMiOiI2ZGYzZjc1ZGUzNmZjYjA3YjgyNTI3OGM5NzQ2MmY5NTE0YmM5YzY0YTBlZDVhMDNiYjUwM2ZiNGE3NjA4ZmVjIiwidGFnIjoiIn0%3D; expires=Tue, 17-Jun-2025 15:33:54 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1140071333\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>16</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pos</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-key>2044</span> => <span class=sf-dump-note>array:9</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"6 characters\">&#1578;&#1580;&#1585;&#1576;&#1610;&#1577;</span>\"\n      \"<span class=sf-dump-key>quantity</span>\" => <span class=sf-dump-num>1</span>\n      \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"5 characters\">10.00</span>\"\n      \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"4 characters\">2044</span>\"\n      \"<span class=sf-dump-key>tax</span>\" => <span class=sf-dump-num>0</span>\n      \"<span class=sf-dump-key>subtotal</span>\" => <span class=sf-dump-num>10.0</span>\n      \"<span class=sf-dump-key>originalquantity</span>\" => <span class=sf-dump-num>1</span>\n      \"<span class=sf-dump-key>product_tax</span>\" => \"<span class=sf-dump-str>-</span>\"\n      \"<span class=sf-dump-key>product_tax_id</span>\" => <span class=sf-dump-num>0</span>\n    </samp>]\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n"}}