{"__meta": {"id": "X14063711e754e6263618bc24b1476498", "datetime": "2025-06-17 13:27:49", "utime": **********.444535, "method": "GET", "uri": "/product-categories", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1750166868.67507, "end": **********.444556, "duration": 0.7694859504699707, "duration_str": "769ms", "measures": [{"label": "Booting", "start": 1750166868.67507, "relative_start": 0, "end": **********.290671, "relative_end": **********.290671, "duration": 0.6156010627746582, "duration_str": "616ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.290682, "relative_start": 0.6156120300292969, "end": **********.444559, "relative_end": 3.0994415283203125e-06, "duration": 0.15387701988220215, "duration_str": "154ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 49310352, "peak_usage_str": "47MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET product-categories", "middleware": "web, verified, auth, XSS, category.access", "controller": "App\\Http\\Controllers\\ProductServiceCategoryController@getProductCategories", "namespace": null, "prefix": "", "where": [], "as": "product.categories", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FHttp%2FControllers%2FProductServiceCategoryController.php&line=203\" onclick=\"\">app/Http/Controllers/ProductServiceCategoryController.php:203-229</a>"}, "queries": {"nb_statements": 6, "nb_failed_statements": 0, "accumulated_duration": 0.03669, "accumulated_duration_str": "36.69ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 16 limit 1", "type": "query", "params": [], "bindings": ["16"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.343723, "duration": 0.02859, "duration_str": "28.59ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 77.923}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.387101, "duration": 0.0007099999999999999, "duration_str": "710μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 77.923, "width_percent": 1.935}, {"sql": "select `permissions`.*, `model_has_permissions`.`model_id` as `pivot_model_id`, `model_has_permissions`.`permission_id` as `pivot_permission_id`, `model_has_permissions`.`model_type` as `pivot_model_type` from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `model_has_permissions`.`model_id` = 16 and `model_has_permissions`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["16", "App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 326}, {"index": 21, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 566}], "start": **********.41128, "duration": 0.0007, "duration_str": "700μs", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:326", "source": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php:326", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=326", "ajax": false, "filename": "HasPermissions.php", "line": "326"}, "connection": "kdmkjkqknb", "start_percent": 79.858, "width_percent": 1.908}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (16) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 312}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 26, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}], "start": **********.41511, "duration": 0.00065, "duration_str": "650μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 81.766, "width_percent": 1.772}, {"sql": "select `product_service_categories`.*, COUNT(pu.category_id) product_services from `product_service_categories` left join `product_services` as `pu` on `product_service_categories`.`id` = `pu`.`category_id` where `product_service_categories`.`created_by` = 15 and `product_service_categories`.`type` = 'product & service' group by `product_service_categories`.`id` order by `product_service_categories`.`id` desc", "type": "query", "params": [], "bindings": ["15", "product &amp; service"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/ProductServiceCategory.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Models\\ProductServiceCategory.php", "line": 116}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/ProductServiceCategoryController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Http\\Controllers\\ProductServiceCategoryController.php", "line": 206}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.423126, "duration": 0.00391, "duration_str": "3.91ms", "memory": 0, "memory_str": null, "filename": "ProductServiceCategory.php:116", "source": "app/Models/ProductServiceCategory.php:116", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FModels%2FProductServiceCategory.php&line=116", "ajax": false, "filename": "ProductServiceCategory.php", "line": "116"}, "connection": "kdmkjkqknb", "start_percent": 83.538, "width_percent": 10.657}, {"sql": "select count(*) as aggregate from `product_services` left join `product_service_categories` as `c` on `c`.`id` = `product_services`.`category_id` where `product_services`.`type` = 'product' and `product_services`.`created_by` = 15", "type": "query", "params": [], "bindings": ["product", "15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/ProductServiceCategoryController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Http\\Controllers\\ProductServiceCategoryController.php", "line": 209}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.4313629, "duration": 0.00213, "duration_str": "2.13ms", "memory": 0, "memory_str": null, "filename": "ProductServiceCategoryController.php:209", "source": "app/Http/Controllers/ProductServiceCategoryController.php:209", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FHttp%2FControllers%2FProductServiceCategoryController.php&line=209", "ajax": false, "filename": "ProductServiceCategoryController.php", "line": "209"}, "connection": "kdmkjkqknb", "start_percent": 94.195, "width_percent": 5.805}]}, "models": {"data": {"App\\Models\\ProductServiceCategory": {"value": 26, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FModels%2FProductServiceCategory.php&line=1", "ajax": false, "filename": "ProductServiceCategory.php", "line": "?"}}, "App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 28, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 1, "messages": [{"message": "[ability => create purchase, result => true, user => 16, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1549710490 data-indent-pad=\"  \"><span class=sf-dump-note>create purchase</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"15 characters\">create purchase</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>16</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1549710490\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.421451, "xdebug_link": null}]}, "session": {"_token": "ixL6BN3l9tyyUscqj0hv99yKIy0daqhD43fAAWcL", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "16"}, "request": {"path_info": "/product-categories", "status_code": "<pre class=sf-dump id=sf-dump-818371464 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-818371464\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-1908297708 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1908297708\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1212452909 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1212452909\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-151666670 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">ixL6BN3l9tyyUscqj0hv99yKIy0daqhD43fAAWcL</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1937 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=1pubyvl%7C2%7Cfwu%7C0%7C1993; _clsk=25xk9k%7C1750165707106%7C8%7C1%7Cl.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6Imwvb3hwWW5TTEgrUm85VVljNEl2Qmc9PSIsInZhbHVlIjoiWHRJdXNybG03aFErN2FNL1haeHo4bHdCV0NJeUYxK0VPelN6QjNaOVFUemQ3VE9XckE4UVF2ZDd4UXQvNG83RXJ0Tld2RURXMTh1T212cWRwRlE1b2FhbXUwaWlZaEdmL040VEZVTWhmWk9UbWFybjBRdlUrdS9jRXNlQjMrejI0STNRSTlxbTRMd0ZEamI2cVc3d0tHU05nRmtGRnBJTmlDTUxDYzZ4bFJBTkhmZGg5UlUvdGYwNDExcWI0a0hxY1dzZWNXSk9KVU1TaFBMQkM2QTRXa2U0OFFXcXArUUlsK01DWG15dGJkbE1QQ1h0YjBpU3JnK3NHL3M5YkxOWU1VdkkvcjRrYlFKOVNGdVlLVHI0c0x0bGRvT0NYWG1XUUx1cGErV3B1THd5Qm96OEREU20zaHdhUlN5NDNHS2dBaHFnamVWbVR5cnRaSEZ0d3NvNXBDSkdnSXB5L0tZNEQ5Y2lXejFveStkb3VwUVBFbUxqTDlkbDloQ0M1UjRzSXBpTWxPWTlPdVlhNUdJbm5ERzJTKzZiK3hneDJTOTRXbWJrUlJ3UnlvNlRISm9nSXRnQUlOK0RzZ0MvZHpZTVZ5TUJocGlyemloR3poL1B4VUpSQjNNSWNJd0hQQWt1N2c1bk9veitJK00xZUdvV3RqWlZKenhPYTR3TzFjdDEiLCJtYWMiOiJmYTE3OWViNmIxYWFkZTViZjViYTY2YTg0ZjlhZGFjMDc0MGFiNjQ0MjNhNzA2MThhMTczODBlMjUzOTA3NGY0IiwidGFnIjoiIn0%3D; test_session=eyJpdiI6IlFJZ09ybnE5NmtXYk8za3FPSnVmRnc9PSIsInZhbHVlIjoiMjllYno0ZS95WTRuV216NmRCOFY4cnBqbHBPZFdBMUo0K1c4c1BJQ3lGSDgvMFd1TVEzYnorMUREaGVDWUlpQldaclVsNG03K2lUdEVISzgxRGRIL3MxNm9uajhUNTJHOWRmam1raVBaTktrdGtpd1d2bm9mQUFlUGhVcy9qVWZ4bVFZRkxCRjQ0MURkZ3FVTlpKK2t4WTl4RG11ZlArOFJ5ODFFejNHeWQ3dWxkOXNibE4zRi9tOWxjclc4cXM4UHRMaFRQWkNkSDFoSmtTU2RicXpCVFRPd2MyZDlsQ29rZ1JmYnpMZXhCUzRmYkdMNU5VN29lTmpDVnlvQytRaENld2l1T3h5eGYrZDVHYUx5QjR6NDFrMmZFT1BBZG5qNWtiU0dZZHQ3cnQ1QmphVkR4VnVjdGJxWFRMRDljdWRFUFRsL0dSNm5EQ1VMSEd3K1IyNkM4YmtyeHBKRnVXeTRCUmMwK2tVN2xuN1BPMFhUV3dxR2tQNEJ0OHNZQzh4OGxjc0phbDRINzRLNkQ1WVFCT1ljOUp5cG0wbGtvYkFlTERZTWZ6VjZja1pyTWNSZjFaMXNQNTFYUEdCZjRNUzNMY0p5T0Z0VUxtaE9uQnh1aUFpeDRBRnQ4V3ZjTzVwUmZobDVqb1BaMjgvdjhzSFlIcnlEQjg4RkV4Sm9uankiLCJtYWMiOiI2ZGQ0NTRlOGY3OGNiMjMzMmNlYWE3NzhmOTQzNjY3NmQ4ZWIxY2YwMDNlOTEzMzA3Y2RiMDM0MDgzMDRjZmE3IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-151666670\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1429261948 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">ixL6BN3l9tyyUscqj0hv99yKIy0daqhD43fAAWcL</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">coJ97Z8YyNCeFgF4h0jUL1PrpYBjauguIcJbJbm8</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1429261948\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 17 Jun 2025 13:27:49 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IlQ2OHZTUGhUS0hDeXY0Z0MyVVhTSlE9PSIsInZhbHVlIjoiSnZha3doL3BOTVB6YW92WWZUWHRKdG5oaHlrRUdxSEJNakl4aisrbUtVREk2WlUvNGN4V1BJK2tMdkgzME9KVUU2ZmFBRDdkdkFUVVA3bU1yM0ZGUTErV2dBcHd2dmE0ZHhIWVo0SUtLRENoUlRLL09XRWVhM0lWYzVZd2pJOG9ER3NxRFE0YllPSU9tSjhqUU9yMklUUURQaTZXOCtmc2NBbXJTMlROUHk5THJ2UTZQUTRtVHpmZ1Q0TVdoTmwySEdlcGxoYVk4V0p0OUk2S3crT2kxclphVFRYbjhJbDJlMlNLWUJLbmsvTTVnT2xMS2VFN3pvN0hhdlZFZTN5MjB3Y21oSks2ODl6c1Y4Z056clgwSEhRY2I0Tmo1Rkl6d1JnNzNiY3JsWWR4K1c2ckE2YlIzWEtNR3htbXVoRTdLd3NsMEtxZ25WMTlGYmFkTjh1NEZiU3ZzSG1oWTRoVTRaR3gvbUFjNmdHYlVFZjhlK0FNUjhLajBJZXFzZEdjUVFHRVhtWm81M3VJUFBWdFYxMlpaeHZwUVhoWEc4QkRQTVhzazFxb05Vd2wzRGR2YnFtRmVuMjZFei9pczlCUU03WmlXcUNHTEVPc3hYZW5qeEpHOXZLd2RmLzZCbDl3Z0IyTkErM08zR25VMDFjWWNDbFdQdkh2QXdXNm5hZWkiLCJtYWMiOiI5MWZmMzdhOTBiNTliN2YyZGMzZjM1ZGRjZjAwMjVlYzY4YWQzYmU4OGM3YTFkMjUzNTk1ZGRlMTQ5ZGE3MTQ3IiwidGFnIjoiIn0%3D; expires=Tue, 17 Jun 2025 15:27:49 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6IlE3aHdqaDk3SXVNY29ucURnTzVLbmc9PSIsInZhbHVlIjoieTc4NU8wYlJGemlvZTR5RG5RUldOalM4aTJnTVdIOHY1UFNOOU5BeGtsbFZaRjRxMVpiM3M1TlpEdXR5Q3Y2WHlwdXRLWGRSeTBCQUV3QmhGVlZFWWptOEtOUUlyY1J0Zlo4Q1JPK2JnRmdhK2g3bVpHODNxNzdlU01Gc3BYK0hQSmJ0Mm02bFRPc2pmakhJcnkxYnYwNG9UR1NxSkVUUWxQOENmYUFlUFpJTm5NVysvT2NKbnplaGg0Nys5VjdQS2ZMeXhKYkR2RTkycmdEcEgyWHUvNnFXb1VzSFRhTFNHVE1VSG90Q25iRGNDWjRDWU9PbzhKdnRHVEpvdmJNYnJkeTkvT2dCcFplTE5TRGdKcUZKdFB6ay84R0NNYTZRMUIzNkttaHNHcVNjTVRqaVUwK3RMN005c2VUbThBNk9PTUdoTXFoSkZ1OVJrUXZNU3hMb2YxUDQwMXB4OUJzMTVEem9FSDNzWEd3N2xqb0NjT2lrOUFUWTBXL0g4YnI2cGJ3VDRjTEQ3Q0dWUEUyUXBxR0huWURqNzJQdUNYVndlTjlieVhYU29IWmhyUmR6bVVIVnJaVFdtRENEUHdZeHFJTnBpTEJPazdodnZjbS9ham1mWk5oWkVXQTl5SnNTbGRYOTB5T2hWa2U5U0VlVVF2VE5QSEIrWXFGdFYrdXYiLCJtYWMiOiI1ZGI5NDc1NjUxOGY2OTdlNGVlM2UyODVhMjVjN2M3MDFmMGZmNDg5N2MwMTZmMWZjMWZkMWFhN2RmMjM5OGU4IiwidGFnIjoiIn0%3D; expires=Tue, 17 Jun 2025 15:27:49 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IlQ2OHZTUGhUS0hDeXY0Z0MyVVhTSlE9PSIsInZhbHVlIjoiSnZha3doL3BOTVB6YW92WWZUWHRKdG5oaHlrRUdxSEJNakl4aisrbUtVREk2WlUvNGN4V1BJK2tMdkgzME9KVUU2ZmFBRDdkdkFUVVA3bU1yM0ZGUTErV2dBcHd2dmE0ZHhIWVo0SUtLRENoUlRLL09XRWVhM0lWYzVZd2pJOG9ER3NxRFE0YllPSU9tSjhqUU9yMklUUURQaTZXOCtmc2NBbXJTMlROUHk5THJ2UTZQUTRtVHpmZ1Q0TVdoTmwySEdlcGxoYVk4V0p0OUk2S3crT2kxclphVFRYbjhJbDJlMlNLWUJLbmsvTTVnT2xMS2VFN3pvN0hhdlZFZTN5MjB3Y21oSks2ODl6c1Y4Z056clgwSEhRY2I0Tmo1Rkl6d1JnNzNiY3JsWWR4K1c2ckE2YlIzWEtNR3htbXVoRTdLd3NsMEtxZ25WMTlGYmFkTjh1NEZiU3ZzSG1oWTRoVTRaR3gvbUFjNmdHYlVFZjhlK0FNUjhLajBJZXFzZEdjUVFHRVhtWm81M3VJUFBWdFYxMlpaeHZwUVhoWEc4QkRQTVhzazFxb05Vd2wzRGR2YnFtRmVuMjZFei9pczlCUU03WmlXcUNHTEVPc3hYZW5qeEpHOXZLd2RmLzZCbDl3Z0IyTkErM08zR25VMDFjWWNDbFdQdkh2QXdXNm5hZWkiLCJtYWMiOiI5MWZmMzdhOTBiNTliN2YyZGMzZjM1ZGRjZjAwMjVlYzY4YWQzYmU4OGM3YTFkMjUzNTk1ZGRlMTQ5ZGE3MTQ3IiwidGFnIjoiIn0%3D; expires=Tue, 17-Jun-2025 15:27:49 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6IlE3aHdqaDk3SXVNY29ucURnTzVLbmc9PSIsInZhbHVlIjoieTc4NU8wYlJGemlvZTR5RG5RUldOalM4aTJnTVdIOHY1UFNOOU5BeGtsbFZaRjRxMVpiM3M1TlpEdXR5Q3Y2WHlwdXRLWGRSeTBCQUV3QmhGVlZFWWptOEtOUUlyY1J0Zlo4Q1JPK2JnRmdhK2g3bVpHODNxNzdlU01Gc3BYK0hQSmJ0Mm02bFRPc2pmakhJcnkxYnYwNG9UR1NxSkVUUWxQOENmYUFlUFpJTm5NVysvT2NKbnplaGg0Nys5VjdQS2ZMeXhKYkR2RTkycmdEcEgyWHUvNnFXb1VzSFRhTFNHVE1VSG90Q25iRGNDWjRDWU9PbzhKdnRHVEpvdmJNYnJkeTkvT2dCcFplTE5TRGdKcUZKdFB6ay84R0NNYTZRMUIzNkttaHNHcVNjTVRqaVUwK3RMN005c2VUbThBNk9PTUdoTXFoSkZ1OVJrUXZNU3hMb2YxUDQwMXB4OUJzMTVEem9FSDNzWEd3N2xqb0NjT2lrOUFUWTBXL0g4YnI2cGJ3VDRjTEQ3Q0dWUEUyUXBxR0huWURqNzJQdUNYVndlTjlieVhYU29IWmhyUmR6bVVIVnJaVFdtRENEUHdZeHFJTnBpTEJPazdodnZjbS9ham1mWk5oWkVXQTl5SnNTbGRYOTB5T2hWa2U5U0VlVVF2VE5QSEIrWXFGdFYrdXYiLCJtYWMiOiI1ZGI5NDc1NjUxOGY2OTdlNGVlM2UyODVhMjVjN2M3MDFmMGZmNDg5N2MwMTZmMWZjMWZkMWFhN2RmMjM5OGU4IiwidGFnIjoiIn0%3D; expires=Tue, 17-Jun-2025 15:27:49 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">ixL6BN3l9tyyUscqj0hv99yKIy0daqhD43fAAWcL</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>16</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n"}}