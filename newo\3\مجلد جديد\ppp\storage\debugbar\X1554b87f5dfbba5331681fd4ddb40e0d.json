{"__meta": {"id": "X1554b87f5dfbba5331681fd4ddb40e0d", "datetime": "2025-06-17 13:33:33", "utime": 1750167213.577749, "method": "GET", "uri": "/cookie-consent?cookie%5B%5D=necessary", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.270372, "end": 1750167213.577774, "duration": 1.3074021339416504, "duration_str": "1.31s", "measures": [{"label": "Booting", "start": **********.270372, "relative_start": 0, "end": **********.877672, "relative_end": **********.877672, "duration": 0.6073000431060791, "duration_str": "607ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.877687, "relative_start": 0.6073150634765625, "end": 1750167213.577776, "relative_end": 1.9073486328125e-06, "duration": 0.7000889778137207, "duration_str": "700ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 51365720, "peak_usage_str": "49MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET cookie-consent", "middleware": "web", "controller": "App\\Http\\Controllers\\SystemController@CookieConsent", "namespace": null, "prefix": "", "where": [], "as": "cookie-consent", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FHttp%2FControllers%2FSystemController.php&line=2387\" onclick=\"\">app/Http/Controllers/SystemController.php:2387-2439</a>"}, "queries": {"nb_statements": 1, "nb_failed_statements": 0, "accumulated_duration": 0.01737, "accumulated_duration_str": "17.37ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Models\\Utility.php", "line": 46}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Models\\Utility.php", "line": 78}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/SystemController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Http\\Controllers\\SystemController.php", "line": 2390}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.939721, "duration": 0.01737, "duration_str": "17.37ms", "memory": 0, "memory_str": null, "filename": "Utility.php:46", "source": "app/Models/Utility.php:46", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FModels%2FUtility.php&line=46", "ajax": false, "filename": "Utility.php", "line": "46"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 100}]}, "models": {"data": [], "count": 0, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "KSCsI6gWVzXFI1KEKZvZuoX2S6RILcyTiSY1GsfV", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]"}, "request": {"path_info": "/cookie-consent", "status_code": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-729736388 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">necessary</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-729736388\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-318242560 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-318242560\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-2068333163 data-indent-pad=\"  \"><span class=sf-dump-note>array:15</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/login</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1936 characters\">XSRF-TOKEN=eyJpdiI6IkQweHMwK0dmQzIwbDFMSXZwM2VYM2c9PSIsInZhbHVlIjoibVdRaE1RSXJCNjg2RmZLR1B2SjNyYzI2WkRqa0hoUVVIUFp2M1pxQ2htRjZuLzY5VzhsaUJoTExIeWFBQWxOOUw5MElJUlk4QXl3VTJFQkhQcnFkTGRlTjlwRXlIM2daZ0tMaWlROUhHTlpmRjRsSHpaUXlTM2ZoUU5ZQWNiVTJRUHQ1UkxITEE3Skd5d2MraUJ0Q0Q2OWM1bzMrSThNZE40YWNHQmh3ZjZ0cGNhdWZ0ejNyMDh1MDZoemVtbDMvVStJRk5seGhXU3hmVE4rOW1CaTgvV1Z4VnFQVEQ1cktOUVJZS2xOMjUzVEFVeloyZjBvd2xDMVR6aSs3dVJWUWZGelBhUWczTFY1QzFPZDZUOFlsR1JoV2pVa1JyWFhHQmtRazFSSWo2cEl0cVFIS3A0QVNnMitGYlhmY29rS2ZNOUZJS0JaVlltSDVmNzE3cmx1YVVJRlNkY01PRFVUTUQyQ1NCV3ZSbTlUN2ZTUVFGeFpwU09VazR3Z3ZPciswdHNyVXdMNzV6clNkZGVIbnkrRnVOTlQ5dTJQZGlSOGF6L0NveGRqNklSaFgrVktkc2pBN2dJcThMaG9zQnF3WGFCZDNIQXpKemNjb2c3V0ZzbSs2SFRIbk1GUkZpbDFETE1ZQVZrTDAvZTZpQlZwNWd0cXhIcHp0cWhsY0gwaE8iLCJtYWMiOiIyMDBiYTYyZDcyZGU1YWVjYmM0MTdlOTFjM2IxNzc3NGIwYTk3YjNmNWU0OWQ0MjNlN2U3NWViYjk3MWRlMmRkIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6ImdjMDlKc1cxNitaUzVNVTZrVXA0RWc9PSIsInZhbHVlIjoiUmZXNjltN0lNcGQrMXBRbVZuTFdSamcxN1BqK3oxbG9SSCswUHdvSXlQUG1iK01taG1oUzREQitPUEtteGVXZ243WWZCWWh0TVhRMmRFR0hoc3o0YUIwalNLbWo1U3FjN2hwZm53NGh2WkYwM1BpMmJERnFpMmViMDVDQmowb2FGUUdUNjFqYXBMMGxuK2wyeWxCamN2eFRUT09QdFBYeHZDNWlGTHlJNDJBWkJPQmMrNTR6NEsyWlJyVklOZitJWGxvMWRrVzRCYXVSTGdYYU5JNTZ1dHJzdENvL0FwV1EyU2p6YVhOSHViSVhMaWtHd1M5YytVMWF4OWNMSmtGWGVYVHJDM0RVMlpaSGljd3Y1THY2dVZpS2FpNTM2Ym91M09MY0ZUY0tFTUdjQ1NkdlFmY3c4NVVkWHpPTHQ2Qlh0YlJESnVFUGNJNTBmWTYrbVFGdDhNMWRYRVpBSjhpd21adFZndjRqUUtzVndNbUhzRUVnejNkV25JNUNSdmdDVzZCbWIzakxhRUlldGdnWUtET2JqNDY1eTE2UU9GTGxTY1dySkNWZ1V5dm9hdWYzaUxtLzFHTFZLczFiWmJNNHFOMDdGTXMxRExXVFhGRnNSSmlZbk9VQTlRVVlVMmhWY1NrSHRFNTV2MjZENHdiamh1cEo3N2pyeVJyNHQ4QjgiLCJtYWMiOiIxODQ2NWNkNDQ1Nzc4ZGRiOWQyZmYzMDBlMzNjMzQxMDU0M2ZhZDM2NGYxNTI3ZTU3MDg2Mjk2NDEzYjA3ZmMwIiwidGFnIjoiIn0%3D; _clck=2gyfy%7C2%7Cfwu%7C0%7C1994; _clsk=1ti4td2%7C1750167180417%7C1%7C1%7Cl.clarity.ms%2Fcollect; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2068333163\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-968453293 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">KSCsI6gWVzXFI1KEKZvZuoX2S6RILcyTiSY1GsfV</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">d80nLUIYlyXXuOSDB3tFqV9U8qArlPq6K2fUObBE</span>\"\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-968453293\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-2098865580 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 17 Jun 2025 13:33:33 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IlJyV21Kemp6UnRqVGExa3dBbE1sR2c9PSIsInZhbHVlIjoiMFZOamtlR1lyUWsvR2VweFp1blhQYU5Fb0JZcnlHWkhRMkdRYUxqOGJHRGZPSCt6RGtWcXgrRHpMU3owak9tVVZWV1M4NGNvK0o3ZHZ1Rzh5bU5RU2EycUt3K25na2RTQUF1ZUlJcmJPZWI0NW5DOEVZSTZUWUNGWldkWVRhL1hhZnEzZXVYeHRLZkdSUkZ6SmdsVmR3RlJ2SFpsaTlLZzJZaEhrN0wxTHJEeGJEcHhrQ3VSaUE5RE9hakZaSnNNbkZWTHlLOFZBcHdlNGVBWVNUbXRsYU1UaGQ4NWoxSE5Cb0VVRnZwYmNzdkVndVNCK1ZEQUQ5UEgzWFBzQW9pUHZkVUsycWw2NytNOCtrMjNBS1kxV3lLOUxKWHB4YVd2aXRYNWJTVTNTR1UwZkNaK2hGNEptOU5ETS9heHVJQUhCZFVLUFVFKzN5V05lS3B2Z0VOMHhVYkdtcjlpWW9rUXNxWTZDN050RHJ6c2wyQjc4RUd5UE0xZGNvY1FDOVZYWklhSXlhRU1kMnBiN0RaeHhzSDBmSVg2bkovQjRtNUI1akZJMnFlSVkvZi9uUGtSSDJkSTRPM00xSmlROWh6UFhQU2V0YWpwdU1XKzZ2ZjBSYTBBMWs3S01KTkdRam1VSGhsU2syS3FwSWRDM2p1MEhBT3Yxa0ZkeXk1RkVoaVMiLCJtYWMiOiIyNzAwNDBlNTkyZTU5YWMyYWM0ZTNjYjQ0ZjgwNzEzZTE0YzdkMDk0ZThjZjI1YjY4OWRiODE4Y2EzZTJhYTAzIiwidGFnIjoiIn0%3D; expires=Tue, 17 Jun 2025 15:33:33 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6IjE0RkdWVjhNOTI1bS8zbzZlRndER3c9PSIsInZhbHVlIjoiUFdIMitNdHJOOEZaVStVTTlDT1JOSFRGK0E3a01IcmNGR2I4SnkwSFNRNXhWTXZPYnBuZ1NkQ0pQcDFUTllmU2VHck1qelEweGxUM2xGY0pqWWErdHRTYUplYkpNL1lEbVBxVzkwcU03L0FDOXEzSStwV3B5SkRhTDJuRjlqSEFtNkZFTUZBQm1RMlNNYjVhMTE3RkNTV2djZVZDd0pkdk1IckxaYnA0NmkzeVcvakJydy9EVW53UXlQTWZJY2JyQWppcFBCQWsxUExvQUZnOFpCMFloSGVJQTkraktaQ0FCbi9FN2c0dkxLS1JCRzhCQnEzZzFRc0V6S29BV1FWRTA2WUI1eVowL3RpTEtla1NNdk02M3Z1cE42NXBwL3hLWnd4MEhDTitzLzczTWQrQmZHOEp1SnZGZnhTRG9CdUpGUktuYkIxTFVWSzBSUmRtbytyU0c5Z1R4SGUrbEsyZFA4amhNS1BsVWdoMnA1MlovaWxqMGtpNGFzUllQUTZmUms2eWh3RlFWYVRlTnlxbjc4d3FXUXk1SXlGVzlUQWVBVnN2L21kbFFZT3d4SXBjcno4U3creDcyVHRtVkh4TG9Bc3ZGNm5rZEt2Tm1lNEJUc1Vod2tiK3VvNWhRL09EbnAvVVZYdjNVQWt2R0VBTDFLOFNCZm1zRzZUN3grUTMiLCJtYWMiOiIzYTRmMDI2NTljZWY4YThkZWYxZjI3MjQ0OGE1NzRhODRlMjA2MDliNDA4NzgyYTE0NTAzZWYzYzFkZWI3MDcwIiwidGFnIjoiIn0%3D; expires=Tue, 17 Jun 2025 15:33:33 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IlJyV21Kemp6UnRqVGExa3dBbE1sR2c9PSIsInZhbHVlIjoiMFZOamtlR1lyUWsvR2VweFp1blhQYU5Fb0JZcnlHWkhRMkdRYUxqOGJHRGZPSCt6RGtWcXgrRHpMU3owak9tVVZWV1M4NGNvK0o3ZHZ1Rzh5bU5RU2EycUt3K25na2RTQUF1ZUlJcmJPZWI0NW5DOEVZSTZUWUNGWldkWVRhL1hhZnEzZXVYeHRLZkdSUkZ6SmdsVmR3RlJ2SFpsaTlLZzJZaEhrN0wxTHJEeGJEcHhrQ3VSaUE5RE9hakZaSnNNbkZWTHlLOFZBcHdlNGVBWVNUbXRsYU1UaGQ4NWoxSE5Cb0VVRnZwYmNzdkVndVNCK1ZEQUQ5UEgzWFBzQW9pUHZkVUsycWw2NytNOCtrMjNBS1kxV3lLOUxKWHB4YVd2aXRYNWJTVTNTR1UwZkNaK2hGNEptOU5ETS9heHVJQUhCZFVLUFVFKzN5V05lS3B2Z0VOMHhVYkdtcjlpWW9rUXNxWTZDN050RHJ6c2wyQjc4RUd5UE0xZGNvY1FDOVZYWklhSXlhRU1kMnBiN0RaeHhzSDBmSVg2bkovQjRtNUI1akZJMnFlSVkvZi9uUGtSSDJkSTRPM00xSmlROWh6UFhQU2V0YWpwdU1XKzZ2ZjBSYTBBMWs3S01KTkdRam1VSGhsU2syS3FwSWRDM2p1MEhBT3Yxa0ZkeXk1RkVoaVMiLCJtYWMiOiIyNzAwNDBlNTkyZTU5YWMyYWM0ZTNjYjQ0ZjgwNzEzZTE0YzdkMDk0ZThjZjI1YjY4OWRiODE4Y2EzZTJhYTAzIiwidGFnIjoiIn0%3D; expires=Tue, 17-Jun-2025 15:33:33 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6IjE0RkdWVjhNOTI1bS8zbzZlRndER3c9PSIsInZhbHVlIjoiUFdIMitNdHJOOEZaVStVTTlDT1JOSFRGK0E3a01IcmNGR2I4SnkwSFNRNXhWTXZPYnBuZ1NkQ0pQcDFUTllmU2VHck1qelEweGxUM2xGY0pqWWErdHRTYUplYkpNL1lEbVBxVzkwcU03L0FDOXEzSStwV3B5SkRhTDJuRjlqSEFtNkZFTUZBQm1RMlNNYjVhMTE3RkNTV2djZVZDd0pkdk1IckxaYnA0NmkzeVcvakJydy9EVW53UXlQTWZJY2JyQWppcFBCQWsxUExvQUZnOFpCMFloSGVJQTkraktaQ0FCbi9FN2c0dkxLS1JCRzhCQnEzZzFRc0V6S29BV1FWRTA2WUI1eVowL3RpTEtla1NNdk02M3Z1cE42NXBwL3hLWnd4MEhDTitzLzczTWQrQmZHOEp1SnZGZnhTRG9CdUpGUktuYkIxTFVWSzBSUmRtbytyU0c5Z1R4SGUrbEsyZFA4amhNS1BsVWdoMnA1MlovaWxqMGtpNGFzUllQUTZmUms2eWh3RlFWYVRlTnlxbjc4d3FXUXk1SXlGVzlUQWVBVnN2L21kbFFZT3d4SXBjcno4U3creDcyVHRtVkh4TG9Bc3ZGNm5rZEt2Tm1lNEJUc1Vod2tiK3VvNWhRL09EbnAvVVZYdjNVQWt2R0VBTDFLOFNCZm1zRzZUN3grUTMiLCJtYWMiOiIzYTRmMDI2NTljZWY4YThkZWYxZjI3MjQ0OGE1NzRhODRlMjA2MDliNDA4NzgyYTE0NTAzZWYzYzFkZWI3MDcwIiwidGFnIjoiIn0%3D; expires=Tue, 17-Jun-2025 15:33:33 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2098865580\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-2086491542 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">KSCsI6gWVzXFI1KEKZvZuoX2S6RILcyTiSY1GsfV</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2086491542\", {\"maxDepth\":0})</script>\n"}}