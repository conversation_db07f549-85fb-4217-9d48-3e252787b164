{"__meta": {"id": "X1ed630283678dbccd225f19d16a8f835", "datetime": "2025-06-17 13:50:05", "utime": **********.566787, "method": "POST", "uri": "/warehouse-empty-cart", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1750168204.96271, "end": **********.566806, "duration": 0.6040961742401123, "duration_str": "604ms", "measures": [{"label": "Booting", "start": 1750168204.96271, "relative_start": 0, "end": **********.493169, "relative_end": **********.493169, "duration": 0.5304591655731201, "duration_str": "530ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.493191, "relative_start": 0.5304811000823975, "end": **********.566809, "relative_end": 2.86102294921875e-06, "duration": 0.07361793518066406, "duration_str": "73.62ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 46326408, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST warehouse-empty-cart", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\ProductServiceController@warehouseemptyCart", "namespace": null, "prefix": "", "where": [], "as": "warehouse-empty-cart", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FHttp%2FControllers%2FProductServiceController.php&line=1663\" onclick=\"\">app/Http/Controllers/ProductServiceController.php:1663-1673</a>"}, "queries": {"nb_statements": 2, "nb_failed_statements": 0, "accumulated_duration": 0.00549, "accumulated_duration_str": "5.49ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 16 limit 1", "type": "query", "params": [], "bindings": ["16"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.537708, "duration": 0.0044800000000000005, "duration_str": "4.48ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 81.603}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.555519, "duration": 0.00101, "duration_str": "1.01ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 81.603, "width_percent": 18.397}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "16", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]"}, "request": {"path_info": "/warehouse-empty-cart", "status_code": "<pre class=sf-dump id=sf-dump-1573108788 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1573108788\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-1425319059 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1425319059\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-732138341 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>session_key</span>\" => \"<span class=sf-dump-str title=\"3 characters\">pos</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-732138341\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-213198512 data-indent-pad=\"  \"><span class=sf-dump-note>array:19</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">15</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1936 characters\">_clck=2gyfy%7C2%7Cfwu%7C0%7C1994; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clsk=1ti4td2%7C1750167218299%7C2%7C1%7Cl.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IlR2SFFSd3Z6RjFKSjNuTjZ2Z21hcXc9PSIsInZhbHVlIjoiZEVvOG13bkVYNE00ZHMvMkhsY0p6aHZPRmJBak0yYURKc1puNnJVMEk3TWYvNHl5TmlpbHVab2lIcHJkRC9aSXpOQk16OGgvdHpWS0gzTm1mWU5DcTU2UlBXRXhlMlF2S2trSk83b0xjVGJRdUtBYnAzd2QwM1p4WGJuRlB5MGVzTW5IdU5iMUlrZThIcmpWZFRDM2xXZnRiYWtsWUZTSEdlWWtZZmt1VjhadkFGekN1QWJsSmtCRGJud0MycGlpT3dIRUl0WjgxbWJ2R3hnWEVSeEdvclY3K09CQmtTbDBsRkdWN3BQQ1F6YXBlYldoNEdoalduQzArZ09EVjYxT1VETXFvVVhFTllPTFVkTmJwU1hlellIbGcwNHRIeXJBSkp5NVZuN2xpR25QTm5FUjUvRjhmcWJEUHJSZzNKa3dVRUtoWVlZRXRhTXgzbTAxNjZ1Z1dDWlVLbnRwbjF6disxbkNTdG81Umxib08rQVgrdDFUbyt1VlppSE85aTRoLzVMNVpwdjMxRVJoRGZRcWZPcUdScGlBb1EwdDB3UnZKTytJWkxxVkJxVWZMU200N3VweVF3aGlLWjUzWnVmaWVIUU9ZOElDY21KbmZvRHo2WGY4cmQwSitQWlRDSVRYRGl0a2dueGsvRjlCRGYwY0JWUkxnZjRTenVUQnVjQVAiLCJtYWMiOiJiNWU3YzViMzZlMGYyOTUyZmJmMmM1Y2FmZDdmZmEwN2ZhNDNiMWNlMjEyMDIzN2U0ZDEzN2QyZGEyMzk5NzJiIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6ImlZV2FBZUNKbmJ0MWMvVXZvS2lUQ0E9PSIsInZhbHVlIjoieTJvOTlpbWFkNlBhOFp3eGRFUll2S2FrVWtOUXpqK1VqcStwSTBOa2c0ay8zdlVaRU01QkszelJtZEdwOWVBeU5TMnE0NXVRa3FWcm02VW5hU1lpdmkwU2FCbkJsMXRCMkFJZ0tSSEV2UWNNZERhdUtleGxQWEtVenJacmNoKzE2SUkxR05ZZXYvRlRlaVNKRUNMRldKMUJVZUNpeTh3eXpHenBJVjRicmJMZ0tuaEgzajIyZTZCU3QvUnpIUDYyVFZwQWU0aGdtYWNyVHV2OWFvY0o1VzliS01YWmJNUnZyRXRPSE9SL1IrZHNDd1BsbWYxS3dCT2FSM1hvUXUzejdTdWxERnYyVWNubERnMVAyNldqV201WEJaRGRNU0toZlNLaUdhTWlaL2hHb0F0NjBXVWtjcmVKQU5IaHltcU0wWUdBd3lMTFBqMVhGU2ZRSUlxek91RXo5alRHemF6dzAxU3Qvb09jQnBzYmlhS2t1dm1NZVhvVmwzTGZnc0doVDRlcG5XWlU0WnJLY1RmUzZvMUJwZVlQM1BqRTJaMU9aQUxUTFowTU1uSDV0R0hWWlB4dEpFaHZzdW5OUjY4eW9uZ3FJZWR6cU5qeGtvSmwwZW5hUTNRcWJPZHMzdE12ZnNXR2E1S2JSa0FndzhOMFEzWDJoRkFNa1VJS0hGeTAiLCJtYWMiOiI2Yjc3NTNkYjdkM2U3NGJiMTRiYzk1NjRiNThkZTZmODg5ZGYwYjAyNzA3N2M0ODIwNDM5MGY1MTQ5MzlmM2YyIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-213198512\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">VloaUQCBjpDKrel7nHeAzB1sTLdTMwteSos1MmAN</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-826227944 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 17 Jun 2025 13:50:05 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IkQySnhJVzhSbmNGdExmZXJ2NFM1THc9PSIsInZhbHVlIjoiNWFjSyttSmRMd0dPeDltOWluRk1Wa0FUWFNIN0ZreFRudnFkdjYyamp5cVFyQldIMTRwOVY2ZXdhUEJJa0EycW1Rdk14WHQrRkhjcEdobThIQmhBZWl4b3pmU0VsaHRIM1pVdU9GRkhHclM4dHIrYmVvQkZ4MWpNanhsN0puK2plaFR1NTRuRTgxRG9CZllNTlJxWEpTZVFIRzFzdGdMOTFwZ0Q3R2o3dzlWd3FNckVYWWlucHNieGJ6Q1VBN1psZmVqS3ptck16d0hLZWJwRytlSit5b1RXY1NoVi9ZTzQrdUpKK2RpMzh2VjFTdWlURnYvVDR3ZzVXOFgrMXhTUjBicC9uWHhzd3o2NmEwYlBHdStLbElnQnpQZnl2UEIybW04OVJOTGdKUGREQjJGU1ZrQi9od0RXM3VuK25HUUtPbnhJaUwxV1dCWXFaOGNEQjdoelRIUXhCeVlWZzk2a1htNmh6TGhoUXdTdzlXYjRPeEhqN1FTZHpEVFBhVTNIc1dsQThTQ1hKODJHMWxrdG91QnpLT0VjdW4wMUN4MkN4QXJwYkdzZ1JSNVhKVVU5TVRoMTRXZm9KVDgrM3BUT0JRckxSRUF0MGZkOThTb1A4K2JSUTJ0Q2tkaGlGSW8rS3dmS2ZUa1RBN2xVRXpIU0d4QnZtdjAxVVhjSmlxTDAiLCJtYWMiOiI4NTVlOTQzYzJhYTNlMjVmODBlZGU0NGFkOTc0MDNhZmZhMmI2MDA4OGM3NWEzYmY1NWFiNTNiMmUwYjYwY2E4IiwidGFnIjoiIn0%3D; expires=Tue, 17 Jun 2025 15:50:05 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6Iko4L25DSjg1YzFxc3NhOHlyWm9sUFE9PSIsInZhbHVlIjoiYzhKQ3BhYVBwUVZYOGFKdTBRYWFiQlY5Y3pMS3crNDRDS3dKQjFYUDlmREZGRnpzWmc4dW5aS0hSc1AzWDhCcjg4Mi85b3ZzWTV5QzJFVmRWbmZIeHV3RERjS05PRjBNTGo3d1FORmtwV3JySHZNeVEvYjRxUi8wOUt6ZHp4WW5JRVlJK0RvRlEyVVlGcitUUDhUTzZSRGk3dk1HQWxJUlB4T2p5YWI1c1htVkFNYjlXa2dpQ2JnanJpTGZ3Y1RNbDVZaHVRLzZkTExPWHlra3FWSkxRa0crbkJkSENFODFoSzFXMXYvaWV3UFphSnNrL2cwYmhLVFYvQVJKaFhpZFpSNkNrNG16bkFVdmNid2UzWHdkZ2FxanloZGJNMitGVWZFSU54emwrM1hLUmtGY1ZadCtycFZKdHA5S1RhNFgrTnlxaDFNNlBWbUszQUkxSFova0k0NDZUZUV5WExRZHBlVndYVVNmT0UxdzNPU2M0VTF3NStsOS9KR1VhdGRsRTlBVGQrbmU2OXo1dkdxZVFSbGlDWnV1UDIxTFk5Zk1od0FpZFFQTDFYV1lJZm5FSzBmYzMyZVhLS3BPYmtPTWFjeXIrRmFaMUc5cXU1M29sSmpZMm8yNEM4OWYyNGhzLzdLd1hzbm9IVWlKZklMVkVlR1Z3UHlJWkxWUjBmYjEiLCJtYWMiOiI0YzIyOWRlYjkwYTJhNjNhNDI5OTZjNzAyMDMzZmJjMjFhZGIyZDM4NDVhZDY5MDI5OTgxNDc1OWQyZTVlYmQ1IiwidGFnIjoiIn0%3D; expires=Tue, 17 Jun 2025 15:50:05 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IkQySnhJVzhSbmNGdExmZXJ2NFM1THc9PSIsInZhbHVlIjoiNWFjSyttSmRMd0dPeDltOWluRk1Wa0FUWFNIN0ZreFRudnFkdjYyamp5cVFyQldIMTRwOVY2ZXdhUEJJa0EycW1Rdk14WHQrRkhjcEdobThIQmhBZWl4b3pmU0VsaHRIM1pVdU9GRkhHclM4dHIrYmVvQkZ4MWpNanhsN0puK2plaFR1NTRuRTgxRG9CZllNTlJxWEpTZVFIRzFzdGdMOTFwZ0Q3R2o3dzlWd3FNckVYWWlucHNieGJ6Q1VBN1psZmVqS3ptck16d0hLZWJwRytlSit5b1RXY1NoVi9ZTzQrdUpKK2RpMzh2VjFTdWlURnYvVDR3ZzVXOFgrMXhTUjBicC9uWHhzd3o2NmEwYlBHdStLbElnQnpQZnl2UEIybW04OVJOTGdKUGREQjJGU1ZrQi9od0RXM3VuK25HUUtPbnhJaUwxV1dCWXFaOGNEQjdoelRIUXhCeVlWZzk2a1htNmh6TGhoUXdTdzlXYjRPeEhqN1FTZHpEVFBhVTNIc1dsQThTQ1hKODJHMWxrdG91QnpLT0VjdW4wMUN4MkN4QXJwYkdzZ1JSNVhKVVU5TVRoMTRXZm9KVDgrM3BUT0JRckxSRUF0MGZkOThTb1A4K2JSUTJ0Q2tkaGlGSW8rS3dmS2ZUa1RBN2xVRXpIU0d4QnZtdjAxVVhjSmlxTDAiLCJtYWMiOiI4NTVlOTQzYzJhYTNlMjVmODBlZGU0NGFkOTc0MDNhZmZhMmI2MDA4OGM3NWEzYmY1NWFiNTNiMmUwYjYwY2E4IiwidGFnIjoiIn0%3D; expires=Tue, 17-Jun-2025 15:50:05 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6Iko4L25DSjg1YzFxc3NhOHlyWm9sUFE9PSIsInZhbHVlIjoiYzhKQ3BhYVBwUVZYOGFKdTBRYWFiQlY5Y3pMS3crNDRDS3dKQjFYUDlmREZGRnpzWmc4dW5aS0hSc1AzWDhCcjg4Mi85b3ZzWTV5QzJFVmRWbmZIeHV3RERjS05PRjBNTGo3d1FORmtwV3JySHZNeVEvYjRxUi8wOUt6ZHp4WW5JRVlJK0RvRlEyVVlGcitUUDhUTzZSRGk3dk1HQWxJUlB4T2p5YWI1c1htVkFNYjlXa2dpQ2JnanJpTGZ3Y1RNbDVZaHVRLzZkTExPWHlra3FWSkxRa0crbkJkSENFODFoSzFXMXYvaWV3UFphSnNrL2cwYmhLVFYvQVJKaFhpZFpSNkNrNG16bkFVdmNid2UzWHdkZ2FxanloZGJNMitGVWZFSU54emwrM1hLUmtGY1ZadCtycFZKdHA5S1RhNFgrTnlxaDFNNlBWbUszQUkxSFova0k0NDZUZUV5WExRZHBlVndYVVNmT0UxdzNPU2M0VTF3NStsOS9KR1VhdGRsRTlBVGQrbmU2OXo1dkdxZVFSbGlDWnV1UDIxTFk5Zk1od0FpZFFQTDFYV1lJZm5FSzBmYzMyZVhLS3BPYmtPTWFjeXIrRmFaMUc5cXU1M29sSmpZMm8yNEM4OWYyNGhzLzdLd1hzbm9IVWlKZklMVkVlR1Z3UHlJWkxWUjBmYjEiLCJtYWMiOiI0YzIyOWRlYjkwYTJhNjNhNDI5OTZjNzAyMDMzZmJjMjFhZGIyZDM4NDVhZDY5MDI5OTgxNDc1OWQyZTVlYmQ1IiwidGFnIjoiIn0%3D; expires=Tue, 17-Jun-2025 15:50:05 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-826227944\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-126605709 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>16</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-126605709\", {\"maxDepth\":0})</script>\n"}}