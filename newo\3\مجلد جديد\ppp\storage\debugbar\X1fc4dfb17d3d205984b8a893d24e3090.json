{"__meta": {"id": "X1fc4dfb17d3d205984b8a893d24e3090", "datetime": "2025-06-17 13:55:02", "utime": **********.119265, "method": "POST", "uri": "/event/get_event_data", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1750168501.519811, "end": **********.119287, "duration": 0.5994760990142822, "duration_str": "599ms", "measures": [{"label": "Booting", "start": 1750168501.519811, "relative_start": 0, "end": **********.020331, "relative_end": **********.020331, "duration": 0.5005199909210205, "duration_str": "501ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.020347, "relative_start": 0.5005362033843994, "end": **********.11929, "relative_end": 3.0994415283203125e-06, "duration": 0.09894299507141113, "duration_str": "98.94ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 46458080, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET event/get_event_data", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\EventController@get_event_data", "namespace": null, "prefix": "", "where": [], "as": "event.get_event_data", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FHttp%2FControllers%2FEventController.php&line=317\" onclick=\"\">app/Http/Controllers/EventController.php:317-345</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.01978, "accumulated_duration_str": "19.78ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 16 limit 1", "type": "query", "params": [], "bindings": ["16"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.068219, "duration": 0.01754, "duration_str": "17.54ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 88.675}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.101681, "duration": 0.00111, "duration_str": "1.11ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 88.675, "width_percent": 5.612}, {"sql": "select * from `events` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/EventController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Http\\Controllers\\EventController.php", "line": 327}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.108975, "duration": 0.00113, "duration_str": "1.13ms", "memory": 0, "memory_str": null, "filename": "EventController.php:327", "source": "app/Http/Controllers/EventController.php:327", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FHttp%2FControllers%2FEventController.php&line=327", "ajax": false, "filename": "EventController.php", "line": "327"}, "connection": "kdmkjkqknb", "start_percent": 94.287, "width_percent": 5.713}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "16", "_previous": "array:1 [\n  \"url\" => \"http://localhost/hrm-dashboard\"\n]"}, "request": {"path_info": "/event/get_event_data", "status_code": "<pre class=sf-dump id=sf-dump-856885082 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-856885082\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-1850234294 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1850234294\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-377468956 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-377468956\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-537859250 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"30 characters\">http://localhost/hrm-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1936 characters\">_clck=2gyfy%7C2%7Cfwu%7C0%7C1994; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clsk=1ti4td2%7C1750168386913%7C7%7C1%7Cl.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6ImRRMHJZUjdOYnphbG8ydEVKNmxrVUE9PSIsInZhbHVlIjoiWm8wSkdxTVZwbjRFWVBGeEpLajROYWJ6RURHMzNWRFNKMTFrbFdYZE9QZjF3NEJyN2p5Y3phcHhuQ3JzaG5WQnJiWUdOVXNqdjNyVk5RRzh2dnIxUmxYazFoT2p4YlQ5WVFtS1VpZVlqMnRjbFhVNGRlbW5QM1lSY3kzOGZXN1hKaENuR3JHSFl6VHczclNIT1E5Nmt6ZXV2L2paQ1hYVXMwZlR0V1JBWldOS1NNTnpidnR2SEY2UVNTZlAzd1B3Mk9nQUx2R29ZWjNOTDdWRzRFbWlZUXhZNHpwWlVmdzl0dWZvMDBXWGIzVkp2N3grQXZGM2FsczA4MHlJUGFFWUZKYzl3cG5oMXJwV2tBendsaGZXaUplQjYwOTdPUDJReWpWZGE0MVlDV0RnMHpreW1NVERUS0ZWdWU0QXVNWW9lVjc2WDduV2VpQmtpVEJtOW93cGxGVUx1c24zZGUxVWtzdkc4MktBRkMxczdyQWxZY0lSMVpGWm9oeVVZTC9iUktnWjZFajR4UlkwdHdaMGdnaElTS3pwZTR0WmtwNUxRdVpZWk1xaFZaSytXSnZxcklGWndheGJhcGJaL2FoMlZMTnRJU0REQnE1ZHhxVVhMYnFRRFBnWWk5SXhIVTZKMm55Znlic2hXbVgwclVEcUp6S1BlN1hQVTZla3BGd2UiLCJtYWMiOiIxOWJkN2I3MGQwZTBlM2I4YzYzMzI3NzE4YmE3OGEyMjc5NzMwYjE5NGNhZGEwZTQ2YTRlNzk3YmJmN2RjODc3IiwidGFnIjoiIn0%3D; test_session=eyJpdiI6IkdiZjJQVlNPdVQvTlBob1BZWFdMZWc9PSIsInZhbHVlIjoib0RlOEZlTFA0enZ5Tm9zNTNDbklCbk9sSCsvY3lSUHZJUEtBMVFtbFd1bHQ2VldsVGNnUEFFZFdWdWlGamRYa2MxcmY3ZytCM01yaEhhaFExM3kxQWQzcDFoc2pIelB5ZUx3WGhTb1NJY3ovRUR6OU8zM0ttSkxQamVBQ1FMNnZ1aXFVTElPYjBoQUpYdDlmZlFtdG9zNXE3aVpIYk5RTmd3dmh3ZENsWVFYa0JPY2tEYTFhVjhIeW5DcjVsRWFXanMzcTRIN2pyUmZBSUd5aU1yWU5xRFFIbFhkTFBHeXhORGJoZURRZDJXeXJnZTYwWjVWOVVIMlk5Nlh5YklQYk5kejllSDlxODh5YVhZRzlUYWQzZVZBRGVyK2F0dFVNMXduOWRaa1duSWZWVnRTUGRZNERsdUZtZmx5RGV5cVp0eGpFdzhnT3F2ektHcm5rbjBITHhzVHRqVU9jdU14QUQ0QWR0cFFraCsrRmJTdmpBOW03VmNvRUV2L29QWW1nOW1kS3dVd0ExTHFHcjhNMk1YdWdNaUFKQUtTRHhtaDRvaUF2Y3dsbHVCSjZLVVBhcW9HR3BQN3dpbytKRVpEcHpuS3d2Q2s2K0M1ajFlVitVaUtOeGZ1NHVUcW9GaWdzTlVFRkNsMElSUm00M29abDdvWW5mQXN0ajVmVVRydlUiLCJtYWMiOiIwMDUyZTU3NDFhMjgzYzYyYjhjNWY5MTY5NjgyZDBkMTQ1MWQ2OTcxZDBjM2Q0ZDU4NjhiOGFmNzk1MmUzZjM5IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-537859250\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-478350532 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">VloaUQCBjpDKrel7nHeAzB1sTLdTMwteSos1MmAN</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-478350532\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1970148424 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 17 Jun 2025 13:55:02 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IlJRUnoyR0U2bGlXQXl4RDdSRHNia0E9PSIsInZhbHVlIjoiZVRScGZscDEwVmVlR0NHbjhVN1ArRmwwWDY4ejd4TVlEZGpncnJlTkxaVkQzUEg4UjgvV1BuSkw2RGl2LzIyMExKeE9EWW9IRDc0dVpuQzlnbEJzbDdTbDF6VzVLNVdZWVlZV3ErMXJvMDBrblRzbnVrcm5RMHl0bVdoQmoxK3pjcGtKUUx6ZGZaaWRWYUJvcE5yRUtNUlNoZ0VRK2xYTVlMNk5ENTUzZE1UaHV3dHoxdTFOWDV3RllwRHpFcUhUS0JxbHduYysvQVF0UTZyaWxNVllxbzBVMEgxYVJ2Z25PT21EYVY1Sld5akwwTTJVQkh4elpEOXh6bkEwYUpwN21xczA2NmZHMFJDZzNYdUZ4ejFsR3JOVktDWHpRdjdhWkVUNUpMS2JRdTJJRGNWU29sS3hkNlJMbEZrdmhZZW40SExJSThvOTJLZjJUU010bUdhL3VUck9PU0ZiRCt0ZVp6bTd2RldhVTFsenRia0JyYVZDZ245U3doU2UyZ0lraHc2VWl0NFJpQTNacDZxVlJRL1JzK0RnRStNSmhrUUthNDZUdjVUenJZbTVPdERhWStQK1ZJSGp6b3RlOUNnSFVuRXg0R0Zxb1hhS0RDTGVlNTlITGFjanQzaDRKV1ozN1NqVEVFS0hwWWI4MEFmR3Fyb2FaMk1ncTVxc0RhN0giLCJtYWMiOiJmMThiNmUwZmU2MDQ3YTQ2Nzc1NjIzY2MzNjI0MWZiMDM1MTBhYmQ3ZGYxNTEyNjIwYTNhYzFjMjQ4ZjMzNjVlIiwidGFnIjoiIn0%3D; expires=Tue, 17 Jun 2025 15:55:02 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6ImZGaEgyTUtFclVrZ1RSclBMTldXQ2c9PSIsInZhbHVlIjoib3NDNG5oR1dLcldoSHEzL3dXYjBBREY4UEl3YVNiYUxLZ3VYQU1TdDFLZGN3cVUxeHZ6bFVSVm9WUmxSZ0JZRURzVXZhN1JGRThtVkZORThxVmFWSDFjVHMxd1NOblR6RjVwVlJMV1AwMmNBZHZXUDN2ZTZZSlpCS0V4SzhqVkFFdS9uMENjcjcrZFZ1cmVrMnhzaVBJVkl3RGlmL011b0h2RVN3ZHVtOHJ5b2ViRTZMeHA0UHRwNjF4dHpNajZ2Q2RFTGNBTGNQYkN1Sm1Bc1RiMFlUeldXKzRabTJNRURTZ1EzSS85NVMveUJJNFBkT1M3NTFFUVNFVkxORVRsQ0tNUTEwK1kyeHNvREpQbjBMVExyTFkwWFZHeitHM2pnUy9LUEoweHZPRHcybFBaVTM4YzZTaVdtcFJJVGNPdUZpN09zeW56RjZldGxlRG13M0RMSE1ZdC9SOXlNc2g1Z3dvb1R0OTZqUGdjb0N2ajI4a1FZbEVzbHRDMXBwNzZHb0h6ZmxMejBGRUV0cnUwTEtPTk9nVnBjWW1PVTc5S3VmdjZtOS9GSEJ0dFBuanpDbEZKUGllMVJySU15c29aZ0JIdk8rd2FWdDZLY0FHN3JObkVXNFdwdXNEZnhtZ3NpUVVtdm10cEJLZWVrT2dkd3ZrWGxmRmZMdVM5MmFWRjgiLCJtYWMiOiIzZWY1Njc3ZjM1M2YyODllMTY1Mzk5N2JjMWYyNzk5ZTRhMWUwYjBlYWViNDY3ZTllMTlkYzQ2Mzc1ZTY5NmViIiwidGFnIjoiIn0%3D; expires=Tue, 17 Jun 2025 15:55:02 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IlJRUnoyR0U2bGlXQXl4RDdSRHNia0E9PSIsInZhbHVlIjoiZVRScGZscDEwVmVlR0NHbjhVN1ArRmwwWDY4ejd4TVlEZGpncnJlTkxaVkQzUEg4UjgvV1BuSkw2RGl2LzIyMExKeE9EWW9IRDc0dVpuQzlnbEJzbDdTbDF6VzVLNVdZWVlZV3ErMXJvMDBrblRzbnVrcm5RMHl0bVdoQmoxK3pjcGtKUUx6ZGZaaWRWYUJvcE5yRUtNUlNoZ0VRK2xYTVlMNk5ENTUzZE1UaHV3dHoxdTFOWDV3RllwRHpFcUhUS0JxbHduYysvQVF0UTZyaWxNVllxbzBVMEgxYVJ2Z25PT21EYVY1Sld5akwwTTJVQkh4elpEOXh6bkEwYUpwN21xczA2NmZHMFJDZzNYdUZ4ejFsR3JOVktDWHpRdjdhWkVUNUpMS2JRdTJJRGNWU29sS3hkNlJMbEZrdmhZZW40SExJSThvOTJLZjJUU010bUdhL3VUck9PU0ZiRCt0ZVp6bTd2RldhVTFsenRia0JyYVZDZ245U3doU2UyZ0lraHc2VWl0NFJpQTNacDZxVlJRL1JzK0RnRStNSmhrUUthNDZUdjVUenJZbTVPdERhWStQK1ZJSGp6b3RlOUNnSFVuRXg0R0Zxb1hhS0RDTGVlNTlITGFjanQzaDRKV1ozN1NqVEVFS0hwWWI4MEFmR3Fyb2FaMk1ncTVxc0RhN0giLCJtYWMiOiJmMThiNmUwZmU2MDQ3YTQ2Nzc1NjIzY2MzNjI0MWZiMDM1MTBhYmQ3ZGYxNTEyNjIwYTNhYzFjMjQ4ZjMzNjVlIiwidGFnIjoiIn0%3D; expires=Tue, 17-Jun-2025 15:55:02 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6ImZGaEgyTUtFclVrZ1RSclBMTldXQ2c9PSIsInZhbHVlIjoib3NDNG5oR1dLcldoSHEzL3dXYjBBREY4UEl3YVNiYUxLZ3VYQU1TdDFLZGN3cVUxeHZ6bFVSVm9WUmxSZ0JZRURzVXZhN1JGRThtVkZORThxVmFWSDFjVHMxd1NOblR6RjVwVlJMV1AwMmNBZHZXUDN2ZTZZSlpCS0V4SzhqVkFFdS9uMENjcjcrZFZ1cmVrMnhzaVBJVkl3RGlmL011b0h2RVN3ZHVtOHJ5b2ViRTZMeHA0UHRwNjF4dHpNajZ2Q2RFTGNBTGNQYkN1Sm1Bc1RiMFlUeldXKzRabTJNRURTZ1EzSS85NVMveUJJNFBkT1M3NTFFUVNFVkxORVRsQ0tNUTEwK1kyeHNvREpQbjBMVExyTFkwWFZHeitHM2pnUy9LUEoweHZPRHcybFBaVTM4YzZTaVdtcFJJVGNPdUZpN09zeW56RjZldGxlRG13M0RMSE1ZdC9SOXlNc2g1Z3dvb1R0OTZqUGdjb0N2ajI4a1FZbEVzbHRDMXBwNzZHb0h6ZmxMejBGRUV0cnUwTEtPTk9nVnBjWW1PVTc5S3VmdjZtOS9GSEJ0dFBuanpDbEZKUGllMVJySU15c29aZ0JIdk8rd2FWdDZLY0FHN3JObkVXNFdwdXNEZnhtZ3NpUVVtdm10cEJLZWVrT2dkd3ZrWGxmRmZMdVM5MmFWRjgiLCJtYWMiOiIzZWY1Njc3ZjM1M2YyODllMTY1Mzk5N2JjMWYyNzk5ZTRhMWUwYjBlYWViNDY3ZTllMTlkYzQ2Mzc1ZTY5NmViIiwidGFnIjoiIn0%3D; expires=Tue, 17-Jun-2025 15:55:02 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1970148424\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1814648001 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>16</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"30 characters\">http://localhost/hrm-dashboard</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1814648001\", {\"maxDepth\":0})</script>\n"}}