{"__meta": {"id": "X21d08963c6b52228118540749df85730", "datetime": "2025-06-17 13:22:49", "utime": **********.428471, "method": "POST", "uri": "/warehouse-empty-cart", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1750166568.601906, "end": **********.428494, "duration": 0.8265879154205322, "duration_str": "827ms", "measures": [{"label": "Booting", "start": 1750166568.601906, "relative_start": 0, "end": **********.316619, "relative_end": **********.316619, "duration": 0.7147128582000732, "duration_str": "715ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.316631, "relative_start": 0.7147250175476074, "end": **********.428497, "relative_end": 3.0994415283203125e-06, "duration": 0.11186599731445312, "duration_str": "112ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 46327584, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST warehouse-empty-cart", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\ProductServiceController@warehouseemptyCart", "namespace": null, "prefix": "", "where": [], "as": "warehouse-empty-cart", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FHttp%2FControllers%2FProductServiceController.php&line=1663\" onclick=\"\">app/Http/Controllers/ProductServiceController.php:1663-1673</a>"}, "queries": {"nb_statements": 2, "nb_failed_statements": 0, "accumulated_duration": 0.015569999999999999, "accumulated_duration_str": "15.57ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 16 limit 1", "type": "query", "params": [], "bindings": ["16"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.376898, "duration": 0.01466, "duration_str": "14.66ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 94.155}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.411769, "duration": 0.00091, "duration_str": "910μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 94.155, "width_percent": 5.845}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "ixL6BN3l9tyyUscqj0hv99yKIy0daqhD43fAAWcL", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "16"}, "request": {"path_info": "/warehouse-empty-cart", "status_code": "<pre class=sf-dump id=sf-dump-1785247062 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1785247062\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-569492536 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-569492536\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1401602286 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>session_key</span>\" => \"<span class=sf-dump-str title=\"3 characters\">pos</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1401602286\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-681191338 data-indent-pad=\"  \"><span class=sf-dump-note>array:19</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">15</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">ixL6BN3l9tyyUscqj0hv99yKIy0daqhD43fAAWcL</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1937 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=1pubyvl%7C2%7Cfwu%7C0%7C1993; _clsk=25xk9k%7C1750165707106%7C8%7C1%7Cl.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IkRLWWtueC9FUEkwcWxyK3dSbm0vZ3c9PSIsInZhbHVlIjoiUUcvalZvQnJsN2ZESTlJSE1ubTg2TzVUci9ZU2c4Q2g5RUR5ckJpUFYzZlZKOXMzbktDRG9vTldvUGtaYU9JRDg0Q0VIOUlRYUpTYnZ0dGsxV3ZSdUt0bGlHYXVsVTV3S0pVVVR0SlhqZFRzdkZZUnJXWnZYbUE2VjQyQ3d0T3hwMWxZNnVxOFdhd2s2c2ZsWTZFSWRqNVNZVUplcHZaS1llVzhyakdaemxVMUxvLzRLMkw2WFV3WFlNZzVRVXFDL0t6aEM2ZXVxSlhNYkhLQ3hzV3daNmlRWVJxTlREWWFNQ3dKd3o0YzBZYzB3bHRpYWloL0JDbm1sdUV0WWk2OEJ2dm5RVXJER2xmb0RqTWg3OHJCZlBFWG5WN0dadE93RUZxM01YYVA1OUxYUEtzLzNnVWQ5V0MwTkNrdnVKWFBzWFJVekF0RUdhNmE4QVlFOW44cER2VjQ1SldsOFg3aXE1TFkxRFZlU1Nxa1lTTjh4SEhvanFtTU8wWEUzL1l2WjdmS2pqZ2RkRHZ0Y1BDNGVURk9wZjM5bWg0bjg5UE4wZG9tenNxZ1RJeXE1RlhNbjZ5UjRPRHBoSEZxczZSeGQ3ZEtZL3ZPdFJ0bHhLV3lIeDh5cTZXNUt5R2ZvQ0w0K0dpT0kreXdLZFJaQUhxMjZIQklUNFREWVlDZEFaN3ciLCJtYWMiOiI1OWVmNDJjODk4NTRhZTNlNzg5OTc3MjY4NWVkYzFiMjZjYTA5Mzc4MjNlZTNmMDVlYWU0MGM3OTIwY2EyOTlmIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6ImVxVitvbmVsY2FOQzdUMDRVS3R5amc9PSIsInZhbHVlIjoiVmN4Q0VCSFliZldzK0EyL1VqRThpSjV0Q1o4aVZhMzN4M2tDb1lSNlcxVnVTRTlXc2RvWWZiNDNteEM1Q2VTTnB6b0xPUmcxWUp1aGhmOU5SRUpMNXhXYU10ZXlTeWE4bmpSRkRqbGVsbzYya1RDTFp2NzN6TEg3TmpoMzNwR2lnRE14TVVVdkJzb2tIc29jSSt3RktXUFlQemlDeEVaMzArS0s5N1FVS0NhdjBYNC9nN3J2N2U1emZHY3hMUWsxc1NIMGJ4eVJzaU9PVzlJWitibXA3RTdQdzhKMTB3SExNZ1h5QmNUZll0K1NZM05kcDhEYVB5YTBxSXp5WVJ2N1RzUytadUMyQWN5aVhiNmJ2alN5dHYyZUVKYk8rckI5aFZ1MXlCMXM1cmlYZlR6ZnFOeDRrVjNLV3o4eGV1UTV2aVBMeGREUWNLR0YwQlljYlp5ekJJTWRsV0hIT0h1ODI5UWczWmxHNmh5eHdjVHRhSlJBaXBBakpXZmV0aU5MdXJxdnNvN2hrZ2Y1MHdKL2E2MjduNDFGWnZlVTRIZ3lQRlFINVZ1MnA2dk05REFqUndUTEc3MzZNNEt6eXJxUCtDZ2hNa2kzZ1RlRUxQaWRrZHFzZ3EzeW1Ya1hZTmYzOER3akxDRTZNZ2EzN1dFNjY2Q24vR0g1cHM5Y1RYN2QiLCJtYWMiOiJjZDI3MmRiNzhlZGQ0MmNhODgzY2FiZWI3YTY3Njc4YWRkZWY4NjEzYzlmMGI0ZDhkMjE5YTdjY2IyODE0ZDhiIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-681191338\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-693321241 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">ixL6BN3l9tyyUscqj0hv99yKIy0daqhD43fAAWcL</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">coJ97Z8YyNCeFgF4h0jUL1PrpYBjauguIcJbJbm8</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-693321241\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-214325831 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 17 Jun 2025 13:22:49 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Ii9ydzJERXJpclJpUFJlK1Q4YmJqVEE9PSIsInZhbHVlIjoiYnFHRjE3S1NWVCtsM3pVMjI4Yk5maXN4UUQxdk9mTW9EKzZYaGx2V3hyaUJjU3R1L0VBRmhuOE5EejlmdXIxSkdRNlF6WERJVmJpSEc2SldER0NXZldtR0VTMnlkQy83T1R1Ymg3SUFnblZkWXlQbzg0d2hmTWtENGl4dS9uUFlsZ3lZRThpUVV0QzFXcTQ1S2ViQUNvL01pWVdwSG5aREZwN3h6TlNRNGJQczlUSDJWRlllMGtNeERmNEZMUWkyQmhBZFZCeU0wMWVzMTB4N2hTd0RZR3hHRkl4bnFJQUtrZWxzL0FRR1RqVDVjOUI4d1IrbUE5NjFwMEQwbTBOUXZ0MHQrMnQyYVF6K09FaDY0cmh2NmxXdGRXRWhycDc0YzQxNjdXR1l2TENtcU1NM1AxNFhkL2ZTalRyTG5TL0hJSW5CelBJaFpUbmJzbmJrK2N5dmRsK3RsdXp5SERwM05JNXdjRlpwYkgyVFdVb0MzRk02b1ZSVDhETGpjaGEvZi9XYXZFZDRUT0JremhpY3dxTk1OQWNJTTJEWWI5MzNSc1pLMVkrVFJaY0pxSC9tSVk4Vjd2OUNkb0ErYU5wWTg4K2JIZ2l3ZXVzc0o3TG5HNUdyV3loWHUrenR1eGZKR08xSHRZU1JzUUhEbVpvUWFFd2g4TWEwQjB0ZnZGa0IiLCJtYWMiOiJkZjQwOGQ1NzA2YmM0MzAzMjRlZmQ5MDBhYmFlNTg0NjM5NzYzMmFmMjhjYWMxN2Y3MWMwMmU2NjdmYTFjYzBiIiwidGFnIjoiIn0%3D; expires=Tue, 17 Jun 2025 15:22:49 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6IkFZK0dMV2VmbU1LYi9ZeEMrZE04amc9PSIsInZhbHVlIjoiL1hCRTl1Q09EVnFIWXAyZjIrVlNpYThseVg2QUJ3c21wdTYrQVkvY01ReHZjMzFYaHZrN2NBSmhpTDdyRU9PdWRQWmlQNWhmN3lBUGZWWFgwaVN0WnlkQ3IwS1J2R0pyYi9GQnRnK2ZYNnoxRGorbUh1SFJSdnJLZllRb0V4WWkwMnVVTDdkWVhWZ3lCZzRnQ3NGSXpqOFprN1ZRakkvTFRvVUw4d1g0c1NLclVOMTJ2ZmtCVFkxbmF2Q2lhVWNEdFUydlREd3NCVCtPblV4VVJiTGozb2pTYUZpNkJyTEpXOUlNS2FVdUZFM3BYVmF0RWwvZXV5YWIydlp4SlVzS3BaTjh0bklMTVQ5cjc4L0puYXhiZTVkYWhhUDV6ek0vVnlDMVRJWWkvSXIzOEdJbS92aTB6ZTZxdUJieFgyclE2UVRGMEJyS3YxaWszUlJEYWVLa3oycEo3K2xNOWV3SS90SDd3MnB0WU11OFo3U0hmeWZCb29tSW1JR2hBWGhiRUtQS3g1R21VRjJMdkt2eDlBV0ZOVUpSNXBCbDBUNFRSaUZsSFJxN2YyZ0VCTithL2tjMjR2TVQ3eFBxVDRWV2lnQ2RKSDhRUjRWOEdHNDVUdXlDaXl5SS9PcnB1Vk5wMDJ0Z3VJaS9kaUsybmxsLytZNjZ6eUpHZDZZQWsxVHYiLCJtYWMiOiIyMGEwZDA5YmJlNzY2ZmJlMGUzYjRiZWFhZWYwMjdhOTkxNGU4MDJhY2MzYmQ1MGFlZDI1ZmI4N2VjZWI1YjYyIiwidGFnIjoiIn0%3D; expires=Tue, 17 Jun 2025 15:22:49 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Ii9ydzJERXJpclJpUFJlK1Q4YmJqVEE9PSIsInZhbHVlIjoiYnFHRjE3S1NWVCtsM3pVMjI4Yk5maXN4UUQxdk9mTW9EKzZYaGx2V3hyaUJjU3R1L0VBRmhuOE5EejlmdXIxSkdRNlF6WERJVmJpSEc2SldER0NXZldtR0VTMnlkQy83T1R1Ymg3SUFnblZkWXlQbzg0d2hmTWtENGl4dS9uUFlsZ3lZRThpUVV0QzFXcTQ1S2ViQUNvL01pWVdwSG5aREZwN3h6TlNRNGJQczlUSDJWRlllMGtNeERmNEZMUWkyQmhBZFZCeU0wMWVzMTB4N2hTd0RZR3hHRkl4bnFJQUtrZWxzL0FRR1RqVDVjOUI4d1IrbUE5NjFwMEQwbTBOUXZ0MHQrMnQyYVF6K09FaDY0cmh2NmxXdGRXRWhycDc0YzQxNjdXR1l2TENtcU1NM1AxNFhkL2ZTalRyTG5TL0hJSW5CelBJaFpUbmJzbmJrK2N5dmRsK3RsdXp5SERwM05JNXdjRlpwYkgyVFdVb0MzRk02b1ZSVDhETGpjaGEvZi9XYXZFZDRUT0JremhpY3dxTk1OQWNJTTJEWWI5MzNSc1pLMVkrVFJaY0pxSC9tSVk4Vjd2OUNkb0ErYU5wWTg4K2JIZ2l3ZXVzc0o3TG5HNUdyV3loWHUrenR1eGZKR08xSHRZU1JzUUhEbVpvUWFFd2g4TWEwQjB0ZnZGa0IiLCJtYWMiOiJkZjQwOGQ1NzA2YmM0MzAzMjRlZmQ5MDBhYmFlNTg0NjM5NzYzMmFmMjhjYWMxN2Y3MWMwMmU2NjdmYTFjYzBiIiwidGFnIjoiIn0%3D; expires=Tue, 17-Jun-2025 15:22:49 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6IkFZK0dMV2VmbU1LYi9ZeEMrZE04amc9PSIsInZhbHVlIjoiL1hCRTl1Q09EVnFIWXAyZjIrVlNpYThseVg2QUJ3c21wdTYrQVkvY01ReHZjMzFYaHZrN2NBSmhpTDdyRU9PdWRQWmlQNWhmN3lBUGZWWFgwaVN0WnlkQ3IwS1J2R0pyYi9GQnRnK2ZYNnoxRGorbUh1SFJSdnJLZllRb0V4WWkwMnVVTDdkWVhWZ3lCZzRnQ3NGSXpqOFprN1ZRakkvTFRvVUw4d1g0c1NLclVOMTJ2ZmtCVFkxbmF2Q2lhVWNEdFUydlREd3NCVCtPblV4VVJiTGozb2pTYUZpNkJyTEpXOUlNS2FVdUZFM3BYVmF0RWwvZXV5YWIydlp4SlVzS3BaTjh0bklMTVQ5cjc4L0puYXhiZTVkYWhhUDV6ek0vVnlDMVRJWWkvSXIzOEdJbS92aTB6ZTZxdUJieFgyclE2UVRGMEJyS3YxaWszUlJEYWVLa3oycEo3K2xNOWV3SS90SDd3MnB0WU11OFo3U0hmeWZCb29tSW1JR2hBWGhiRUtQS3g1R21VRjJMdkt2eDlBV0ZOVUpSNXBCbDBUNFRSaUZsSFJxN2YyZ0VCTithL2tjMjR2TVQ3eFBxVDRWV2lnQ2RKSDhRUjRWOEdHNDVUdXlDaXl5SS9PcnB1Vk5wMDJ0Z3VJaS9kaUsybmxsLytZNjZ6eUpHZDZZQWsxVHYiLCJtYWMiOiIyMGEwZDA5YmJlNzY2ZmJlMGUzYjRiZWFhZWYwMjdhOTkxNGU4MDJhY2MzYmQ1MGFlZDI1ZmI4N2VjZWI1YjYyIiwidGFnIjoiIn0%3D; expires=Tue, 17-Jun-2025 15:22:49 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-214325831\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-508020359 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">ixL6BN3l9tyyUscqj0hv99yKIy0daqhD43fAAWcL</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>16</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-508020359\", {\"maxDepth\":0})</script>\n"}}