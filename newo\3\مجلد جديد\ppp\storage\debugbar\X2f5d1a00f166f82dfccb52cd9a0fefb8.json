{"__meta": {"id": "X2f5d1a00f166f82dfccb52cd9a0fefb8", "datetime": "2025-06-17 13:53:05", "utime": **********.536935, "method": "POST", "uri": "/pos-financial-record", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1750168384.931609, "end": **********.536974, "duration": 0.6053650379180908, "duration_str": "605ms", "measures": [{"label": "Booting", "start": 1750168384.931609, "relative_start": 0, "end": **********.4148, "relative_end": **********.4148, "duration": 0.48319101333618164, "duration_str": "483ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.414811, "relative_start": 0.4832019805908203, "end": **********.536978, "relative_end": 4.0531158447265625e-06, "duration": 0.12216711044311523, "duration_str": "122ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 52047952, "peak_usage_str": "50MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST pos-financial-record", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\FinancialRecordController@SetOpeningBalance", "namespace": null, "prefix": "", "where": [], "as": "pos.financial.record.opening.balance", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FHttp%2FControllers%2FFinancialRecordController.php&line=134\" onclick=\"\">app/Http/Controllers/FinancialRecordController.php:134-167</a>"}, "queries": {"nb_statements": 6, "nb_failed_statements": 0, "accumulated_duration": 0.034460000000000005, "accumulated_duration_str": "34.46ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 16 limit 1", "type": "query", "params": [], "bindings": ["16"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.45594, "duration": 0.01638, "duration_str": "16.38ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 47.533}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.4846468, "duration": 0.00114, "duration_str": "1.14ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 47.533, "width_percent": 3.308}, {"sql": "insert into `shifts` (`shift_opening_balance`, `is_closed`, `created_by`, `warehouse_id`, `updated_at`, `created_at`) values ('0', 0, 16, 8, '2025-06-17 13:53:05', '2025-06-17 13:53:05')", "type": "query", "params": [], "bindings": ["0", "0", "16", "8", "2025-06-17 13:53:05", "2025-06-17 13:53:05"], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Http/Controllers/FinancialRecordController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Http\\Controllers\\FinancialRecordController.php", "line": 147}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.499242, "duration": 0.00924, "duration_str": "9.24ms", "memory": 0, "memory_str": null, "filename": "FinancialRecordController.php:147", "source": "app/Http/Controllers/FinancialRecordController.php:147", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FHttp%2FControllers%2FFinancialRecordController.php&line=147", "ajax": false, "filename": "FinancialRecordController.php", "line": "147"}, "connection": "kdmkjkqknb", "start_percent": 50.842, "width_percent": 26.814}, {"sql": "select * from `financial_records` where (`shift_id` = 26) and `financial_records`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["26"], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Http/Controllers/FinancialRecordController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Http\\Controllers\\FinancialRecordController.php", "line": 154}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.511529, "duration": 0.00158, "duration_str": "1.58ms", "memory": 0, "memory_str": null, "filename": "FinancialRecordController.php:154", "source": "app/Http/Controllers/FinancialRecordController.php:154", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FHttp%2FControllers%2FFinancialRecordController.php&line=154", "ajax": false, "filename": "FinancialRecordController.php", "line": "154"}, "connection": "kdmkjkqknb", "start_percent": 77.655, "width_percent": 4.585}, {"sql": "insert into `financial_records` (`shift_id`, `opening_balance`, `created_by`, `updated_at`, `created_at`) values (26, '0', 16, '2025-06-17 13:53:05', '2025-06-17 13:53:05')", "type": "query", "params": [], "bindings": ["26", "0", "16", "2025-06-17 13:53:05", "2025-06-17 13:53:05"], "hints": null, "show_copy": false, "backtrace": [{"index": 26, "namespace": null, "name": "app/Http/Controllers/FinancialRecordController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Http\\Controllers\\FinancialRecordController.php", "line": 154}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.51562, "duration": 0.00321, "duration_str": "3.21ms", "memory": 0, "memory_str": null, "filename": "FinancialRecordController.php:154", "source": "app/Http/Controllers/FinancialRecordController.php:154", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FHttp%2FControllers%2FFinancialRecordController.php&line=154", "ajax": false, "filename": "FinancialRecordController.php", "line": "154"}, "connection": "kdmkjkqknb", "start_percent": 82.24, "width_percent": 9.315}, {"sql": "update `users` set `is_sale_session_new` = 0, `users`.`updated_at` = '2025-06-17 13:53:05' where `id` = 16", "type": "query", "params": [], "bindings": ["0", "2025-06-17 13:53:05", "16"], "hints": null, "show_copy": false, "backtrace": [{"index": 12, "namespace": null, "name": "app/Http/Controllers/FinancialRecordController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Http\\Controllers\\FinancialRecordController.php", "line": 162}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.5216792, "duration": 0.0029100000000000003, "duration_str": "2.91ms", "memory": 0, "memory_str": null, "filename": "FinancialRecordController.php:162", "source": "app/Http/Controllers/FinancialRecordController.php:162", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FHttp%2FControllers%2FFinancialRecordController.php&line=162", "ajax": false, "filename": "FinancialRecordController.php", "line": "162"}, "connection": "kdmkjkqknb", "start_percent": 91.555, "width_percent": 8.445}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK", "_flash": "array:2 [\n  \"old\" => array:1 [\n    0 => \"success\"\n  ]\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "16", "_previous": "array:1 [\n  \"url\" => \"http://localhost/hrm-dashboard\"\n]", "success": "تم تحديث الرصيد المفتوح بنجاح", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/pos-financial-record", "status_code": "<pre class=sf-dump id=sf-dump-2014235848 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-2014235848\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-2146078273 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-2146078273\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-363654973 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK</span>\"\n  \"<span class=sf-dump-key>opening_balance</span>\" => \"<span class=sf-dump-str>0</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-363654973\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-276061408 data-indent-pad=\"  \"><span class=sf-dump-note>array:20</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">65</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">max-age=0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"33 characters\">application/x-www-form-urlencoded</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"30 characters\">http://localhost/hrm-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1936 characters\">_clck=2gyfy%7C2%7Cfwu%7C0%7C1994; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clsk=1ti4td2%7C1750168382688%7C6%7C1%7Cl.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6ImpCaWNDQkhFaEt0MlkyY0tSQXllV3c9PSIsInZhbHVlIjoibEM5dWw5WlNNTWVhckRqT0UzSVRhMlVuVUx0NDNQcTd2Z2w5UWoyNVJDcXN4VE1jRVI2SGp5TW0vUjloQktIU2tWY0xSOGVkcDBiU3lRZGFETmtyRHh3ZEEyZ0hVd21LbUt4SElGNmwydEkzZFJpd1hxSEM4UDlSWHJpQzdBRzBQbzQydWFVS0VMYUlEc1NPNDQrWXVQcStjbWJlUGVYK3RBeDRxK1M1VmxBOVlZS0pWZ0J2SFgzY3hWTTBIelRIWmZxSlZEQ2VkZmZYbmtWb3ZKV280d0krUXNuNGQwUnhpRmhXaVdPZW5mOEIzbjV3Sm9ndGRpUnY1UXFNM1plZVNJMUxqaHJoMXpiY3RXY0k5VVBzNDBoenY0WDZXRnNDK1BCanFmcENxV2JEQUdUdmt2dVJEa1lzMnhRV2dUU1R2OFJCTU82U2c3SzZ6WWpOR2ZpMWRUejUxajdSejM5S3BubndDdTBSQXVybTVCb2tBTGFCZlhWNXhsZ2tYb3hRbEZYeUJrdzVuOFZhWmRGNW1BTUliRktLVzFuekNvRFErWFlNYVB4clBwSXIwQmR6NkdVTWFwTEJqZ2M3dlhzY0pQMC9iSC9uRmx6Wk1IZHh1NUxnTDF3a0piM3pZUWV4QnRGOWpjQllneWtFaVJxbjNqUElwYXNjcEczZHIyU3EiLCJtYWMiOiJjNDc2N2RmMGM3ODhhZDg2NTkwMjFhMGNjNWFkNmExYzc2OWE3YTc3Mjc2ZjAwYmJiOWExZGE3MWQ1ZmYyODdlIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6ImEzWGl4VU0wYi9YNjNPMEFBbzBabFE9PSIsInZhbHVlIjoiV2l6YXlLa3c5K3NvdTlNZnNyWmg4dk9MMkNrbldSZndhRTdwL2JjY0pQUXBwcGkxbzFCZ1d4ZWlvSmdCZlI5M0hrN3h5NGJ4OFJnUGJTSnhJTVNUS1czVmtZVUFLOFZFUVNuMm4rNmFMQVF6QXVSL1AvUjRvRXdkOU5Eb1ZOdVJIbFNIMXZuNk80aHJBTnovL2MwZ3VqMUtzM3ZJMVA0UE0vSmdlUWVTYmgyWkpQN1pnK0wvb1lmUUNVMDF6dnd4NmVqVGF1OFZhMnV6MkQzaTgzblhKZFZiUHNBNjQxdjVYMTdSTU9JTzJrRWZxNXlwTmgvNTBRMmpsWTJPWDZVdmdab21qUzF5N1NoS3VyeWVhc2tmZ3FZd3B5WkJVWkpnYTZpbmMwYjRDYzhxYTM2cXhIQ0NpWjFCd1hydi9BSWlsRGJDWDRLRjZkKzJIZ21SdFNhMXRnSVdRSldVcWs2SkVxZlViVG42YUh3em9wMDY3NnowWklvTjhtYzJlTWRGbGhNWCtJUUFRTkVTR21EUXVNbWNaT1A3YVk4SDFBT2srRm82emt0ak5GSXYvVFVlaElYTk9qSHZVWHUxNnllWTZWS2YzcFRJVzY0RzJFL2V2YnVqVTgwVXM4eTQzYjR2SVUrUnBaUEFyMlJjcVF2VGFONlZhMFlPcVBMaUp1cnQiLCJtYWMiOiJkNTFmMWFlYTdiYjgwYzZiYjVmOTljNjVkZmIyNmM1ZTQ0OWE3MmVhYzE2Nzg3OTI2MjI2Y2U0MzRlMzcxMTcxIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-276061408\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-241262592 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">VloaUQCBjpDKrel7nHeAzB1sTLdTMwteSos1MmAN</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-241262592\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 17 Jun 2025 13:53:05 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"30 characters\">http://localhost/hrm-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6InNxZk16KzNzdDNXMzNtdncrVnQ3Y1E9PSIsInZhbHVlIjoiand6MWxFQnVscDdDR0p0eThrSTlrazhsN2RrS28xMlN6OHBkUURTWEtVd3h6SHJmb1N3OWV5NzZaMVNHTUcrWnFCVHZiVVZORHFiSGh4ZnlMc05wZlJMenFOZVNjT1dkbTAvWWt0MW1hNXlNVjJJdTlkb2ttMWRGZExpOE5DZGlYdzRzcCtGa3IrTG1KVUlwV3JsZkhyMzVxOEpuSCsrYTJwaTF3VU1ZZzB2WCtCV2hpdi9ib2xZTS8xT0VYWHQrMVR6b2hJMHBMU3hOTE1lL1lNd051RWhCblFwbVU3V2tUbW50dlpORCtRNnp3Z2E0MTM5ZXIzSFJuTU9uUmgyekpZczFBME43eUJCRDh1UWVZOEVqdm5MR3lKMzJoWmJYVjBRMzBhajl4WDBHNk95cXJPbGJLQXhnRHl5NkpDSmZwVlR3bEdGQW1oLy83N2VEV3VEc0RmQ1BOTE56K2ROWjJhUTlxL3lZSnlXQ2dlZWJxRWtPWXhDWmJsRWtpbW5HRVpra2ErL0R0cElITm44U2R6WHl1NHd1NDdnazNCVmpndkltakZSS1BzS0tMYzlzYlM4UWNhc1V2NTMwU2Y2QzY0NHo2YnZYRnp3N25sYTkwZFUxc3BoR2lzVjAyWGx6aCt3bHhzaXJuUEZRY0ZoUkRSNnRSZzF5Zm8xaHQ0SDkiLCJtYWMiOiI0NTM3NGYyYjUxZDAzYzIzM2EzOTlmZjM2MTNiY2Q2NzM3NGE2NzNkODIzYmQxMjE2N2YzNzllMTg3YjMyNDJmIiwidGFnIjoiIn0%3D; expires=Tue, 17 Jun 2025 15:53:05 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6ImpxbFdOeTFhTnpINEMwOVVWR0YwVVE9PSIsInZhbHVlIjoiNlVSeFhmUVhTMzVaVXVFcVZrRndYV001NVFaNmNJc1FLWG5qQWxKWTBWUWlxeXk1V2FGckdjUkJTdU9CVE1Wd3lTRUczV0t1Y2ZBNUMrbzZwQUNtUS9FU2lUWXFsZkd0WG1hQm1ObndJTEs5YjExOHZkdmhjbFJWQjdjb3hTNFQzSXdwMFJSY2pyYjY3YzF2NExJbGZFT3QzNUM1N3E1b0J1cCszb2FOOFpzZGtOYVhEUkpwSHF6WEowOWM5MWVOOVNDOFRPR2JyOHp6ODJkY1lLcVRIUTJrV3hRcVQ2R1FPVVY0TEJFSEcrdkhBcW1aR2kySU16ZkZITkdRaDlkU2hjdm9UVjZmNmREMjNrbi90Y0RLb2ZabjlTRFZJbHo1MlZHdFNJUTczUERQUmllRS9RWFFjYllNa1BDTjZZdWpORnYwTW1pbG5xWGtSb3VwZVFjWm9ucStQdmt1WHZMenVOa2puUE1aQ0J1TnJBK2ZqRVkyVDVaVmlPSmVZQ2p3V0hvZW54TGJlMy9VTEQrVnFnb1NDV09XWnoxb0kySkdQcU12Z1BFSXRULzNEaEFEWmN0bHU1K2x5aU5Ua3JpazFUcVFPTWttR25WckZzSTZWanRXSE9BQWpoUXZxVHA3YlRTMGZ1OTZTNkhKaERHYXhlYUF4NTVYZmo0YWdMNlQiLCJtYWMiOiIzYzE4NGUwMjZhNDlmZDMyMTBiMTY5OGM1NDRlNjRiMTBmNGI0NDAxNmJiZTk3MzNiOGIxNTAwNjMzZDIwMWUzIiwidGFnIjoiIn0%3D; expires=Tue, 17 Jun 2025 15:53:05 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6InNxZk16KzNzdDNXMzNtdncrVnQ3Y1E9PSIsInZhbHVlIjoiand6MWxFQnVscDdDR0p0eThrSTlrazhsN2RrS28xMlN6OHBkUURTWEtVd3h6SHJmb1N3OWV5NzZaMVNHTUcrWnFCVHZiVVZORHFiSGh4ZnlMc05wZlJMenFOZVNjT1dkbTAvWWt0MW1hNXlNVjJJdTlkb2ttMWRGZExpOE5DZGlYdzRzcCtGa3IrTG1KVUlwV3JsZkhyMzVxOEpuSCsrYTJwaTF3VU1ZZzB2WCtCV2hpdi9ib2xZTS8xT0VYWHQrMVR6b2hJMHBMU3hOTE1lL1lNd051RWhCblFwbVU3V2tUbW50dlpORCtRNnp3Z2E0MTM5ZXIzSFJuTU9uUmgyekpZczFBME43eUJCRDh1UWVZOEVqdm5MR3lKMzJoWmJYVjBRMzBhajl4WDBHNk95cXJPbGJLQXhnRHl5NkpDSmZwVlR3bEdGQW1oLy83N2VEV3VEc0RmQ1BOTE56K2ROWjJhUTlxL3lZSnlXQ2dlZWJxRWtPWXhDWmJsRWtpbW5HRVpra2ErL0R0cElITm44U2R6WHl1NHd1NDdnazNCVmpndkltakZSS1BzS0tMYzlzYlM4UWNhc1V2NTMwU2Y2QzY0NHo2YnZYRnp3N25sYTkwZFUxc3BoR2lzVjAyWGx6aCt3bHhzaXJuUEZRY0ZoUkRSNnRSZzF5Zm8xaHQ0SDkiLCJtYWMiOiI0NTM3NGYyYjUxZDAzYzIzM2EzOTlmZjM2MTNiY2Q2NzM3NGE2NzNkODIzYmQxMjE2N2YzNzllMTg3YjMyNDJmIiwidGFnIjoiIn0%3D; expires=Tue, 17-Jun-2025 15:53:05 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6ImpxbFdOeTFhTnpINEMwOVVWR0YwVVE9PSIsInZhbHVlIjoiNlVSeFhmUVhTMzVaVXVFcVZrRndYV001NVFaNmNJc1FLWG5qQWxKWTBWUWlxeXk1V2FGckdjUkJTdU9CVE1Wd3lTRUczV0t1Y2ZBNUMrbzZwQUNtUS9FU2lUWXFsZkd0WG1hQm1ObndJTEs5YjExOHZkdmhjbFJWQjdjb3hTNFQzSXdwMFJSY2pyYjY3YzF2NExJbGZFT3QzNUM1N3E1b0J1cCszb2FOOFpzZGtOYVhEUkpwSHF6WEowOWM5MWVOOVNDOFRPR2JyOHp6ODJkY1lLcVRIUTJrV3hRcVQ2R1FPVVY0TEJFSEcrdkhBcW1aR2kySU16ZkZITkdRaDlkU2hjdm9UVjZmNmREMjNrbi90Y0RLb2ZabjlTRFZJbHo1MlZHdFNJUTczUERQUmllRS9RWFFjYllNa1BDTjZZdWpORnYwTW1pbG5xWGtSb3VwZVFjWm9ucStQdmt1WHZMenVOa2puUE1aQ0J1TnJBK2ZqRVkyVDVaVmlPSmVZQ2p3V0hvZW54TGJlMy9VTEQrVnFnb1NDV09XWnoxb0kySkdQcU12Z1BFSXRULzNEaEFEWmN0bHU1K2x5aU5Ua3JpazFUcVFPTWttR25WckZzSTZWanRXSE9BQWpoUXZxVHA3YlRTMGZ1OTZTNkhKaERHYXhlYUF4NTVYZmo0YWdMNlQiLCJtYWMiOiIzYzE4NGUwMjZhNDlmZDMyMTBiMTY5OGM1NDRlNjRiMTBmNGI0NDAxNmJiZTk3MzNiOGIxNTAwNjMzZDIwMWUzIiwidGFnIjoiIn0%3D; expires=Tue, 17-Jun-2025 15:53:05 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1103373655 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">success</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>16</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"30 characters\">http://localhost/hrm-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>success</span>\" => \"<span class=sf-dump-str title=\"29 characters\">&#1578;&#1605; &#1578;&#1581;&#1583;&#1610;&#1579; &#1575;&#1604;&#1585;&#1589;&#1610;&#1583; &#1575;&#1604;&#1605;&#1601;&#1578;&#1608;&#1581; &#1576;&#1606;&#1580;&#1575;&#1581;</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1103373655\", {\"maxDepth\":0})</script>\n"}}