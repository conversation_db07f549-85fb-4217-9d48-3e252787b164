{"__meta": {"id": "X332af9b2a6542991d2d31ba329e93a03", "datetime": "2025-06-17 13:52:57", "utime": **********.672322, "method": "POST", "uri": "/chats/getContacts", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.070342, "end": **********.672351, "duration": 0.6020088195800781, "duration_str": "602ms", "measures": [{"label": "Booting", "start": **********.070342, "relative_start": 0, "end": **********.57942, "relative_end": **********.57942, "duration": 0.5090780258178711, "duration_str": "509ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.579434, "relative_start": 0.509091854095459, "end": **********.672354, "relative_end": 3.0994415283203125e-06, "duration": 0.09292006492614746, "duration_str": "92.92ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 46130656, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/getContacts", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getContacts", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "contacts.get", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=333\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:333-390</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.01563, "accumulated_duration_str": "15.63ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 16 limit 1", "type": "query", "params": [], "bindings": ["16"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.618764, "duration": 0.013689999999999999, "duration_str": "13.69ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 87.588}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.646034, "duration": 0.00113, "duration_str": "1.13ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 87.588, "width_percent": 7.23}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (16) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.658168, "duration": 0.0008100000000000001, "duration_str": "810μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 94.818, "width_percent": 5.182}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "16", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos-financial-record\"\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/getContacts", "status_code": "<pre class=sf-dump id=sf-dump-266336636 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-266336636\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-63663880 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-63663880\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-970765590 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-970765590\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-2128765330 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"37 characters\">http://localhost/pos-financial-record</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1936 characters\">_clck=2gyfy%7C2%7Cfwu%7C0%7C1994; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clsk=1ti4td2%7C1750168314888%7C3%7C1%7Cl.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6InpURFhpK1drbGFpV2xRRFFoMG5ySlE9PSIsInZhbHVlIjoiMGpwT01mbnBOQnYzUmZqRkVpWjQrV1lHRUpGaXdkYXpEL2EwL01ycmFqWjYzL2lITEx6SS9VZTYrd243cHdqaEhXa3VxVXlaOSttYnRQejdOOHprVVpaT1ZJYmFpY2d1RVZTQnNERi84RTkxTXlBOExNUWZ1NTNPVjBLallQRGRqSUN5a00xZUpkd0w2SjN0M2M3c0xvdDh3L3gzK3FZVlh3ejNGa1RpR2Fub1VjQmdVTTVZZHdwV2JsRWdIdXhOWkFNNnFXaS9lSlB3ODZ0KzQ0M0FHSGFqVnNjLzhIdDAwb1FieTBONC80eXZBSEM0TC9IN3NUSW5uaDRrUm5Cc2JBdmsyS3N1YUMrcWVZb1ZDRVVGSTdPYzBnNlhIRTFZQ2dPYTRwbUh3UHhYUFpxTVFkbmJMejl5TjFJdE1VZG0zdEZXUmNwTFB5eHdIMUl3cVBuMy9DcEpGN1VMTFFtbng1VjE2VTN6bjVnMGhyTzhSNE5KZHFCRFB4SVppK243Y2lqYWQ2MTQwNElRRHZPSXF1N0pnbnYrcytOTUJtYXVsd2dYVThPQ0ZmYTFKOUhwM3N4UzdNRG11VXllWUpodjFHMlNHUEVMOXRMYVVSQVgzUnc2dEVFaGx5N1RpaG1MNk54Sm5EWXNIUW8yVU5sS1lKK1NLcFRlNGdpMVJQVjMiLCJtYWMiOiI5Yzc2OTg5MGI5YWVkOWZhYWIyZGRkNGViYTNkM2MzYzQ3MGU3M2I2YTJlOWIyMzE2YzNlM2E1ZTJlMTZiZmQ5IiwidGFnIjoiIn0%3D; test_session=eyJpdiI6IlVXVXVUaVBmSnZoZHlNb3EreDZsQ3c9PSIsInZhbHVlIjoia0tsamVEUTNmeEY5TnY1c0NWSWZnQUMvdHlNVFkyTWV0cytRdG14SkNHckdnVkphMFdGZDJMUTRtUDViaTdoa0I1WUNKaGprOU9GN2toM0pRQUxKMVlhbWpXak1xWU5iRjNTc3BRTCsxbmlrbmlnNGNCMzJiSS9TR0JSYXdZTHAyRmNGc29UanRHbzRXbk93Z0UvcE5YWGI4b0xIK28yVDlLNWFjMlkvVG1SY2lFYXpoVFlNSUpYWW0zR1U1RWhjbjFWOVIxOVVPTDVzdXM3dWRwMmpnNThOeXVmUUExdmMyUWxTVlZIQnA1azVPY2Jady9wbWpMaHo2dW1xcGJIL0NsRHd0Qng1SklzSEpVaUF6TGc4UFI2QnlIdUFublZlS2thYTRBbWpiaC8xQ2kweTRFZ0tSYzJDalRCcm4xdG56cjE1SVVOZ0xXWnZmalNNY1c4TFdua0VhZ1UvaUZnY1NzMVJWZG5oR3U3L2JsTUEzL2ZtVyt5WGV6ZGxta2tmT0Zmb1ljK2RKTGNycTZtdE9vdVZiWkJBb3V0MkZiOE1ub0RQWElWZ0EwTnl6UzNkb2hSYWFtUXgyRldRaE83TlluN05LWkdnL3BBcVFXOGpKVjFqL3FUOXlVYVhDS1JTY21RSXFIbVduVUFTZ0I1SDVnS1ZaQWhkQzFXY0VKTVkiLCJtYWMiOiIxNzNiNGQ0ODdhODNkZTZlZjk2YTc2NzJjNGM2NWY1YTU5YTdhNjE5ZjMyMmZiNmQ0N2IwMWMyZWVhZjNjNjI4IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2128765330\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1529205921 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">VloaUQCBjpDKrel7nHeAzB1sTLdTMwteSos1MmAN</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1529205921\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-648229867 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 17 Jun 2025 13:52:57 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IkhWcm0vNmx0bkhqMk1Sd1VVd0FYeEE9PSIsInZhbHVlIjoickNUZEF3dFo0R2NCMDEra08ycUpra3hPTUZCSmR2UlJ1WkZqYU4wajNQbVUzVlVJcnpzMyt6R2p2TDhpVnFJZEJybk92a2wyZHU4dVRQRnU0SmV5R0oyRjA3bloreTlETTl0dVh3YlZRWkF2bDBwK25hc0NjWUJlL3JFN0lRblpCS25VeWsxTjFEcUpPNnEyTm8zYjRBTE14d0FFZGxwUjFVS1VsVzhqbkdnVFM0Q3dvL1RVR1I0eURXOFBQaktSNGFhVllpZWNvRHVBbnM3Z0l3bkhHZUVDQkl4cXArMnNsMjMwQ1Z5SzUwbWR2aE9HQU9RZG5wYVA1SFR5c29WUFRqTmpkSXdkUWZqU3BPNFBEcmNWVVdzSHFqTFJQZnFPNDB6SXFnbEZDVTFhUDhjRDlxN0trMUxUUVJIK2lLTXNVV3RUYzh4Nm0xQkxzT3FDaDRHa0NuaFdRekd6QksvSFQvM1pPZlZaVnV6bGNPSmlrZ1JyZ0IxK3JzTnppd243ME9iaUxTTlFZc20yU21MMjZqdmJqZVNEalJQWlpYSTFmTUxCYUhIYVlLVjl4WHZPWjhQbERYWWM5Rm1uYUpKOVJMeURiSTdCWmJ2MFpHQ1pLNlVKSjF0UDd3bEUvVDhGU3VKWTRWZ09jTGVnVXZqcjB4MFBHWEpJVVZqc1dycDYiLCJtYWMiOiIzM2NhMWE3YzRmMDdjYTI5N2ZlMWI4OTcyZWYxMTI2YTMwOTU0ZTliNGMwOTkxNjY4YzYzODQ4MTA1MjFkN2E4IiwidGFnIjoiIn0%3D; expires=Tue, 17 Jun 2025 15:52:57 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6IkVRQ3FEWVR4SWg3Z1FvbGdZRFZ0SlE9PSIsInZhbHVlIjoialpndDV5UmNjR2tMZjZYdWkyd2RjMXR2M0R2NmMxQnZkMUNIQ00rRWFOUVNqL09ia29XNE1UQXptSTJvNnVuOG9FbGY3WVBlZkZUVU4vWG5jQ1BvbUxaclAzUjI5akZDN1NQRDd0Ylo5bnBlei9CYjdXU0NxdGtCZGNQLzJ1V0ttZTR2cmhJdUlIdjUyaWZZUjV0Z0JpakJ4bkNiNDVRT284emZiVE1UR01kTEh1RzdPNU91L3FnUURDTE9nWE9tZFg2UXBOcE5MVitmN1BqamNFWS8yQ2hvZlVHcTlQYnBDOCtSN3d2OEVNay9ERzZLWFRyNjVESytjeWt6SGo1bllMaE1vUitTbUJGV2VvQ01NOWpLc3d0aTQxVHFUY0JJRXpMNHl3NUlySnI3SXZRZTVBWTROVEZPWEFLZHQ1YTNjWVlZTm1Fa0QycTk4aHVTZ3dSb0dkTjQ3YW1odjhQVzZPVm1VL0Rsd1ZQcjRxTHFBdDZ1TUxpM0o2M1M0M3VudkNRK0lsenhwRlhMUFdSYnhyZnVJYjlnYWVIR253cXBENjdRWE0yeEVHVzNsdHBjdjIyOTZCazVXWGVFbVh2TGp6V0l2M01kalpjUnNFRDRMTUxzL0RBWU5BNnFyMXpHUThORWY4WWkrajBHZUF6YnpMYVhpMjRZNXhjVnVrWCsiLCJtYWMiOiI0M2U2OWU4ZmMwYTU1MjA4ODE0MDkyM2RjODEyMTJjMzJkZDZhNjY2ODFiZDg5NTg0MWYyMmQyNTE0YzNmMTdlIiwidGFnIjoiIn0%3D; expires=Tue, 17 Jun 2025 15:52:57 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IkhWcm0vNmx0bkhqMk1Sd1VVd0FYeEE9PSIsInZhbHVlIjoickNUZEF3dFo0R2NCMDEra08ycUpra3hPTUZCSmR2UlJ1WkZqYU4wajNQbVUzVlVJcnpzMyt6R2p2TDhpVnFJZEJybk92a2wyZHU4dVRQRnU0SmV5R0oyRjA3bloreTlETTl0dVh3YlZRWkF2bDBwK25hc0NjWUJlL3JFN0lRblpCS25VeWsxTjFEcUpPNnEyTm8zYjRBTE14d0FFZGxwUjFVS1VsVzhqbkdnVFM0Q3dvL1RVR1I0eURXOFBQaktSNGFhVllpZWNvRHVBbnM3Z0l3bkhHZUVDQkl4cXArMnNsMjMwQ1Z5SzUwbWR2aE9HQU9RZG5wYVA1SFR5c29WUFRqTmpkSXdkUWZqU3BPNFBEcmNWVVdzSHFqTFJQZnFPNDB6SXFnbEZDVTFhUDhjRDlxN0trMUxUUVJIK2lLTXNVV3RUYzh4Nm0xQkxzT3FDaDRHa0NuaFdRekd6QksvSFQvM1pPZlZaVnV6bGNPSmlrZ1JyZ0IxK3JzTnppd243ME9iaUxTTlFZc20yU21MMjZqdmJqZVNEalJQWlpYSTFmTUxCYUhIYVlLVjl4WHZPWjhQbERYWWM5Rm1uYUpKOVJMeURiSTdCWmJ2MFpHQ1pLNlVKSjF0UDd3bEUvVDhGU3VKWTRWZ09jTGVnVXZqcjB4MFBHWEpJVVZqc1dycDYiLCJtYWMiOiIzM2NhMWE3YzRmMDdjYTI5N2ZlMWI4OTcyZWYxMTI2YTMwOTU0ZTliNGMwOTkxNjY4YzYzODQ4MTA1MjFkN2E4IiwidGFnIjoiIn0%3D; expires=Tue, 17-Jun-2025 15:52:57 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6IkVRQ3FEWVR4SWg3Z1FvbGdZRFZ0SlE9PSIsInZhbHVlIjoialpndDV5UmNjR2tMZjZYdWkyd2RjMXR2M0R2NmMxQnZkMUNIQ00rRWFOUVNqL09ia29XNE1UQXptSTJvNnVuOG9FbGY3WVBlZkZUVU4vWG5jQ1BvbUxaclAzUjI5akZDN1NQRDd0Ylo5bnBlei9CYjdXU0NxdGtCZGNQLzJ1V0ttZTR2cmhJdUlIdjUyaWZZUjV0Z0JpakJ4bkNiNDVRT284emZiVE1UR01kTEh1RzdPNU91L3FnUURDTE9nWE9tZFg2UXBOcE5MVitmN1BqamNFWS8yQ2hvZlVHcTlQYnBDOCtSN3d2OEVNay9ERzZLWFRyNjVESytjeWt6SGo1bllMaE1vUitTbUJGV2VvQ01NOWpLc3d0aTQxVHFUY0JJRXpMNHl3NUlySnI3SXZRZTVBWTROVEZPWEFLZHQ1YTNjWVlZTm1Fa0QycTk4aHVTZ3dSb0dkTjQ3YW1odjhQVzZPVm1VL0Rsd1ZQcjRxTHFBdDZ1TUxpM0o2M1M0M3VudkNRK0lsenhwRlhMUFdSYnhyZnVJYjlnYWVIR253cXBENjdRWE0yeEVHVzNsdHBjdjIyOTZCazVXWGVFbVh2TGp6V0l2M01kalpjUnNFRDRMTUxzL0RBWU5BNnFyMXpHUThORWY4WWkrajBHZUF6YnpMYVhpMjRZNXhjVnVrWCsiLCJtYWMiOiI0M2U2OWU4ZmMwYTU1MjA4ODE0MDkyM2RjODEyMTJjMzJkZDZhNjY2ODFiZDg5NTg0MWYyMmQyNTE0YzNmMTdlIiwidGFnIjoiIn0%3D; expires=Tue, 17-Jun-2025 15:52:57 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-648229867\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-415035014 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>16</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"37 characters\">http://localhost/pos-financial-record</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-415035014\", {\"maxDepth\":0})</script>\n"}}