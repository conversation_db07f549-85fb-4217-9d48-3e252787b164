{"__meta": {"id": "X37b626703611bc1dfa6dad76c76777c2", "datetime": "2025-06-17 13:50:06", "utime": **********.579201, "method": "GET", "uri": "/customer/check/warehouse?customer_id=10&warehouse_id=8", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1750168205.97569, "end": **********.579227, "duration": 0.6035370826721191, "duration_str": "604ms", "measures": [{"label": "Booting", "start": 1750168205.97569, "relative_start": 0, "end": **********.488313, "relative_end": **********.488313, "duration": 0.5126230716705322, "duration_str": "513ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.488332, "relative_start": 0.5126421451568604, "end": **********.579229, "relative_end": 2.1457672119140625e-06, "duration": 0.0908970832824707, "duration_str": "90.9ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 46219624, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET customer/check/warehouse", "middleware": "web, verified, auth, XSS, revalidate", "controller": "App\\Http\\Controllers\\CustomerController@checkWarehouse", "namespace": null, "prefix": "", "where": [], "as": "customer.check.warehouse", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FHttp%2FControllers%2FCustomerController.php&line=383\" onclick=\"\">app/Http/Controllers/CustomerController.php:383-397</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.02193, "accumulated_duration_str": "21.93ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 16 limit 1", "type": "query", "params": [], "bindings": ["16"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.528872, "duration": 0.01982, "duration_str": "19.82ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 90.378}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.5609488, "duration": 0.0011200000000000001, "duration_str": "1.12ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 90.378, "width_percent": 5.107}, {"sql": "select * from `customers` where `customers`.`id` = '10' limit 1", "type": "query", "params": [], "bindings": ["10"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/CustomerController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Http\\Controllers\\CustomerController.php", "line": 388}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.5671692, "duration": 0.00099, "duration_str": "990μs", "memory": 0, "memory_str": null, "filename": "CustomerController.php:388", "source": "app/Http/Controllers/CustomerController.php:388", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FHttp%2FControllers%2FCustomerController.php&line=388", "ajax": false, "filename": "CustomerController.php", "line": "388"}, "connection": "kdmkjkqknb", "start_percent": 95.486, "width_percent": 4.514}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\Customer": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FModels%2FCustomer.php&line=1", "ajax": false, "filename": "Customer.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "16", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]"}, "request": {"path_info": "/customer/check/warehouse", "status_code": "<pre class=sf-dump id=sf-dump-1250987243 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1250987243\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-1654891541 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>customer_id</span>\" => \"<span class=sf-dump-str title=\"2 characters\">10</span>\"\n  \"<span class=sf-dump-key>warehouse_id</span>\" => \"<span class=sf-dump-str>8</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1654891541\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1586200255 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1586200255\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-329078713 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1936 characters\">_clck=2gyfy%7C2%7Cfwu%7C0%7C1994; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clsk=1ti4td2%7C1750167218299%7C2%7C1%7Cl.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6Im9CTExGRFltcGR2MU9EUzBhUW1WNmc9PSIsInZhbHVlIjoiTlZraVdNcU10WFhqSWxjRUx2RDBBeUh1NGUxQXNCbENSK1RPVlp3SlI1ZjlvNmM3Z3hodWZxU3dOUDNzRkNBOWY5Q3NvM1dpY1E1bDNUVzlmWGVZeW16REl0NE9iTU91Ym5QNGNLa2ZIaXZYeExBN1U2YkdRTURMZnY4Q212ekZPYWY5d2pBaXlrSEMvY0RlMUZnYzNiN0dZOEhVNHY4QUw0am96eDBEeTVrcEFPRm5lWGlZMDJucFg5MnFySnFETDRpVkIwYnp0am9tbkYrd2V4d3FmbUFYL1JUZDcxdEYvWG5Qck1MYW5wS1hVZ092bjAwcG9GK0JIQnpsVlBLTmxxVkNFY0JBMFg1VHZqalQ5Y0V0K09BMTFuV2pXM2Znd1VIM3MrOGJveFBDV28wLzdyczhVTGIycmUxSXR3WU1JOWxPZGlPMnNONDlPVTNCa0g0TE9GZDhnYlBiRDExY2NuY0U3ZkRHSmEyYXhFOXJ6TE1Jd3VBNE40V1dJb1RWdXJ2aUpQU0h2NHUraktDdzJEVkYyWVFzQ1I4VFdaWHNhU3k1dXgrVGJNelBIeDA2RU1mWW4vb28vRzJKVkJod05ScHcxbkNFYnZXRmR5S2s1RXAwbnR3cThSSHpZOTBHb0dxVDZ2Q1ZaZEErVUxSVjZMZnc3VWlDcE9SYmlodUIiLCJtYWMiOiIxZjcxYzM3MGU4YmEyZmY2YWQ2NTkwMDI0OTZjNTczYWVlMDNlNjAzZWMwYmE2ZDMwNDg0ZDUzMjFjYmI5NjcxIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6IkxmQjIvbk9ZRlZTT21Ta2Z4WUVJc1E9PSIsInZhbHVlIjoiUmtUNDI3RFk3Tkd0eXZJTXQyQ2p0U0UyMzVwTVlrcmR5bFBpbDVkM1pPNUF0Tk0ySGF4L2hLMUVOR080TXp1Nk1XWjcyNHdzWjYxU0h1enFJMkFTeVFGOGhoR3NzZ2ZVcjJnY3lGRXgrTzAyT0sweldNVUtFaXVka3JXaWoyd1RtZERVcmVWWDR4aEtpREV0MFZRNVhJa1V5bEh0S1pVTVlpOGZUSXF2N0dGcEtiWHJFSy9TZFZoc0RtZlc4Q1BVRFppSDE0RkEzNVExdmpwaStCRDkwczdYMUE3VFZucUlIdVZvOHl4dHdvaGorZW1iTzYyS3RZQVh3MTQ5RFYvWnBOQ0oxME9Cb0VPeFVxV1NLTmp2NVlrc09CdG5VNFFpK0IyUHdETDhWdnF4dDcxR0EwRS9JTXZudVVHaGQ5Rno2VW1VeWUxQ21xWUFlS21Ra1R1Zi9JcWs5OVJZcEo3Q1ZpTFhHaGFQR0pmbFI5cWNqZ0Rjb01MYlBBb0crZmRVL2ZDZG42Vk0ydzI3ZjJ5Q2FRN0hVR1N4OVEydFRxVVJBcHlyeEZyaWNnTEliM3NPbFc2TDB5UVlxWERib2V4VnNkRnZYQ1M5UzkrT0x6bEp6Q2VvWDVQMm1tVDFwbjNZUnhFV1dic1Jvem1UV1hNNDV0bEhkRXVBTUx6ZHF6RDEiLCJtYWMiOiI1MmZhOTdlMjdiYzRlZDQ5ZWZjMTBkYjU0NTU0N2YxYzczYzFkYjEwZGFhMTBkNTFlNjliYTg2NDNjNjBiNjk3IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-329078713\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1188399917 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">VloaUQCBjpDKrel7nHeAzB1sTLdTMwteSos1MmAN</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1188399917\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-94006010 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 17 Jun 2025 13:50:06 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-methods</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">POST, GET, OPTIONS, PUT, DELETE</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-headers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"80 characters\">Content-Type, Accept, Authorization, X-Requested-With, Application, X-CSRF-TOKEN</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6ImxuMktQRUZHUHpNV3FJemFkMUtSbUE9PSIsInZhbHVlIjoiU3QxNWpzZXhwTTBCMkVxbUhRaWcydkRPYVhEMk1Qa0h3QmttK3EzNnY5SDFvWXVJUm5DZ0RCREt2UnRhYUgvOXR4Y1dFKy9jMkhsNjlhdFpyWG1WSG1RejIwNEkraElyL3hsUVVGNnk3QytGM09SNnFGOVBkRHZ2NFF6SENsMWdzV1E5V1cvb2dkNkdUT2VKN3BUUGNRQVlFTHJvQ2pLemdnL2JLa0M4STFJdXBVU0Eyb1NESlV3RS9ZdEZWUFpHb0pDUU5yQnZ5YzFYWlI3TlJuL1BvTVJyckV5NVFjNXFyWmhtc3E5OVdXMFBpYzZ6RHJiM2hNUUJmNnpMcTk3MDgya1E1VVRnU0ZLRTNjVDh0QXZXSllpbkRiREE4OG83emNBSkxWMEZRWlBmLzl1czNxRjh1cGdTRDV6TzROQmlrSmxpSWdpWWVmR3VNKzdNdVVEWXdyUDJkaEpmM051U0hHaElrRGJ4RnVnQlpGUlkrMythZjRkV1VDNjVxc1ljVyt0WjlTcHNGT204QkEwd2FmTlY5VnFFc3h2eE5nMFFPN2FSSjQ1Y1FwQVpma1hXenJqRUVOeDR4ay9xc011MXNFN3UxSXRlY2RFWmZWelBLa293VVhhWEJURWJ2TnFWd3NydFh0eUp2clo2QmY0Q2xsK0ozL1B4cnREMkRlelEiLCJtYWMiOiJmNDMzMWQ2ZDA0MDMwZDYxNDllYTc1YmM4NGNkOGEwMDRjYjg0M2E2YTZmMGQ4ZWQ4MTIzYTg3ZGQ5ZDc5NmNjIiwidGFnIjoiIn0%3D; expires=Tue, 17 Jun 2025 15:50:06 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6IkZLQ3VRR2dSeGlwVDl5UjI1T0gyQUE9PSIsInZhbHVlIjoiUzJTQlBHTGJrTkJEeTVacTRQNS9MU01ubm5KSHJRc3F0cmJFc09McWVmZ1hqUDFSSksxTGo3L29MeVFuTXo3Vk5RRndLdExOUGJXWENLNDg5dlczM1B1REVobzBybGFKMW5FQW1ENnRtMUwwRUxNSXE2WVdLQ1N6YlUyRkFPcGZpZWNJVStxSjBJT1hlSVMxZkNiWUllV0ZjaG1jQ1ZieWRMcFJNcnU3ZjN5dURDb2lPR3RzWkc0NTVHT2pmbDhWMHhxQmx5czFDeVFXUG14R2VrbEdqdmVrSTcyUkRzdTJpOENGOVM3RVlObHVKOGdBUmE5SEEwMGJVeWVvdkJCaUt6N0RZWGw1MG56clFtRDEzdklMbjZoNll6N1FiZG9rTkJFay9WN1hRMk1pSDJwQ2xzVzAvdEtlaW1RUEd3bWdoV1VrSElqYTZ2SVh6Y1lEZHI3YXJsaDhiTmYyNm1ZczBSQytwZUZBZUN2NVh5bFVJUFZxVzVlUGFDb1gveGVBQy9zYTFIaHQ5Y0lJSld2NUlJRUExSE11cHhoNDNBb0c4bE13WjV3MDhzb2lndFMwRVFTUTNTK1Z2YzJSSkJmc25XbGtWano1ZmxwRVhkNFlFdDV4SFNHemNkcVY5a3l2aEY3K2ZsbTRuNlpYZTZ4eFN5NlpoLzh3cTRPbWZGcmIiLCJtYWMiOiIwMjYxODkxN2RkNGYzYzdmNGJhZjM0ZjA0OWI1ZGI3YzQzMWU5N2E5ZWQzZWE1MGU0MmI3Y2FjNThhZWRmYzQ2IiwidGFnIjoiIn0%3D; expires=Tue, 17 Jun 2025 15:50:06 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6ImxuMktQRUZHUHpNV3FJemFkMUtSbUE9PSIsInZhbHVlIjoiU3QxNWpzZXhwTTBCMkVxbUhRaWcydkRPYVhEMk1Qa0h3QmttK3EzNnY5SDFvWXVJUm5DZ0RCREt2UnRhYUgvOXR4Y1dFKy9jMkhsNjlhdFpyWG1WSG1RejIwNEkraElyL3hsUVVGNnk3QytGM09SNnFGOVBkRHZ2NFF6SENsMWdzV1E5V1cvb2dkNkdUT2VKN3BUUGNRQVlFTHJvQ2pLemdnL2JLa0M4STFJdXBVU0Eyb1NESlV3RS9ZdEZWUFpHb0pDUU5yQnZ5YzFYWlI3TlJuL1BvTVJyckV5NVFjNXFyWmhtc3E5OVdXMFBpYzZ6RHJiM2hNUUJmNnpMcTk3MDgya1E1VVRnU0ZLRTNjVDh0QXZXSllpbkRiREE4OG83emNBSkxWMEZRWlBmLzl1czNxRjh1cGdTRDV6TzROQmlrSmxpSWdpWWVmR3VNKzdNdVVEWXdyUDJkaEpmM051U0hHaElrRGJ4RnVnQlpGUlkrMythZjRkV1VDNjVxc1ljVyt0WjlTcHNGT204QkEwd2FmTlY5VnFFc3h2eE5nMFFPN2FSSjQ1Y1FwQVpma1hXenJqRUVOeDR4ay9xc011MXNFN3UxSXRlY2RFWmZWelBLa293VVhhWEJURWJ2TnFWd3NydFh0eUp2clo2QmY0Q2xsK0ozL1B4cnREMkRlelEiLCJtYWMiOiJmNDMzMWQ2ZDA0MDMwZDYxNDllYTc1YmM4NGNkOGEwMDRjYjg0M2E2YTZmMGQ4ZWQ4MTIzYTg3ZGQ5ZDc5NmNjIiwidGFnIjoiIn0%3D; expires=Tue, 17-Jun-2025 15:50:06 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6IkZLQ3VRR2dSeGlwVDl5UjI1T0gyQUE9PSIsInZhbHVlIjoiUzJTQlBHTGJrTkJEeTVacTRQNS9MU01ubm5KSHJRc3F0cmJFc09McWVmZ1hqUDFSSksxTGo3L29MeVFuTXo3Vk5RRndLdExOUGJXWENLNDg5dlczM1B1REVobzBybGFKMW5FQW1ENnRtMUwwRUxNSXE2WVdLQ1N6YlUyRkFPcGZpZWNJVStxSjBJT1hlSVMxZkNiWUllV0ZjaG1jQ1ZieWRMcFJNcnU3ZjN5dURDb2lPR3RzWkc0NTVHT2pmbDhWMHhxQmx5czFDeVFXUG14R2VrbEdqdmVrSTcyUkRzdTJpOENGOVM3RVlObHVKOGdBUmE5SEEwMGJVeWVvdkJCaUt6N0RZWGw1MG56clFtRDEzdklMbjZoNll6N1FiZG9rTkJFay9WN1hRMk1pSDJwQ2xzVzAvdEtlaW1RUEd3bWdoV1VrSElqYTZ2SVh6Y1lEZHI3YXJsaDhiTmYyNm1ZczBSQytwZUZBZUN2NVh5bFVJUFZxVzVlUGFDb1gveGVBQy9zYTFIaHQ5Y0lJSld2NUlJRUExSE11cHhoNDNBb0c4bE13WjV3MDhzb2lndFMwRVFTUTNTK1Z2YzJSSkJmc25XbGtWano1ZmxwRVhkNFlFdDV4SFNHemNkcVY5a3l2aEY3K2ZsbTRuNlpYZTZ4eFN5NlpoLzh3cTRPbWZGcmIiLCJtYWMiOiIwMjYxODkxN2RkNGYzYzdmNGJhZjM0ZjA0OWI1ZGI3YzQzMWU5N2E5ZWQzZWE1MGU0MmI3Y2FjNThhZWRmYzQ2IiwidGFnIjoiIn0%3D; expires=Tue, 17-Jun-2025 15:50:06 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-94006010\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1490682484 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>16</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1490682484\", {\"maxDepth\":0})</script>\n"}}