{"__meta": {"id": "X39b35199a61f93d87243fe2aa628e991", "datetime": "2025-06-17 13:33:36", "utime": **********.992891, "method": "POST", "uri": "/event/get_event_data", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.138916, "end": **********.992923, "duration": 0.8540070056915283, "duration_str": "854ms", "measures": [{"label": "Booting", "start": **********.138916, "relative_start": 0, "end": **********.878085, "relative_end": **********.878085, "duration": 0.7391688823699951, "duration_str": "739ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.878102, "relative_start": 0.7391860485076904, "end": **********.992926, "relative_end": 2.86102294921875e-06, "duration": 0.11482381820678711, "duration_str": "115ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 46443248, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET event/get_event_data", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\EventController@get_event_data", "namespace": null, "prefix": "", "where": [], "as": "event.get_event_data", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FHttp%2FControllers%2FEventController.php&line=317\" onclick=\"\">app/Http/Controllers/EventController.php:317-345</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.02044, "accumulated_duration_str": "20.44ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 16 limit 1", "type": "query", "params": [], "bindings": ["16"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.942321, "duration": 0.0184, "duration_str": "18.4ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 90.02}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.975574, "duration": 0.0011799999999999998, "duration_str": "1.18ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 90.02, "width_percent": 5.773}, {"sql": "select * from `events` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/EventController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Http\\Controllers\\EventController.php", "line": 327}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.982372, "duration": 0.00086, "duration_str": "860μs", "memory": 0, "memory_str": null, "filename": "EventController.php:327", "source": "app/Http/Controllers/EventController.php:327", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FHttp%2FControllers%2FEventController.php&line=327", "ajax": false, "filename": "EventController.php", "line": "327"}, "connection": "kdmkjkqknb", "start_percent": 95.793, "width_percent": 4.207}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "16", "_previous": "array:1 [\n  \"url\" => \"http://localhost/hrm-dashboard\"\n]"}, "request": {"path_info": "/event/get_event_data", "status_code": "<pre class=sf-dump id=sf-dump-1460843998 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1460843998\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-1205257897 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1205257897\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-857231004 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-857231004\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1046427384 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"30 characters\">http://localhost/hrm-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1936 characters\">_clck=2gyfy%7C2%7Cfwu%7C0%7C1994; _clsk=1ti4td2%7C1750167180417%7C1%7C1%7Cl.clarity.ms%2Fcollect; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; XSRF-TOKEN=eyJpdiI6IjB0YUJxeXAwUDJaSHRIdTNVWGFhTFE9PSIsInZhbHVlIjoiWjJWdzNKeVlpZTRrVjFHSU1zV0hycUtRV2lnSDRONS81Zmw5UTc5ZS9FSnphK01vZHIrMm1IQ0F6Y1cwMkdqM05VL1N0TTJKU3ZEbC85d0dYQnl3cDZpSjBVNnpTT2hsWWs1RzJhN0FIRjZpcFp4OHNuSDYzUUp6TWY5WkVqZjVlSXRjNUxsbXNVNVQ2K1NNbjhxZ3RCaHpBdytzcmcvMW9vdUw3NVZ0dlYzRVdXZEdDKzlIb01jYUh6cVpLNzl6YisxSE9GZW9KdEoyb0h3YXhrU0V4ekhOWENFSE5OU1pjbENFTEEycG1LQWxIejJ6a1IrOTFrWGdHOUp6TUlQb1pnNjduNmhXbEJzSTdOdVZ1OGtiNmFRV3U3K0RtbmhGTHFta2JWNzJST0RYRmFSYVJ1b0hId2dKakFYdkRkZW92aXF5R05xU3NMK1FiN2RhNmZoeGc4NEl1UGpOaWNJaTc3dFN0d2RLSEZ6WXRFbXpGRjdZQlc1REo3eC9WYm9yZnVSczEwamhDTzQ3b1hscmkrMDdCMXFOczlTbUwyMzFLd0hBemdjTVcxUk5kS3l2Ynh3MVlndU05eERYa212dGpOR095b2RRdndhV0R3U0ZRRlliYzkyTDY3Nmg1bjVWM0tSaFBUK29BODluTEhZZ0hpZkFhUFVEZ1VwQkQ2WEYiLCJtYWMiOiJhMmZjMWUxMTE0N2UzZDZkMDcxNjYzYjU0ZmZkNGFkYjMxZWFjMjkyNDdkZTg3MzJkMjQ5MWUzNWE3ZjRhNTdjIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6IjNWcmI5QlRtSWszaXUyaHRnUHprb3c9PSIsInZhbHVlIjoiMUtEQWVuck9xcEllaTdTWnNuRWJneG9xTW9SWWFwTTBmbDVlWGh4RlJvQ05TSlBmZTlWRnBxZTFSeC9lODBwS0pwWUxtTEhhS2ZNSkVEa2gyUlNMUEVrZnZ4cWpBZ2x2SWF6TVZOdldySzcxbmlBWGROK0dpSUtVZnBKVnY1MzYzOCtXMEQrUm1FUWNLNjREL1RHKy8rQmxCTTRONUxoajM2NkZYZ3ZCYjcwMmlacjd2aThYcTR1MGxhWjZ3Mi9KdUxKUFEveEhXSG5jYzJ1dWpkdXZqMEhrY0tranB0Q0pEdlRwdWJUTENaQXZGZWZyVVFlWE5BTGNGaEN6cFI1R004Sk94QTJMY3luVkIzVjBWUDNiZTVzcC9wRXNUWmw5ZERtSHJkUjI5d0NFMWUxK3RqeEtQUE03QzNMVkZiaUVsQjI0cnBpQzdLd0dEZ3RZYnhicmgxU09wNUJEZ1prSkdMNkRqazJMOHJiMVZOcG9rcDdnNXJtQ1VITFYvZU9kcU1lQ05HQkRkNjlNZUtVNG1hR2QzbHRCaHJXNk1oSTc2bEE2S2xYZXhtR29pVlZZd09LdjNocnFzbmUvT0d6ak9hT281ZEZGVmljaXgxRUxubG81a2k5VS9GQ1hzWFFTMmJlSSt0WEFFem9pUnRoUWI4RlUwSWlkejVMYktEZmgiLCJtYWMiOiJjOTk4MDBiNjFjYjRmMDY3MTNkNWRhMjdkOGIwZDY0YjYxNmM4MmNlOTJjZWUzMjI1YTA0OGM4OGM2OGU1OTIzIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1046427384\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1373356113 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">VloaUQCBjpDKrel7nHeAzB1sTLdTMwteSos1MmAN</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1373356113\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 17 Jun 2025 13:33:36 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6ImZFaVJxYnlyZythdGVXSjE3ejJwSFE9PSIsInZhbHVlIjoiMjJkY3dpeitJbGhXTHdUbWozeFNVV1laYzJ2SU50RVZkMnlJVDZqTjc4WjllUjFVRDRPWFBpeVdidlh2UVhmN3BzOEVteFhzVGFrQmJCQ3lwUlNMMVlIeUhBQXhCRlJvNmVmNHZELzZvZ0o5Q2cwaHpJSThsZTNKbm9FV05WS3BXME9QbjBmQUxHRzdPVERqbGZFb05rNTg2empMNWJHeGJmTnB4NFc2Y2s3aEVSRlNGbmJsRitielFJRVA1bnpFR2s0TnN0R2VlSG9FUGZQcUVZeVNPK25PQjRuT2dQbkdQa1dJUlFPR2l5SnJjTFE3eE9aSlpKL3JrY0lYRU5zOGkwQWNKWmpYbUVISExaTzR5b0FVcHVOM0Z0bTUvbjBzUHgyU25wdCtqOUM4VElCLzVjUk92QnJvT2JibTU3bXA1R0ZxRThndFhWdVVJd3o5ZlNWaUJUdzBMUUpRMjArUkdGQlFGZnhaOUtZWkRqZjE1NUNnZTZ0d2F4ajdERFo2V2VGUWF3dVNCVGQ5QkhlN1orVVBxVzRiR2l6K1F2U0JRVWJDbHJvZkVSeWxMUU9BVWNSckNJNW1ieHJidm9VUW5ReWJnRUU2YTdtSHJ6dTcremJoQklkQ25YVzQ3V2hsaVpzQXExNnQzV3RXTy9SdytBZVdoamh2RHdHWVdta1IiLCJtYWMiOiI4NWVkMDg0Mzg5OTI5YmI3Y2ExMjQxZmUyZTMzMjg3NWE2YjJlNDA0NzI1OWQ4OGI2ZTAzNjVhNjgzYTBjYmFlIiwidGFnIjoiIn0%3D; expires=Tue, 17 Jun 2025 15:33:36 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6IlFPUUxub3dLQkU1TUVhWitkZVcvbVE9PSIsInZhbHVlIjoiUVh3S0J5Wnh1N09BaktPdTlWQTBxd1VWTWJHemp4Z0dXeDNsejdVdzhvVXIyZTlLQS9BOGFkeW5qYUNVbHpuSGZzd1VxSm10bEY1RlNnRHRzbUwzNUpCT0h2ZitzWkFvL254WHFvV3FjTmQvaEJJV2thV2daaXhJMXVhY3pubVlHZzdOeG90bVFjdTVUNHpHWlY2MmpneUJkZkdOT2YxOVd1NTRKQlBCaTExdEs0bC9Fd2FKS3pLbDJiYmYxWlpnZ3FjMmJ5Tk9weVhCM0podTVPWnR4cFV4aUNEcnBoUTdzRmdlNDU0ZnpoY05HQmtrT0dZTU1zWXozcGVGOFZ1OFBCM3JTRFRpRDlhSmZXSm54U3hWMFlZZSs2bDNzeHZUaFBxYkY1QjVaamVPeUtWai9Qb25vdk5Lc0hUN2tKWGZvSHdaQmNXV0ZLOFRIak5Mb052YnBLbktGeFNWVndFM05ZWmt3OVpEbUJFTTZBL1kyYTZoa29uNzVjak5qbnpIVk5BVEFjUjVkamVRSFEyZ3ByOHRGeWcxcTlHZ1Y1NXhtVU9IR2I5RUt0bFkxVTVEVWhPOUpqQUpJZTNyU1pEOEFObGVzNmVoZHdVWWVmd2g0NVV0TEtiNHROaGVNdlEvQVNoZkYrMmwwQk0xaTdIWlhYbW5mUUdwUWFIZU1zeEwiLCJtYWMiOiJlMGYyMDM0ZGM3NWJjNmE5YjIwY2QyYjY3MzNmMGM5NjU5OTkwNzJhNGJhODcyYjdiNzg1NTM3NWFiY2RlZmZjIiwidGFnIjoiIn0%3D; expires=Tue, 17 Jun 2025 15:33:36 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6ImZFaVJxYnlyZythdGVXSjE3ejJwSFE9PSIsInZhbHVlIjoiMjJkY3dpeitJbGhXTHdUbWozeFNVV1laYzJ2SU50RVZkMnlJVDZqTjc4WjllUjFVRDRPWFBpeVdidlh2UVhmN3BzOEVteFhzVGFrQmJCQ3lwUlNMMVlIeUhBQXhCRlJvNmVmNHZELzZvZ0o5Q2cwaHpJSThsZTNKbm9FV05WS3BXME9QbjBmQUxHRzdPVERqbGZFb05rNTg2empMNWJHeGJmTnB4NFc2Y2s3aEVSRlNGbmJsRitielFJRVA1bnpFR2s0TnN0R2VlSG9FUGZQcUVZeVNPK25PQjRuT2dQbkdQa1dJUlFPR2l5SnJjTFE3eE9aSlpKL3JrY0lYRU5zOGkwQWNKWmpYbUVISExaTzR5b0FVcHVOM0Z0bTUvbjBzUHgyU25wdCtqOUM4VElCLzVjUk92QnJvT2JibTU3bXA1R0ZxRThndFhWdVVJd3o5ZlNWaUJUdzBMUUpRMjArUkdGQlFGZnhaOUtZWkRqZjE1NUNnZTZ0d2F4ajdERFo2V2VGUWF3dVNCVGQ5QkhlN1orVVBxVzRiR2l6K1F2U0JRVWJDbHJvZkVSeWxMUU9BVWNSckNJNW1ieHJidm9VUW5ReWJnRUU2YTdtSHJ6dTcremJoQklkQ25YVzQ3V2hsaVpzQXExNnQzV3RXTy9SdytBZVdoamh2RHdHWVdta1IiLCJtYWMiOiI4NWVkMDg0Mzg5OTI5YmI3Y2ExMjQxZmUyZTMzMjg3NWE2YjJlNDA0NzI1OWQ4OGI2ZTAzNjVhNjgzYTBjYmFlIiwidGFnIjoiIn0%3D; expires=Tue, 17-Jun-2025 15:33:36 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6IlFPUUxub3dLQkU1TUVhWitkZVcvbVE9PSIsInZhbHVlIjoiUVh3S0J5Wnh1N09BaktPdTlWQTBxd1VWTWJHemp4Z0dXeDNsejdVdzhvVXIyZTlLQS9BOGFkeW5qYUNVbHpuSGZzd1VxSm10bEY1RlNnRHRzbUwzNUpCT0h2ZitzWkFvL254WHFvV3FjTmQvaEJJV2thV2daaXhJMXVhY3pubVlHZzdOeG90bVFjdTVUNHpHWlY2MmpneUJkZkdOT2YxOVd1NTRKQlBCaTExdEs0bC9Fd2FKS3pLbDJiYmYxWlpnZ3FjMmJ5Tk9weVhCM0podTVPWnR4cFV4aUNEcnBoUTdzRmdlNDU0ZnpoY05HQmtrT0dZTU1zWXozcGVGOFZ1OFBCM3JTRFRpRDlhSmZXSm54U3hWMFlZZSs2bDNzeHZUaFBxYkY1QjVaamVPeUtWai9Qb25vdk5Lc0hUN2tKWGZvSHdaQmNXV0ZLOFRIak5Mb052YnBLbktGeFNWVndFM05ZWmt3OVpEbUJFTTZBL1kyYTZoa29uNzVjak5qbnpIVk5BVEFjUjVkamVRSFEyZ3ByOHRGeWcxcTlHZ1Y1NXhtVU9IR2I5RUt0bFkxVTVEVWhPOUpqQUpJZTNyU1pEOEFObGVzNmVoZHdVWWVmd2g0NVV0TEtiNHROaGVNdlEvQVNoZkYrMmwwQk0xaTdIWlhYbW5mUUdwUWFIZU1zeEwiLCJtYWMiOiJlMGYyMDM0ZGM3NWJjNmE5YjIwY2QyYjY3MzNmMGM5NjU5OTkwNzJhNGJhODcyYjdiNzg1NTM3NWFiY2RlZmZjIiwidGFnIjoiIn0%3D; expires=Tue, 17-Jun-2025 15:33:36 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>16</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"30 characters\">http://localhost/hrm-dashboard</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n"}}