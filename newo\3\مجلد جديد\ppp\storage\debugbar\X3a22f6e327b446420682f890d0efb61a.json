{"__meta": {"id": "X3a22f6e327b446420682f890d0efb61a", "datetime": "2025-06-17 13:30:59", "utime": **********.826441, "method": "GET", "uri": "/customer/check/delivery?customer_id=10", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1750167058.944668, "end": **********.826472, "duration": 0.8818039894104004, "duration_str": "882ms", "measures": [{"label": "Booting", "start": 1750167058.944668, "relative_start": 0, "end": **********.698031, "relative_end": **********.698031, "duration": 0.7533628940582275, "duration_str": "753ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.698049, "relative_start": 0.7533810138702393, "end": **********.826476, "relative_end": 4.0531158447265625e-06, "duration": 0.12842702865600586, "duration_str": "128ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 44963576, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET customer/check/delivery", "middleware": "web, verified", "controller": "App\\Http\\Controllers\\CustomerController@checkDelivery", "namespace": null, "prefix": "", "where": [], "as": "customer.check.delivery", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FHttp%2FControllers%2FCustomerController.php&line=365\" onclick=\"\">app/Http/Controllers/CustomerController.php:365-378</a>"}, "queries": {"nb_statements": 2, "nb_failed_statements": 0, "accumulated_duration": 0.028779999999999997, "accumulated_duration_str": "28.78ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 16 limit 1", "type": "query", "params": [], "bindings": ["16"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/AuthManager.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\AuthManager.php", "line": 57}, {"index": 23, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 33}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.7713199, "duration": 0.027809999999999998, "duration_str": "27.81ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 96.63}, {"sql": "select * from `customers` where `customers`.`id` = '10' limit 1", "type": "query", "params": [], "bindings": ["10"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/CustomerController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Http\\Controllers\\CustomerController.php", "line": 368}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.809376, "duration": 0.0009699999999999999, "duration_str": "970μs", "memory": 0, "memory_str": null, "filename": "CustomerController.php:368", "source": "app/Http/Controllers/CustomerController.php:368", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FHttp%2FControllers%2FCustomerController.php&line=368", "ajax": false, "filename": "CustomerController.php", "line": "368"}, "connection": "kdmkjkqknb", "start_percent": 96.63, "width_percent": 3.37}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\Customer": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FModels%2FCustomer.php&line=1", "ajax": false, "filename": "Customer.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "ixL6BN3l9tyyUscqj0hv99yKIy0daqhD43fAAWcL", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "16", "pos": "array:1 [\n  2043 => array:9 [\n    \"name\" => \"zello sour\"\n    \"quantity\" => 1\n    \"price\" => \"7.00\"\n    \"id\" => \"2043\"\n    \"tax\" => 0\n    \"subtotal\" => 7.0\n    \"originalquantity\" => 19\n    \"product_tax\" => \"-\"\n    \"product_tax_id\" => 0\n  ]\n]"}, "request": {"path_info": "/customer/check/delivery", "status_code": "<pre class=sf-dump id=sf-dump-1615966325 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1615966325\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>customer_id</span>\" => \"<span class=sf-dump-str title=\"2 characters\">10</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1751260506 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1751260506\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">ixL6BN3l9tyyUscqj0hv99yKIy0daqhD43fAAWcL</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1937 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=1pubyvl%7C2%7Cfwu%7C0%7C1993; _clsk=25xk9k%7C1750165707106%7C8%7C1%7Cl.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6Ikg5L3MvKzdZckdsRW9xc0FlRTNweVE9PSIsInZhbHVlIjoiaFUwL2xUYmgrQW9KUDlyQXQxYWh4allwZDlsaldYdGNaY0MrTHBWMXpuenRwUEpzYXRrRDdKd0prd2lOZFVORHlyd29tWC9nRnkwL0p4R1hDc0Vuclk5RUlvK1lXdkNMRGV5VXpGNGl4ai8vd1hBYUhadndSY3dXUnlkZ0NvNkd1T1RGb1FjQStqQmEvUEhWOFhMam5sd2UxRTNxbUhOY1VwTTQ3Z202UjA2MVN1ZlQwVCtFVDZ1NGNhSXYvZk5JMkdUbEpQSW5jK25DcEtzcTlvZmt3ZzhHYmc5aTdZeW5Ma0xmeElFV3pyYWE5c3Q3R29nRm9kSVROUG5GL2hXMnM3ZzgvN0lJVWllSFRKTnowdFhmdGlmMDcyUlk5WVE2emtGaGVSWnltVDMvRVNYeFpyK1paMEd5cmRiRDNuK3hEdCt5R0NFRS9lVjdzSVpubmRzTXpDUXlRMm9UM2hHdVgrUnRXV3kzRnVUQTZLWFYya010bHR1cWpSUDE2TkZFL2lWSWR6NXVHYmJwMkE5RE4vZFQ1d0dXMzFNQkJHbVJiYW0yWkZmaXNGK0JBbzZnTGZNd0hqQ2k4QzkzNUwzUXFPYUJ6cjI1amhRdklsMGNlQnlUZWUvR3VXRnM1Sit2bktFTGs4QW8rSU5SV1c3QzdZVXhoS1IrMHVlVEo2NTUiLCJtYWMiOiIzN2I0MzI1MDkyZDMwMDZmMzdjZWRkOTRjMjFiNGQwOWY3OGZlNTk5MmI0N2U5ZDMyZTU0M2JjMDAyZWE5MjFmIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6InBydXplWjRDaVJ0elZUVUFsRFh6VlE9PSIsInZhbHVlIjoiNldjRi9ZaWFtbEtrUk1MbkxsZmlYRHZ4SjBQRVZkcFh3K3JMR3graHA2REtZbTltMk1pMTU3d0tJQ1E1NGZDY25kWnJLamlvZm12d002M2pmRWhsdzBwc1FuWk90U2RFVVVCb0p2L25SSzZ2Q3hNU2FhUEgzQm03eVd5N2xSVG1WRkdMbWRRY1ZLQ1RJY0lpVjlVbml2Yk5EVHh3bVhwMjY1Wm9Fb1owUS9vMmlpVDJmT2pocDgrTS9NczF3eEVlSGxYeVJpNldQdkdBc1dZVDI3SkY0ZmFuNzE0M2RVQWphOHc1eTNXZ3FycGd0NDduSUJRY2hwZ0orUGUwMFp4Z1pVWnFhNThNNmVQMDVRL29PbWE1Zmk3K1FqdG51bFcwUXZKV1JpdFI1UFJLNkhlSGJoTnVDcW02MWxsL0xOVXI5NXJjbmQvUkZWaTExOHd3bGl6WnFtWitCUndxdVB1bUdhNFQ2NTI0UmZLSU1IVlpPMkFHQlBSNU1ZeFAyR2h4RVVwamtNOVZDbnVHK3JJZ2swMmJsY3lIWWhKWkR5S2gwamQ0emNiMlJic3I3MEt5SzRqY1RjUGNFUFRoL1lHMWRtWnVySDZZTjVqRG1BOXpsL0tGQWJRZEJWTEwwVDRTUmkwMXpvZUwxUkp3V3k1ZDVWRmNnNGVodS95RU0vankiLCJtYWMiOiIyNTAzNmZmM2Y4YmIxYjMwNjQzZGNkMTk4MjNhMGZjMzFmMDE2NGE5NjcyYjc1ZWZjYWZkODBhN2YzOWUzZGIwIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-848174832 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">ixL6BN3l9tyyUscqj0hv99yKIy0daqhD43fAAWcL</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">coJ97Z8YyNCeFgF4h0jUL1PrpYBjauguIcJbJbm8</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-848174832\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-761139702 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 17 Jun 2025 13:30:59 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Img2Y1NlUE9MNDAyNzRMeEpqVllES2c9PSIsInZhbHVlIjoiRkJMRWFGdjlGUGVCcXpnQ1l1QlFYaUVRVk1LTHN0WHFmejZmMG1lVjN1dURxV0lxOWVpYUtHYk5id3pBT1FJMXVtOXM1VHRqdkI3SklDOFBkSGhRVS9IQThMaDhKM1JWb0g3WERTcWJ5UmFOVndsWkZGRlJvTzVXdTcvRCtVSWpCcXNZam9OV1pEaGhhMUYzVGpLbWtwOUVqazZNNmUyRzhXSFo1dlBkOU8wb2xNR3Y1K2ZtTlBuVmdlNGxRdEZkeFlPNUhqdkoreGtmWGxEMEJqdmRTbXRzb0F0TGVCdTlvejVlSDA4dThLZ1NRZGdobkhxdWlQS1NzWkpZd0U1Wk13enRSUnpYZ2RyL1JzWG9BTXYyVXB4aWRxTTJwVkVjdlpZU3R1WmdxUkNwa256RXZBc3EzZjZpQ1BCTWlya2RkREZGcHROTlR5NWFrRERsM0Q0aW5Oc0p3VSt5UE5UKzJxdm1scHJJNUpMbkl6cTZhQVNSK1hOVlVVbFpUTmV0NTQ2RFlUdytlNzB6NDlNaWdTcTNucDFBeitmN0VWVm1hWFBBbmpQUUhTeEhObysxd0V1a0NXQ1c1T3h1SUdMbE9mSzluNUMrZEl6b05OU0V3RHhCR1hNYko0Nlk3ekpVUEtJczFsUGJIWXBDTWJVWm1FdTF0MnJSTk5od3dRcksiLCJtYWMiOiIwMDg2MDNiZmY1MWU3OTczNDU0MjA0YjRkODFiM2UxZjRkYTc3MDJlMTkxZTg4NDc1YmYyMTc4NWEyYWM3YjllIiwidGFnIjoiIn0%3D; expires=Tue, 17 Jun 2025 15:30:59 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6Im12eU0wY21ON3QvMlhWcGhGWERuTXc9PSIsInZhbHVlIjoiSk5UYWtXMnBVSEE0R3VST3RhVk4zMzVnK2lsbTdaWmtUeitaViswMUx1Y1U4MjFpRzFIUENNeTZ4bnFNdUcwZmVKNXpPc2JXYjZYdnlTU0FPbFNiSmJuREI2dy9iMHN0TmtJcVVoNVhTV1NEa05LVmlzWlcwWk1KcHZweW40cHhsaTFOeVZxUVYyZDVKZlVmVFpsa1JscGhjc3FwMUFYelp0VzgvZlhvd3BQYVVYZityV3NyOGl6S1VYeFU2MWZOUXE5L2liQmhna1QyQzlEWUpXUnd4RDNPZ2ZVaU81NFNNWDVOYUxhOXBlbStqeUVKNzV2QlQ0enl0bjNSZ0c4RHF5djlvc0lDM1F4bjFBNC9IWWJtYnVqaFR1ZElLRjhNZ3FQd3V6OGN5YXRZL3RUdFhuRzVPSldRUGFLOExDZUltcktBMVBUSFN4LzZUM0RCWXdHc2Y3eUVFM01xM2xIcGdqak0rWjBpTWVkWHZYN1ozR0g1ZlpaN0xsVXVJUHpkWTFkVEJLWUdZbC9zVzV4SlZmaTB2RER2MnV2QnZjR2FYTWxnTUVHZkEzTXBFQlFuNnh3SkdxbGdpNnczUHoxUEQwM2RIU0NBY0FNTUF1WXNsZnJoL3BlOW1TRFczNW9OR0JDTHhHK3ZONkx5T2pMc05uc0lyNHVmazNwYkpJT3QiLCJtYWMiOiJhODljNjUwOGZmYzJjYjkwYWIzOTMzNTJlMGMxOWNlNGVmMjM3YTE3ODAzNDI1NTRjMTRjZTFhMWQwMmZlZDMwIiwidGFnIjoiIn0%3D; expires=Tue, 17 Jun 2025 15:30:59 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Img2Y1NlUE9MNDAyNzRMeEpqVllES2c9PSIsInZhbHVlIjoiRkJMRWFGdjlGUGVCcXpnQ1l1QlFYaUVRVk1LTHN0WHFmejZmMG1lVjN1dURxV0lxOWVpYUtHYk5id3pBT1FJMXVtOXM1VHRqdkI3SklDOFBkSGhRVS9IQThMaDhKM1JWb0g3WERTcWJ5UmFOVndsWkZGRlJvTzVXdTcvRCtVSWpCcXNZam9OV1pEaGhhMUYzVGpLbWtwOUVqazZNNmUyRzhXSFo1dlBkOU8wb2xNR3Y1K2ZtTlBuVmdlNGxRdEZkeFlPNUhqdkoreGtmWGxEMEJqdmRTbXRzb0F0TGVCdTlvejVlSDA4dThLZ1NRZGdobkhxdWlQS1NzWkpZd0U1Wk13enRSUnpYZ2RyL1JzWG9BTXYyVXB4aWRxTTJwVkVjdlpZU3R1WmdxUkNwa256RXZBc3EzZjZpQ1BCTWlya2RkREZGcHROTlR5NWFrRERsM0Q0aW5Oc0p3VSt5UE5UKzJxdm1scHJJNUpMbkl6cTZhQVNSK1hOVlVVbFpUTmV0NTQ2RFlUdytlNzB6NDlNaWdTcTNucDFBeitmN0VWVm1hWFBBbmpQUUhTeEhObysxd0V1a0NXQ1c1T3h1SUdMbE9mSzluNUMrZEl6b05OU0V3RHhCR1hNYko0Nlk3ekpVUEtJczFsUGJIWXBDTWJVWm1FdTF0MnJSTk5od3dRcksiLCJtYWMiOiIwMDg2MDNiZmY1MWU3OTczNDU0MjA0YjRkODFiM2UxZjRkYTc3MDJlMTkxZTg4NDc1YmYyMTc4NWEyYWM3YjllIiwidGFnIjoiIn0%3D; expires=Tue, 17-Jun-2025 15:30:59 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6Im12eU0wY21ON3QvMlhWcGhGWERuTXc9PSIsInZhbHVlIjoiSk5UYWtXMnBVSEE0R3VST3RhVk4zMzVnK2lsbTdaWmtUeitaViswMUx1Y1U4MjFpRzFIUENNeTZ4bnFNdUcwZmVKNXpPc2JXYjZYdnlTU0FPbFNiSmJuREI2dy9iMHN0TmtJcVVoNVhTV1NEa05LVmlzWlcwWk1KcHZweW40cHhsaTFOeVZxUVYyZDVKZlVmVFpsa1JscGhjc3FwMUFYelp0VzgvZlhvd3BQYVVYZityV3NyOGl6S1VYeFU2MWZOUXE5L2liQmhna1QyQzlEWUpXUnd4RDNPZ2ZVaU81NFNNWDVOYUxhOXBlbStqeUVKNzV2QlQ0enl0bjNSZ0c4RHF5djlvc0lDM1F4bjFBNC9IWWJtYnVqaFR1ZElLRjhNZ3FQd3V6OGN5YXRZL3RUdFhuRzVPSldRUGFLOExDZUltcktBMVBUSFN4LzZUM0RCWXdHc2Y3eUVFM01xM2xIcGdqak0rWjBpTWVkWHZYN1ozR0g1ZlpaN0xsVXVJUHpkWTFkVEJLWUdZbC9zVzV4SlZmaTB2RER2MnV2QnZjR2FYTWxnTUVHZkEzTXBFQlFuNnh3SkdxbGdpNnczUHoxUEQwM2RIU0NBY0FNTUF1WXNsZnJoL3BlOW1TRFczNW9OR0JDTHhHK3ZONkx5T2pMc05uc0lyNHVmazNwYkpJT3QiLCJtYWMiOiJhODljNjUwOGZmYzJjYjkwYWIzOTMzNTJlMGMxOWNlNGVmMjM3YTE3ODAzNDI1NTRjMTRjZTFhMWQwMmZlZDMwIiwidGFnIjoiIn0%3D; expires=Tue, 17-Jun-2025 15:30:59 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-761139702\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1232266494 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">ixL6BN3l9tyyUscqj0hv99yKIy0daqhD43fAAWcL</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>16</span>\n  \"<span class=sf-dump-key>pos</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-key>2043</span> => <span class=sf-dump-note>array:9</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"10 characters\">zello sour</span>\"\n      \"<span class=sf-dump-key>quantity</span>\" => <span class=sf-dump-num>1</span>\n      \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"4 characters\">7.00</span>\"\n      \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"4 characters\">2043</span>\"\n      \"<span class=sf-dump-key>tax</span>\" => <span class=sf-dump-num>0</span>\n      \"<span class=sf-dump-key>subtotal</span>\" => <span class=sf-dump-num>7.0</span>\n      \"<span class=sf-dump-key>originalquantity</span>\" => <span class=sf-dump-num>19</span>\n      \"<span class=sf-dump-key>product_tax</span>\" => \"<span class=sf-dump-str>-</span>\"\n      \"<span class=sf-dump-key>product_tax_id</span>\" => <span class=sf-dump-num>0</span>\n    </samp>]\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1232266494\", {\"maxDepth\":0})</script>\n"}}