{"__meta": {"id": "X3d3566bac4e16ba0cb42c12552a0900f", "datetime": "2025-06-17 13:32:09", "utime": **********.915046, "method": "GET", "uri": "/", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.264922, "end": **********.915069, "duration": 0.6501471996307373, "duration_str": "650ms", "measures": [{"label": "Booting", "start": **********.264922, "relative_start": 0, "end": **********.850878, "relative_end": **********.850878, "duration": 0.5859560966491699, "duration_str": "586ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.850897, "relative_start": 0.585975170135498, "end": **********.915071, "relative_end": 1.9073486328125e-06, "duration": 0.06417393684387207, "duration_str": "64.17ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 44439928, "peak_usage_str": "42MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET /", "middleware": "web, XSS, revalidate", "controller": "App\\Http\\Controllers\\DashboardController@landingpage", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FHttp%2FControllers%2FDashboardController.php&line=59\" onclick=\"\">app/Http/Controllers/DashboardController.php:59-74</a>"}, "queries": {"nb_statements": 1, "nb_failed_statements": 0, "accumulated_duration": 0.00551, "accumulated_duration_str": "5.51ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Models\\Utility.php", "line": 46}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Models\\Utility.php", "line": 78}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Http\\Controllers\\DashboardController.php", "line": 66}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.897517, "duration": 0.00551, "duration_str": "5.51ms", "memory": 0, "memory_str": null, "filename": "Utility.php:46", "source": "app/Models/Utility.php:46", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FModels%2FUtility.php&line=46", "ajax": false, "filename": "Utility.php", "line": "46"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 100}]}, "models": {"data": [], "count": 0, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "KSCsI6gWVzXFI1KEKZvZuoX2S6RILcyTiSY1GsfV", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/", "status_code": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1489335108 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1489335108\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1188773872 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1188773872\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1358818195 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-purpose</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"18 characters\">prefetch;prerender</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>purpose</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">prefetch</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">none</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1358818195\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-32971043 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-32971043\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-2134856182 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"55 characters\">max-age=0, must-revalidate, no-cache, no-store, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 17 Jun 2025 13:32:09 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/login</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pragma</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>expires</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 01 Jan 1990 00:00:00 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IkM0WEhuNU8xM2J4WXFqYTUyYVAvU3c9PSIsInZhbHVlIjoid1ptNVNTakhNdkcrYWorY2RIaERqSHhqdUNqU3V0R1ZIV0RhU3hhSDR5bXZKMHEyaHFxTStaQTdBbmRlak9aVmFMdWZHQi9wQUR0Z0hYS3pBbTlDNW83K3pxb0xwVW9TZFl4UkZ3Q0RUMTVmMG9KZzdEY1JuQnJ0Wk55aDB5NmtIMlZGMHhvYnJrY1E0Y3NJREZqQS9GSytObmp3bjhxT0tDZGpzSEtCYUVyakpvM0JoaXUzY2twZ3ZPNVlZQUw0bmRCVUJ2THJVNXVnUDdsREtNZWJyN3N1VGwyaVN5ZXdFQlg1a0NNNkx2d3JMOFZFaUlTQ1VRMVNpck85QW13bmVqejBxbCtMYzBqNTZjVGR2cjlSdkk4T0xIVkIrdmk3QWN1VDAvbHhSNUF5a2xIYzlWVThHdEtQcENjU1ZyUG81aVAyUHZEQnJZNWI4Y1dRaDQzWUpwNU4rOThZaEhkaUtyVkNLUWZJSkxsK0hzVmNvZWRBY0Vuci9LTys4YWF4eGZWN2FjYlpybjBJNG8yUDlSVU5pUkJFc2N1MVVPc3VNeklSUVdwN25QdXU3UXBPNTZ5bWF6NUlaNlFpRFZKV2V1NTkwcGRsaGpnTEhSR0NkUXoxL3I1RmhPTmlVc3RtR1U5c2F2aEdsZ044dE1nK28vWVhlUGZDOUJIM3lnN1AiLCJtYWMiOiI5MGMwMWM5ZDc1ZGZhODY2ODgwZmJiMWM4YzFmNzU2MmFiMjIyOWE1ZjFiY2ZlOTljNTcxOTEzODEzNDUxOTdjIiwidGFnIjoiIn0%3D; expires=Tue, 17 Jun 2025 15:32:09 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6IjMvWE1IUFBEZmZ3eWpHMVU3VTNrUkE9PSIsInZhbHVlIjoiYmxmaS9WamRWNEtMeHhPcCtoY3FZbG1ZMllYWWw4elRyQWlLcDNzOTcwS2oweG9HSkRnU0tlSXVGZStrNG11MDh2Y1pYNGFWVEczVU9TTTNkdytyNzlXbUhhd3dDc1VqN24yZ3pTSnFIS2FkSmZ6MDhoSUg4RFZxOGtldGhsTnRTZnhNY1FPakRlMittTTFydGsvZFFvTVZtVXZzYXptaEZwZGE5MERON0wzZFlVTjJoaWNNbnJ5VytQb2Z1TDZhVWhPM1ZYU0Q5M01kMTFKNjhzODJmOWNvbndraCtQekcvb2drN0F4QThHNnBDemhMZnNpcFhIYVMvSjA4WCtId01YcnNTbEI3dlVDZitwYWc3Q0xFVGRORTVCQTBWK0N0SXh0WElKTVI5d2ZPbldDc1lBUTd6bThKUWF3T3lzczNTWjZBUEw5eVRGU1hkUXkzdGNveWxlQ2xnTllDT29pVXZFR2VoRnlENzFNemVSWnNSZUlQMU1FUGpBRjRtanpOSzNjQjRJb3hlaWprVllldWZhNEIzaXliMjJxRkorRzJtWURENnJ1bVl0YlptR1gxak5QU2NCUlJNUzU5U1N4UjY4aGZ2TmgrSWhzUytmbXQxV2JVMmFKSHpmb3hqeXQ4WmpVdVdSYjVpZ3JrZWxZeDI4Wk1NN1N4OUhqMEl0a1ciLCJtYWMiOiI0NmVjZjMzNTEzOTQwZjJlMmI4ZTAyYzhjYTcyMjk2YzlhYjY4YmEzYTgxZjVhMWM0NDJhZjYyYjA5YTM2YzQ0IiwidGFnIjoiIn0%3D; expires=Tue, 17 Jun 2025 15:32:09 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IkM0WEhuNU8xM2J4WXFqYTUyYVAvU3c9PSIsInZhbHVlIjoid1ptNVNTakhNdkcrYWorY2RIaERqSHhqdUNqU3V0R1ZIV0RhU3hhSDR5bXZKMHEyaHFxTStaQTdBbmRlak9aVmFMdWZHQi9wQUR0Z0hYS3pBbTlDNW83K3pxb0xwVW9TZFl4UkZ3Q0RUMTVmMG9KZzdEY1JuQnJ0Wk55aDB5NmtIMlZGMHhvYnJrY1E0Y3NJREZqQS9GSytObmp3bjhxT0tDZGpzSEtCYUVyakpvM0JoaXUzY2twZ3ZPNVlZQUw0bmRCVUJ2THJVNXVnUDdsREtNZWJyN3N1VGwyaVN5ZXdFQlg1a0NNNkx2d3JMOFZFaUlTQ1VRMVNpck85QW13bmVqejBxbCtMYzBqNTZjVGR2cjlSdkk4T0xIVkIrdmk3QWN1VDAvbHhSNUF5a2xIYzlWVThHdEtQcENjU1ZyUG81aVAyUHZEQnJZNWI4Y1dRaDQzWUpwNU4rOThZaEhkaUtyVkNLUWZJSkxsK0hzVmNvZWRBY0Vuci9LTys4YWF4eGZWN2FjYlpybjBJNG8yUDlSVU5pUkJFc2N1MVVPc3VNeklSUVdwN25QdXU3UXBPNTZ5bWF6NUlaNlFpRFZKV2V1NTkwcGRsaGpnTEhSR0NkUXoxL3I1RmhPTmlVc3RtR1U5c2F2aEdsZ044dE1nK28vWVhlUGZDOUJIM3lnN1AiLCJtYWMiOiI5MGMwMWM5ZDc1ZGZhODY2ODgwZmJiMWM4YzFmNzU2MmFiMjIyOWE1ZjFiY2ZlOTljNTcxOTEzODEzNDUxOTdjIiwidGFnIjoiIn0%3D; expires=Tue, 17-Jun-2025 15:32:09 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6IjMvWE1IUFBEZmZ3eWpHMVU3VTNrUkE9PSIsInZhbHVlIjoiYmxmaS9WamRWNEtMeHhPcCtoY3FZbG1ZMllYWWw4elRyQWlLcDNzOTcwS2oweG9HSkRnU0tlSXVGZStrNG11MDh2Y1pYNGFWVEczVU9TTTNkdytyNzlXbUhhd3dDc1VqN24yZ3pTSnFIS2FkSmZ6MDhoSUg4RFZxOGtldGhsTnRTZnhNY1FPakRlMittTTFydGsvZFFvTVZtVXZzYXptaEZwZGE5MERON0wzZFlVTjJoaWNNbnJ5VytQb2Z1TDZhVWhPM1ZYU0Q5M01kMTFKNjhzODJmOWNvbndraCtQekcvb2drN0F4QThHNnBDemhMZnNpcFhIYVMvSjA4WCtId01YcnNTbEI3dlVDZitwYWc3Q0xFVGRORTVCQTBWK0N0SXh0WElKTVI5d2ZPbldDc1lBUTd6bThKUWF3T3lzczNTWjZBUEw5eVRGU1hkUXkzdGNveWxlQ2xnTllDT29pVXZFR2VoRnlENzFNemVSWnNSZUlQMU1FUGpBRjRtanpOSzNjQjRJb3hlaWprVllldWZhNEIzaXliMjJxRkorRzJtWURENnJ1bVl0YlptR1gxak5QU2NCUlJNUzU5U1N4UjY4aGZ2TmgrSWhzUytmbXQxV2JVMmFKSHpmb3hqeXQ4WmpVdVdSYjVpZ3JrZWxZeDI4Wk1NN1N4OUhqMEl0a1ciLCJtYWMiOiI0NmVjZjMzNTEzOTQwZjJlMmI4ZTAyYzhjYTcyMjk2YzlhYjY4YmEzYTgxZjVhMWM0NDJhZjYyYjA5YTM2YzQ0IiwidGFnIjoiIn0%3D; expires=Tue, 17-Jun-2025 15:32:09 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2134856182\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-54086981 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">KSCsI6gWVzXFI1KEKZvZuoX2S6RILcyTiSY1GsfV</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-54086981\", {\"maxDepth\":0})</script>\n"}}