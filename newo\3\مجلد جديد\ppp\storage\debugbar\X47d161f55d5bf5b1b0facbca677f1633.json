{"__meta": {"id": "X47d161f55d5bf5b1b0facbca677f1633", "datetime": "2025-06-17 13:51:55", "utime": **********.239553, "method": "POST", "uri": "/chats/favorites", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1750168313.863673, "end": **********.239583, "duration": 1.3759100437164307, "duration_str": "1.38s", "measures": [{"label": "Booting", "start": 1750168313.863673, "relative_start": 0, "end": **********.069078, "relative_end": **********.069078, "duration": 1.2054049968719482, "duration_str": "1.21s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.069101, "relative_start": 1.205428123474121, "end": **********.239587, "relative_end": 4.0531158447265625e-06, "duration": 0.1704859733581543, "duration_str": "170ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 46117984, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/favorites", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getFavorites", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "favorites", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=454\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:454-477</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.02542, "accumulated_duration_str": "25.42ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 16 limit 1", "type": "query", "params": [], "bindings": ["16"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.1557178, "duration": 0.02355, "duration_str": "23.55ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 92.644}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.205216, "duration": 0.00111, "duration_str": "1.11ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 92.644, "width_percent": 4.367}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (16) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.2218878, "duration": 0.00076, "duration_str": "760μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 97.01, "width_percent": 2.99}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "16", "_previous": "array:1 [\n  \"url\" => \"http://localhost/account-dashboard\"\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/favorites", "status_code": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-322499260 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1936 characters\">_clck=2gyfy%7C2%7Cfwu%7C0%7C1994; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clsk=1ti4td2%7C1750167218299%7C2%7C1%7Cl.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6Ijk5eE5PcThPRE8wY3hlYVR5NkZ6eGc9PSIsInZhbHVlIjoiOG1OU0w0QXRtMlphTTVybzU5Q0hRS3BjRFY2S2ViTmJjOGlFZTJnejErSlRWNU43UnA5MWp4MitUcDRPSFhjSTlGYTN5aDZKamduS1MrK3QvRlh5ZUhRdnltWlhKUnIyTTF3YVBxdWo2OEY5aEY2bWwrUGsybFAxNXFRemd4K3lyaS9jS3NOSFhVYlJvRVNqOUtvTUkxaVRGeXVmdllSV043SUtNakJxaVNrTVRHUHNPTkUzMWhYd1dBb1hTWTFPRDFvTGwrTktPNG9uRHRpeEFuS3pDdFFOT21DQjcxemcyVDQvWHVxVDNUdkNFZS9xQTg1YlB2K0EzaXRRT0NZcWdDUlZXcjIwbzg1QzZ6ZjBvanJLVnJ6WWFNcWxZSjBDOFlPTmJuWG9DOG84RFdPSXlOKzNncW40VDV0UXFFS0dkWFNaYlNvbE1uQ1VqcDFlNzUyOFVHVjg2QWhuL0JqQ2ozTEQvajZaVzYvcFY4YlhtSnRPeUViSkxZYzhWUUxKMXZ1MUlndWpjMXhJVXZ4dHNkaFY5WUVWOWtURndiNXlJeGk2TXJNL3R5b0k2L1BsV1gwMG55OW1jT0daV1lINXFFMDR5c0ltWlpjb1R5b1ZDdkZkR0dxblZiWFY1Qi9NMmVhOGhpUHZCRkFGWXlaeTJBNDZ1S3E0VXQ5OHllak0iLCJtYWMiOiJlN2FiYzY4Y2VlNTliNDllZTc4YTZmMjliZTVlMTgzM2Y3OTIxZDI0YWNhZjEyYzhiZGY5MjdmNzMwNGI2MmNmIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6Ik5sQWx2TUQ5c01GVWJTd05sNnRyV2c9PSIsInZhbHVlIjoiNW5mTk5VcXp0SWZOdk9UbUtsRTl3NExUYmR4SC9lTzB5cUpRd2lHKzZ6Ylorb0xEQ2prY2pYR3dzMkc5aEl0YVA5dXI4RnJSeTF6am1VaU9veTBkSVFqZEtZVzZnN21xL3hUMUg3VFFuc3ZBZDY3ZEJLaDl0RUx6U3UrMkVIYUtKRE0wRDFuU1ZGOUdGZ2dTUC9uYVFQUXRGY1pkbVU2Rm9lL0dYcnkvSThTQjNhWUsyL0FiUmNaRjVXNUpucFVvMzg4RzI0WmVvRHYxQkRVYW1qYlFBcXVadnk1MWR3SUg1QmNNZ1dLdEtNMHVkaHlpL3V6NE0rbmVGb296T29uRk1adzZLVTV0SlJqOXR2bnJaZGRYNHRSOWgwZEozYW5kaXV3OFYyV2NqY0ZmOStYTGFMMU9yR2NkQktHUHdPNVdGZmg2d08vc1UrdStYRDFheFA4Snk3VkVwZ3A3cFJINm5nWXZvUjN0Mm1Bc2Z3cDRRWWZuYkk4QW9nVm54akg1T0JZWVRQR25JSnptNzdBZkVsSitHNXA0dHNIMHE2cFRENURobTZ3S2duWCtvU1k4d2xyMkhMa295Zm5NZnRTOEw0dzJ3MUM1dGw5SHR2SmtvTnVwTUtlNnQzdEU1TkUxZm9mREVDT21SQnNiZ1pObTNRUC90STFYUFViZUgrZ2kiLCJtYWMiOiI0NGQyNTUwZmYxYmE5ZmM5MGE4ZWM0MGU4OGQyYWU0YTcwMTRkOWE3MjQ5ODNkZmMwZDVjYWI2OTZmOWNjNzYxIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-322499260\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-227998734 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">VloaUQCBjpDKrel7nHeAzB1sTLdTMwteSos1MmAN</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-227998734\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1724133861 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 17 Jun 2025 13:51:55 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IlNGNlFUUjZPcWZQQzdZUEhvRWEwTHc9PSIsInZhbHVlIjoidmNiMjQ0eUhINzhsZndESHFka2tGaG15ZEZJQXJhMmYrVG01S0tqODBXVFRONElOcWwzUmRZcGFIZkNTVkQ3cW92UDNWNmVZTGJGeWtQWVcvRjlXZG5sUzdRNm9LVlJwcFc0WUVzZVI4cG5tOUpFZlFLN28wZWV4THo5UWJhRXhLSjZockwyNy81bFRzM2tEdFhXajZxS2dIWG9ac2VVa20wUVN1OFk2ZkpiL1RMOUxaRnlGZzFwS2Faenh4QysxMXFjdFBOc1hwYkRPN056R1A3bG9FZWtmY2tleDJ6NCtJMjdmcUd5QWtSVDhUcm1XTVNvNWd4TFNvdEtZUWVZdktnb2pWZHJFZlB6TS9pdkl5cTd2b00wM25Kay9EZW0yNDBXMUYxdU5jLzE0OU54WjhGUEVuTHdNZFhyMTNRekZ0ak5zeHM0aGN0YXdqUDA1UnBiVlNURnErb0huTzFHalJQWmhmd3RoTWY5Mjh6d1Bva09iQi9qVDJjRXpUSFdSVEJtT1lIcXpDUUdoTDlIQ1RFbDVsdHhPbkxjSmZzUjhBYytqRi9WU0FPbGFOQitOUHhNdHNhT1hDT3lUa3dLdTlDQy9Pc0cwRU5PZkRyOVNDZnR2RE12aUt3TDRuYVU2WDJWNjJLazhEdTZEVWJweTYrS0RmTzZqRjlHR3lJbXYiLCJtYWMiOiJhYjcxYjEwNGUzY2QxZTM2MDNlNWZiNTRlMGQ4MzEwMTdiNjJjNzc5MjVlNmUxZmIwMTE3NzU1ZTE5Mjk3NDY5IiwidGFnIjoiIn0%3D; expires=Tue, 17 Jun 2025 15:51:55 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6Im1aQmF5OFNJRjR5OVpNazNabGdJOHc9PSIsInZhbHVlIjoiQmd4UFpycmFnZTVsRFRhK2RKRFdtRmFFVjJianF4MGNHTlAreHhZRG1EY3MvQ1hvNDlyS1V5WCt3dlpockhqNTFubU9oUWMyZ2xYZDlQaHRiUWxrZVlKR0FudWJVQ0k4cmVjS1pXV3lGclA5ZGl0emd1bHlVajcwMFFPdHA4clQvbCtJdDVIb0piWkFyUXdYNGxxTDBibysxQmp6N1kvMEU4MW9idFV3eTZoN0o4a1d0R0hhR01hd1F1eW5xeTQ2b0Y0K0JKQjViTXUzaWwrSDFWTnZmcU5BR3NRS2FDM3JrRWllMDR1b2VBVmNnamh1bzVSQUtURVhFR0ZVMEg3ZldGRm01MEFGY3htOEtrenJObkxhUzI5UXlORTBGRktwUmpmWXJ4ZnVKUDlRUUdKZXRyUEZYNmlPSjRIRDZOblBocHRXSTBORlFSU2l3MU5jckZCbkkzbHJQbjNtWkxnZ0hhUDUwbUxrbHVmWHNoWUhkY0djOFVFVHlnWDhDSGVqaWhFVzFOdCtZOGQ1TlhaVmxpZVZzeHNCWk9lZGhjR0hPVll0NmRBMGVxZldsc1JleTQ5emxoSncyd2YvMTdxRS93aVBJUkhKR3BsUU9hQnlNbWVVTUoyalFCNUU4L1BaTHdzMnBzb1hIb0ZFcVh4R2NTTEI4SFVQcVFBZ0VnTDkiLCJtYWMiOiI4MmM4ZTIzMTk3OGIzYTYyODdlOTJjZjRiZGFiYzk2NWE5NmViYzY0MWI1MjllMjM1MDFiMmFjYzdkOWFlMDY5IiwidGFnIjoiIn0%3D; expires=Tue, 17 Jun 2025 15:51:55 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IlNGNlFUUjZPcWZQQzdZUEhvRWEwTHc9PSIsInZhbHVlIjoidmNiMjQ0eUhINzhsZndESHFka2tGaG15ZEZJQXJhMmYrVG01S0tqODBXVFRONElOcWwzUmRZcGFIZkNTVkQ3cW92UDNWNmVZTGJGeWtQWVcvRjlXZG5sUzdRNm9LVlJwcFc0WUVzZVI4cG5tOUpFZlFLN28wZWV4THo5UWJhRXhLSjZockwyNy81bFRzM2tEdFhXajZxS2dIWG9ac2VVa20wUVN1OFk2ZkpiL1RMOUxaRnlGZzFwS2Faenh4QysxMXFjdFBOc1hwYkRPN056R1A3bG9FZWtmY2tleDJ6NCtJMjdmcUd5QWtSVDhUcm1XTVNvNWd4TFNvdEtZUWVZdktnb2pWZHJFZlB6TS9pdkl5cTd2b00wM25Kay9EZW0yNDBXMUYxdU5jLzE0OU54WjhGUEVuTHdNZFhyMTNRekZ0ak5zeHM0aGN0YXdqUDA1UnBiVlNURnErb0huTzFHalJQWmhmd3RoTWY5Mjh6d1Bva09iQi9qVDJjRXpUSFdSVEJtT1lIcXpDUUdoTDlIQ1RFbDVsdHhPbkxjSmZzUjhBYytqRi9WU0FPbGFOQitOUHhNdHNhT1hDT3lUa3dLdTlDQy9Pc0cwRU5PZkRyOVNDZnR2RE12aUt3TDRuYVU2WDJWNjJLazhEdTZEVWJweTYrS0RmTzZqRjlHR3lJbXYiLCJtYWMiOiJhYjcxYjEwNGUzY2QxZTM2MDNlNWZiNTRlMGQ4MzEwMTdiNjJjNzc5MjVlNmUxZmIwMTE3NzU1ZTE5Mjk3NDY5IiwidGFnIjoiIn0%3D; expires=Tue, 17-Jun-2025 15:51:55 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6Im1aQmF5OFNJRjR5OVpNazNabGdJOHc9PSIsInZhbHVlIjoiQmd4UFpycmFnZTVsRFRhK2RKRFdtRmFFVjJianF4MGNHTlAreHhZRG1EY3MvQ1hvNDlyS1V5WCt3dlpockhqNTFubU9oUWMyZ2xYZDlQaHRiUWxrZVlKR0FudWJVQ0k4cmVjS1pXV3lGclA5ZGl0emd1bHlVajcwMFFPdHA4clQvbCtJdDVIb0piWkFyUXdYNGxxTDBibysxQmp6N1kvMEU4MW9idFV3eTZoN0o4a1d0R0hhR01hd1F1eW5xeTQ2b0Y0K0JKQjViTXUzaWwrSDFWTnZmcU5BR3NRS2FDM3JrRWllMDR1b2VBVmNnamh1bzVSQUtURVhFR0ZVMEg3ZldGRm01MEFGY3htOEtrenJObkxhUzI5UXlORTBGRktwUmpmWXJ4ZnVKUDlRUUdKZXRyUEZYNmlPSjRIRDZOblBocHRXSTBORlFSU2l3MU5jckZCbkkzbHJQbjNtWkxnZ0hhUDUwbUxrbHVmWHNoWUhkY0djOFVFVHlnWDhDSGVqaWhFVzFOdCtZOGQ1TlhaVmxpZVZzeHNCWk9lZGhjR0hPVll0NmRBMGVxZldsc1JleTQ5emxoSncyd2YvMTdxRS93aVBJUkhKR3BsUU9hQnlNbWVVTUoyalFCNUU4L1BaTHdzMnBzb1hIb0ZFcVh4R2NTTEI4SFVQcVFBZ0VnTDkiLCJtYWMiOiI4MmM4ZTIzMTk3OGIzYTYyODdlOTJjZjRiZGFiYzk2NWE5NmViYzY0MWI1MjllMjM1MDFiMmFjYzdkOWFlMDY5IiwidGFnIjoiIn0%3D; expires=Tue, 17-Jun-2025 15:51:55 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1724133861\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>16</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n"}}