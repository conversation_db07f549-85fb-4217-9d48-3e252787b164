{"__meta": {"id": "X4a638ea8baa06ff9e9fb57474ae6d8e6", "datetime": "2025-06-17 13:30:33", "utime": **********.824375, "method": "GET", "uri": "/add-to-cart/2043/pos", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.032174, "end": **********.824405, "duration": 0.7922308444976807, "duration_str": "792ms", "measures": [{"label": "Booting", "start": **********.032174, "relative_start": 0, "end": **********.63446, "relative_end": **********.63446, "duration": 0.6022858619689941, "duration_str": "602ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.634478, "relative_start": 0.6023039817810059, "end": **********.824408, "relative_end": 3.0994415283203125e-06, "duration": 0.18992996215820312, "duration_str": "190ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 49802496, "peak_usage_str": "47MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET add-to-cart/{id}/{session}", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\ProductServiceController@addToCart", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FHttp%2FControllers%2FProductServiceController.php&line=1338\" onclick=\"\">app/Http/Controllers/ProductServiceController.php:1338-1571</a>"}, "queries": {"nb_statements": 7, "nb_failed_statements": 0, "accumulated_duration": 0.02497, "accumulated_duration_str": "24.97ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 16 limit 1", "type": "query", "params": [], "bindings": ["16"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.712611, "duration": 0.016829999999999998, "duration_str": "16.83ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 67.401}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.747917, "duration": 0.00106, "duration_str": "1.06ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 67.401, "width_percent": 4.245}, {"sql": "select `permissions`.*, `model_has_permissions`.`model_id` as `pivot_model_id`, `model_has_permissions`.`permission_id` as `pivot_permission_id`, `model_has_permissions`.`model_type` as `pivot_model_type` from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `model_has_permissions`.`model_id` = 16 and `model_has_permissions`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["16", "App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 326}, {"index": 21, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 566}], "start": **********.7784688, "duration": 0.00094, "duration_str": "940μs", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:326", "source": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php:326", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=326", "ajax": false, "filename": "HasPermissions.php", "line": "326"}, "connection": "kdmkjkqknb", "start_percent": 71.646, "width_percent": 3.765}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (16) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 312}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 26, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}], "start": **********.7826, "duration": 0.0007700000000000001, "duration_str": "770μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 75.41, "width_percent": 3.084}, {"sql": "select * from `product_services` where `product_services`.`id` = '2043' limit 1", "type": "query", "params": [], "bindings": ["2043"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/ProductServiceController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Http\\Controllers\\ProductServiceController.php", "line": 1342}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.790164, "duration": 0.00082, "duration_str": "820μs", "memory": 0, "memory_str": null, "filename": "ProductServiceController.php:1342", "source": "app/Http/Controllers/ProductServiceController.php:1342", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FHttp%2FControllers%2FProductServiceController.php&line=1342", "ajax": false, "filename": "ProductServiceController.php", "line": "1342"}, "connection": "kdmkjkqknb", "start_percent": 78.494, "width_percent": 3.284}, {"sql": "select sum(`quantity`) as aggregate from `warehouse_products` where `product_id` = 2043 and exists (select * from `warehouses` where `warehouse_products`.`warehouse_id` = `warehouses`.`id` and `created_by` = 15)", "type": "query", "params": [], "bindings": ["2043", "15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/ProductService.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Models\\ProductService.php", "line": 155}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/ProductServiceController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Http\\Controllers\\ProductServiceController.php", "line": 1346}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.799261, "duration": 0.00387, "duration_str": "3.87ms", "memory": 0, "memory_str": null, "filename": "ProductService.php:155", "source": "app/Models/ProductService.php:155", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FModels%2FProductService.php&line=155", "ajax": false, "filename": "ProductService.php", "line": "155"}, "connection": "kdmkjkqknb", "start_percent": 81.778, "width_percent": 15.499}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Models\\Utility.php", "line": 4725}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Models\\Utility.php", "line": 4659}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/ProductServiceController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Http\\Controllers\\ProductServiceController.php", "line": 1415}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.806184, "duration": 0.00068, "duration_str": "680μs", "memory": 0, "memory_str": null, "filename": "Utility.php:4725", "source": "app/Models/Utility.php:4725", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FModels%2FUtility.php&line=4725", "ajax": false, "filename": "Utility.php", "line": "4725"}, "connection": "kdmkjkqknb", "start_percent": 97.277, "width_percent": 2.723}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}, "App\\Models\\ProductService": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FModels%2FProductService.php&line=1", "ajax": false, "filename": "ProductService.php", "line": "?"}}}, "count": 3, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 1, "messages": [{"message": "[\n  ability => manage product & service,\n  result => true,\n  user => 16,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-788394691 data-indent-pad=\"  \"><span class=sf-dump-note>manage product & service</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"24 characters\">manage product &amp; service</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>16</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-788394691\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.78886, "xdebug_link": null}]}, "session": {"_token": "ixL6BN3l9tyyUscqj0hv99yKIy0daqhD43fAAWcL", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "16", "pos": "array:1 [\n  2043 => array:9 [\n    \"name\" => \"zello sour\"\n    \"quantity\" => 1\n    \"price\" => \"7.00\"\n    \"id\" => \"2043\"\n    \"tax\" => 0\n    \"subtotal\" => 7.0\n    \"originalquantity\" => 19\n    \"product_tax\" => \"-\"\n    \"product_tax_id\" => 0\n  ]\n]"}, "request": {"path_info": "/add-to-cart/2043/pos", "status_code": "<pre class=sf-dump id=sf-dump-1478653136 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1478653136\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-1804952611 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1804952611\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1704062874 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1704062874\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">ixL6BN3l9tyyUscqj0hv99yKIy0daqhD43fAAWcL</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1937 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=1pubyvl%7C2%7Cfwu%7C0%7C1993; _clsk=25xk9k%7C1750165707106%7C8%7C1%7Cl.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IlhROEYzMm9waE1kaWFSTVl0QzA3dVE9PSIsInZhbHVlIjoiMUI4aEFMUzUxWS9WWThnVUduS0wwQStRWGptWjVvb0hLaGFpOVFDeDR6YlI1cElkQzRMUVJXYzBZS2ExL1ovT3N6UmorOVdpN3gxQWRON1l6bzdpNHRvT3RNSTNWc2dUMmwrK1ZUcXV2Lzc5bjdGbkZURTVHOFMxMVlmS0cwc1prYjNTczl6ZHV1KzNLSHY4OWY2MlZFdXpmeSswTyt3ZWs0Q0xJbTZ1U05GV20xRWwybDlLVFI1VUZmVHVJcHQ1S0c4VWtEMWp0cmFuWUVkODIvcGxoS2k5RXE4ZE9vb2JKanJLcERXVGZIckVTS01wOEkwSTVDVEV2aFh2eWxlVkhJTUF1VlRPUGVmaUJ5ZlpoOGh0UEpEZnRneTh5NHFETng0eVJYcUJEMjlTQnZHRW55Ykl2Q3FzSkJPTjkySmxCcytkZWRsTjVRQ2wxZVBGaWpkTTdTWXhJei9KR1IwdnJ5OHZDOVJvdy9rQU9ORTNKSEt6dlNLR1AyNWUxUE9zOHhua0JqM3ZKaHR4TDJXVzRBWHplMDBKTlBQMm5ZM2RCZ1BRcWo1NXJDYzhiQ3E1UXRhUGQ3SjNJWGptekkyNVllN3VtYytuMHY3T1NublUyT2lpRGgzV2dyUHRPS0lweUhBbjVNTGNramtkR1YwY04rRkFvNUwvNEN2ejJKQ20iLCJtYWMiOiJmNDBkNjczNzA1NjgyNmI3ZTg2OGQ1ZDY4ZTFkZmY4YWQ0NTVjN2RmZDg2ZjhkMjAwZTVjM2ZlZjNkNGRjNDQ5IiwidGFnIjoiIn0%3D; test_session=eyJpdiI6IktwenFWeEZJajJZUzJ1Tm0xQ0lucmc9PSIsInZhbHVlIjoiM2pMVjhJazRCZGdGQUJ0bzgwd3VndExEK0FzMlZpZHdzZWpEMm9pOWRXQi9EYjJYcWd1b1hJMzlwRDZTMTdGWTlnbFNFSlg4bTlTdU1nalNXblVFMUlnbFo5RG9VZ3A5eEwzYkViVHR1Nk8xeWkrVVhST3c3NzA0cmpsd09vcVlQUzMyZk15UjVENDQ5ekRucWRDNkYxUUlrQXJDK0wyL0hSZiszenFXR29XblgwUzhaVS90aDRvd1l4NnRQNS8xNWpyVTNOdFlGSk1TNkFpRkpGN1dmUUdiaEtKNWswd29yNTI5aUJwclJaQXN2ai85MHJBZFpuc0lrZHVPd0lodjB0TS9HdGdWVUhqdnk1NnNNMEwwTkwzS2NXb0NJaEJhTFRvckUrSU1POFAvT0lYTUJsMU53RzJGNXhXY3NaMTEwWEhtL2cvbUNodCtFWFdOVW0yOEkyRXF3UDd6VmtrcGtVQ09FVDZVVlJnZUs2elIwNFg5dU5WbWFYQ0EyckFGVGZ1V1hDRCtXOXRrbnBFdjR1d2h4Tm50QWk1dzZmclMrL2VETXJ0S2tvNFh4d2oyYlhQU05OUEVXNnB1eXgvS2E3VHV4VHdHd0FSVit4T3Z6RHFyZlI3RVlqMkpuUXY2VzVtVitTS2llTkhPQ3lSSlRxa003aVRzRkY0RUFiMWsiLCJtYWMiOiI2ZWViNmJmYmEyMWJmN2RkNTJjNGJhZGM3NjdiOGY4ZjY4M2QwZGMyZmU1NDkwZTJmZDg4NDY3YWIxOGJkNGUxIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-836602560 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">ixL6BN3l9tyyUscqj0hv99yKIy0daqhD43fAAWcL</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">coJ97Z8YyNCeFgF4h0jUL1PrpYBjauguIcJbJbm8</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-836602560\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1194158472 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 17 Jun 2025 13:30:33 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Ik01eFhlMWlISFplR01Xamwrb0p4WEE9PSIsInZhbHVlIjoiditzZG4ydEE4alNxVVFUS2pDQVE3TFRZdWsxMEhoeW5CUzZ5M2hYTy8xUzE2RnVWSUZLTDJNSzkxbHBjL0dDL3FFUEZkT1FCVnlnVTNPQi9uZ0FFZlpUZ0NoSlhXaEIvdDJXTG4vL1pSZUZNM0NIa1U4QnNGc1NpMExBMURHWjd6aFNkQUkyS0pqK2RlZ0V3UWFmTjllbHdUdTh2WmdtSjBQcGUwMEVRcW9sWE93a05oNFhVZVRaekZnaWNLMGFEM2NOeUFmVFRHRzRFMFF6Y0RTWnpDcW9tODdlWW9pTkRjbHNIK1hJUCtoYjUyZEhQVlpaWExvcnFVYVA3dlBGcXIrRXlCdnprbjZwSzFBRjBqUnBlQjRwenVtVTUyMmY5dkhQaE1tTm5hWFF4TVZpaHBPYWlUUzlZSTNoaWcwRGloYXNlb3B4WEM2ZVNnbEF5aloyS0VBSW00R3BVc2tSNTdRLzU1VTF2V2MxNVkzWC9tZk9mNFVObnJ5a2RPbUpJQmh3UWI3YTMzcFJpU3hZb2xXalFHYzdyYStxWVVrTmRvV0k1NGd0T001ZWFUenQ5YzRMekR1cWM5SkZBbllEU0s0QkVIa0E5MEZwT2VVOUhGcEM5dHVTVWRYdFMzYWRYOVVmSW1STE0rU2VXdjRaV21tZVY1dThiSmdscmFyZzQiLCJtYWMiOiI5NWVlMDA0OTFiYzcyZGQ0MDhiODIxN2VhZTI4YzE4NDJlZDBlZWEyYTQ5NjAwNmI2YzJiNDRhZGE5NWZjMGRmIiwidGFnIjoiIn0%3D; expires=Tue, 17 Jun 2025 15:30:33 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6IjlkSUNkYnVsbkZkTEM5WCs1UFdqMGc9PSIsInZhbHVlIjoiajNBR29adzRKQXREcVJFREZFTWhaMHJLek03d0MzVi8wMGxlWmllRi9uTElKa2VzQlRwbjlLR1RQSzVoWTYrRkE4N3U0UTZqU2lGK0JIQ3RFeEI3dGM4WEg1WTcyL2ZVYzQyZXVoTzRlcmhjS2RaUjZxUFhIcU9QTVZVcHlOdWZqbW14NHNiS0ZkcVNOeVZNUzlLWDY5UmlEMnRTamx0cS9LaEpXNldyL0dweDJoQlA5d0c2azU1WDlQRXZvZnVKSlZRUTFIZTBaYmFYU0xaM1hUUkIwV3NrRmwvc2h6cVVlUGowUnJJUW1VVVN3T1M3KzhFT0o0QWhUSUxBVnlVSWtzSVIxL1hxaGhNNjU4MFZmUTgvOXV2QUVVU3JYUkRLSmRXS1NnUEJ1bjl6VFp6VDQyV2dDeFZPSEh6Y21aVTNnclFDWGxKN0xGbk5seGtnRE1TRGoxeWJtMWwrc01FU3MraWNDNlRRYnp2TTRORnBia1NjNWpNazFGVXZBcVRnRkd1Q00wdU9NL0ZCTjVUQkdtM2d5K01Hc3VUZktkUGIzdGNXMWF4WW5xc2tlN2MyeDh5NllkRnhPT1J1bnZ6bHhNam5ITHEwOExFYlZDMXFTeGloSHNEOWVBS3ltY1ovZEh5OXIvS3h1TzR2eENld2dPUFFQRjhLanptS0tEWUciLCJtYWMiOiJiYjYxOGM1ODNkMmZjNzY1MjMxZjkxNDZlMDQ2MjZiOTYwYTE3OWQ2YWY3ZDdhOGRjNmIxMjVmZTNkYmUyM2FiIiwidGFnIjoiIn0%3D; expires=Tue, 17 Jun 2025 15:30:33 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Ik01eFhlMWlISFplR01Xamwrb0p4WEE9PSIsInZhbHVlIjoiditzZG4ydEE4alNxVVFUS2pDQVE3TFRZdWsxMEhoeW5CUzZ5M2hYTy8xUzE2RnVWSUZLTDJNSzkxbHBjL0dDL3FFUEZkT1FCVnlnVTNPQi9uZ0FFZlpUZ0NoSlhXaEIvdDJXTG4vL1pSZUZNM0NIa1U4QnNGc1NpMExBMURHWjd6aFNkQUkyS0pqK2RlZ0V3UWFmTjllbHdUdTh2WmdtSjBQcGUwMEVRcW9sWE93a05oNFhVZVRaekZnaWNLMGFEM2NOeUFmVFRHRzRFMFF6Y0RTWnpDcW9tODdlWW9pTkRjbHNIK1hJUCtoYjUyZEhQVlpaWExvcnFVYVA3dlBGcXIrRXlCdnprbjZwSzFBRjBqUnBlQjRwenVtVTUyMmY5dkhQaE1tTm5hWFF4TVZpaHBPYWlUUzlZSTNoaWcwRGloYXNlb3B4WEM2ZVNnbEF5aloyS0VBSW00R3BVc2tSNTdRLzU1VTF2V2MxNVkzWC9tZk9mNFVObnJ5a2RPbUpJQmh3UWI3YTMzcFJpU3hZb2xXalFHYzdyYStxWVVrTmRvV0k1NGd0T001ZWFUenQ5YzRMekR1cWM5SkZBbllEU0s0QkVIa0E5MEZwT2VVOUhGcEM5dHVTVWRYdFMzYWRYOVVmSW1STE0rU2VXdjRaV21tZVY1dThiSmdscmFyZzQiLCJtYWMiOiI5NWVlMDA0OTFiYzcyZGQ0MDhiODIxN2VhZTI4YzE4NDJlZDBlZWEyYTQ5NjAwNmI2YzJiNDRhZGE5NWZjMGRmIiwidGFnIjoiIn0%3D; expires=Tue, 17-Jun-2025 15:30:33 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6IjlkSUNkYnVsbkZkTEM5WCs1UFdqMGc9PSIsInZhbHVlIjoiajNBR29adzRKQXREcVJFREZFTWhaMHJLek03d0MzVi8wMGxlWmllRi9uTElKa2VzQlRwbjlLR1RQSzVoWTYrRkE4N3U0UTZqU2lGK0JIQ3RFeEI3dGM4WEg1WTcyL2ZVYzQyZXVoTzRlcmhjS2RaUjZxUFhIcU9QTVZVcHlOdWZqbW14NHNiS0ZkcVNOeVZNUzlLWDY5UmlEMnRTamx0cS9LaEpXNldyL0dweDJoQlA5d0c2azU1WDlQRXZvZnVKSlZRUTFIZTBaYmFYU0xaM1hUUkIwV3NrRmwvc2h6cVVlUGowUnJJUW1VVVN3T1M3KzhFT0o0QWhUSUxBVnlVSWtzSVIxL1hxaGhNNjU4MFZmUTgvOXV2QUVVU3JYUkRLSmRXS1NnUEJ1bjl6VFp6VDQyV2dDeFZPSEh6Y21aVTNnclFDWGxKN0xGbk5seGtnRE1TRGoxeWJtMWwrc01FU3MraWNDNlRRYnp2TTRORnBia1NjNWpNazFGVXZBcVRnRkd1Q00wdU9NL0ZCTjVUQkdtM2d5K01Hc3VUZktkUGIzdGNXMWF4WW5xc2tlN2MyeDh5NllkRnhPT1J1bnZ6bHhNam5ITHEwOExFYlZDMXFTeGloSHNEOWVBS3ltY1ovZEh5OXIvS3h1TzR2eENld2dPUFFQRjhLanptS0tEWUciLCJtYWMiOiJiYjYxOGM1ODNkMmZjNzY1MjMxZjkxNDZlMDQ2MjZiOTYwYTE3OWQ2YWY3ZDdhOGRjNmIxMjVmZTNkYmUyM2FiIiwidGFnIjoiIn0%3D; expires=Tue, 17-Jun-2025 15:30:33 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1194158472\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">ixL6BN3l9tyyUscqj0hv99yKIy0daqhD43fAAWcL</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>16</span>\n  \"<span class=sf-dump-key>pos</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-key>2043</span> => <span class=sf-dump-note>array:9</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"10 characters\">zello sour</span>\"\n      \"<span class=sf-dump-key>quantity</span>\" => <span class=sf-dump-num>1</span>\n      \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"4 characters\">7.00</span>\"\n      \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"4 characters\">2043</span>\"\n      \"<span class=sf-dump-key>tax</span>\" => <span class=sf-dump-num>0</span>\n      \"<span class=sf-dump-key>subtotal</span>\" => <span class=sf-dump-num>7.0</span>\n      \"<span class=sf-dump-key>originalquantity</span>\" => <span class=sf-dump-num>19</span>\n      \"<span class=sf-dump-key>product_tax</span>\" => \"<span class=sf-dump-str>-</span>\"\n      \"<span class=sf-dump-key>product_tax_id</span>\" => <span class=sf-dump-num>0</span>\n    </samp>]\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n"}}