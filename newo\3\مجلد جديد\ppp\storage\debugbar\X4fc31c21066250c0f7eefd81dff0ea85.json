{"__meta": {"id": "X4fc31c21066250c0f7eefd81dff0ea85", "datetime": "2025-06-17 13:29:23", "utime": **********.313703, "method": "POST", "uri": "/warehouse-empty-cart", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1750166962.577643, "end": **********.313728, "duration": 0.7360851764678955, "duration_str": "736ms", "measures": [{"label": "Booting", "start": 1750166962.577643, "relative_start": 0, "end": **********.213069, "relative_end": **********.213069, "duration": 0.6354260444641113, "duration_str": "635ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.213082, "relative_start": 0.6354391574859619, "end": **********.31373, "relative_end": 1.9073486328125e-06, "duration": 0.1006479263305664, "duration_str": "101ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 46326408, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST warehouse-empty-cart", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\ProductServiceController@warehouseemptyCart", "namespace": null, "prefix": "", "where": [], "as": "warehouse-empty-cart", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FHttp%2FControllers%2FProductServiceController.php&line=1663\" onclick=\"\">app/Http/Controllers/ProductServiceController.php:1663-1673</a>"}, "queries": {"nb_statements": 2, "nb_failed_statements": 0, "accumulated_duration": 0.020450000000000003, "accumulated_duration_str": "20.45ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 16 limit 1", "type": "query", "params": [], "bindings": ["16"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.266357, "duration": 0.018850000000000002, "duration_str": "18.85ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 92.176}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.300211, "duration": 0.0016, "duration_str": "1.6ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 92.176, "width_percent": 7.824}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "ixL6BN3l9tyyUscqj0hv99yKIy0daqhD43fAAWcL", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "16"}, "request": {"path_info": "/warehouse-empty-cart", "status_code": "<pre class=sf-dump id=sf-dump-354239507 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-354239507\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-209280065 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-209280065\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1004183671 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>session_key</span>\" => \"<span class=sf-dump-str title=\"3 characters\">pos</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1004183671\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1194112449 data-indent-pad=\"  \"><span class=sf-dump-note>array:19</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">15</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">ixL6BN3l9tyyUscqj0hv99yKIy0daqhD43fAAWcL</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1937 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=1pubyvl%7C2%7Cfwu%7C0%7C1993; _clsk=25xk9k%7C1750165707106%7C8%7C1%7Cl.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IkRSdWZDMHN0b09LSTRjUUwyK1hYK2c9PSIsInZhbHVlIjoiMWFsY1pucWtqUEN5cHU2cFkxMG5rcjhWNGwvaGlQdkN3RThqWVJVK2Y5Mm9ZR3dZUHNRdzc3SjFKck4vYmk2THVpdVFVZ2NyY2ZNNGJmWk1Qd2lRSEpJK2VpMlltbk5ydUxSMTF4bkNvU0tJbGN4TWRPUHNzWkF1aVZHelRCOTJBOU1EQ3FNc05XZm5OWk5qRUZDSjl4K3QwdEUvc2FTcFdkOTREM01lWDkwY2o3QmhLYTlZYjVtZE05QWdIUFlwYUY2VE9zakxhWEIwWW9GWXBnSjl0czJaaG9IVzhxRnNPa3BhWE5va0twcTc0bkpla0lCbzRUZHhURGNJQmJMdEs4emF0aWhnVHlXVklYeVUvTVRzTXcxZFRpM2pYeURxZUtNS0hka01XREM3NVdyNm9lY0F3bENYY1VaZVRaaUxJUDNLbWRIbkZZdjM4U0pXTGZneWY3VDdhYkNveVIrcXlrZ0tWM3lVa2gvM2FEZm1PRm1ndXRGbVhjYTRVb0Voa0F0Y1N6bUxZL0ZrWDdsKzFjYTlTb1RnL1lSZlNnczJrVGtxNGRVM3Q1VlRyS3prTVBmK3NKWEZ1cnZFbTFiSnhsYk5CQ29yL1A1UWFpdUwvWXFTOXJDT1FKdmtSeXpsN3BKWmZSWU8vZFphQjhIditlY3VUU3NwV2NYRDFwaDYiLCJtYWMiOiIzOWQxMDAxZDU4ZjMzMGJmMDdjNzk2MDIxZjcxN2MwY2VkYzU3MDQ2MDUyZjhmZGY3MDViMjIxNGU3MzhlMmI3IiwidGFnIjoiIn0%3D; test_session=eyJpdiI6IjdvOThqQ21uSWxXWjZKaVhYNXZ6aFE9PSIsInZhbHVlIjoia09PVDNnZm9ETzE3MGpzZjBqcHRJUDZNUUlpeWY3OGFOSWwvK0VMcUsxaFRtTjRNbHVaME1RdkR5U2RLd3Z0UTd1ZlBvNFdNdVlXNHlORTMva21vUG10ZUl0Q1pDNU5jaWcremV5a3ZDdk12MnlEajBYWjFLekt3NytIRytXRkc3djNzTkNqQjM3VmVWcWFKMDAxOEhYZlhFZkNJTjRDTEJnbU1YMXU4bEpoY29XVE0vS1NoZXgzSHZidmJ1RXNZK2FZM2hveFV2d1U2YkNzaGdJZ0kwYi9OcnVmUlViZitiTVAwOFV3ZnZOc05YaEhXVzFVRnhRZ0hnenpNNHZGSWwxK0RaaVd2eTY0NkNNM2tvU0FpZG9od005VXVONVF4bjhiS2g0dTNjNVVmU3VYQUlMMUMvSFNaNDNZZTRRdUJnNkJWV2lpMHM1QzVMbFQ5Y3MzS1I3YlZtb2RnNnd6Y2UvNkx2eVcrUnZVdmVUR3dMNzd3SDJDOTEvaUk1bzlJVThLcDJLZHBZY1RMa0d0bXNTdW9kZGVwbGJXWVRMM2NLbnN1cmFWcFUzSW51cFZSc1VpM0cvdmJiUjgrQ2hmOGpZaWNHRzNMOXRpeENpZzJWUHk3WkRGdXY1dEg3Q3dnTWdkL0JPV1ltY28vZW9oSWRNSm9TRG9oYUF5c1lQL00iLCJtYWMiOiIwYTZhZmM3YzAyYjRhNDNmNDY0YmY5Yzk2ZTNhYjA5MzQyNmRiOGEzOTZmMDc2Njk2ZGEzOGVkZjQ0NTA1N2UxIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1194112449\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-2115769024 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">ixL6BN3l9tyyUscqj0hv99yKIy0daqhD43fAAWcL</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">coJ97Z8YyNCeFgF4h0jUL1PrpYBjauguIcJbJbm8</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2115769024\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1334248617 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 17 Jun 2025 13:29:23 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6InhUVjlCcDJ0QU9iNXYzUUVvZmdqelE9PSIsInZhbHVlIjoidUVWMlYyWXVlMjdnM0twL3NHZFB3cFZZK1ZyZGxtTGFDbk9iaGNjVlo5a1JrQVZ4RHZYOVBvN0lWNHhSVU9jaFNOUHorbjczd1AySC8rY3hHRXRUdXMzeVF6dVd6dklWWjExQzhobC9rMGdIZHFUV0IrdGhSQ3Q3SlB4Rm9YLzJSRTNnc1pPZzllVkhybDMzTEw3ZGdFc0dLUmhHUTU1R2ZWNmNSSmhpaXhyQnFieVRJa2ZGZFAxNHhnSVVqaFVCT0JZNVJZTTlrM0l0ZDFOOEtiSG4zZG9KckprN21WaUc0TVBQWHFCeTF5WHRGLzJESEU4UlNrN0FhUEFWNTJmMzlla3FIRloyMGl3Ujd4YlBZMjlqc2ozZDZjeW1zc1lacHF3cWtIZy90ZElWTkV1d0dkNENVWWtSdkdsd1RZZUVYbXVGVG00SkZUTmFoRTIwNnNvTHBZK1czSjRoOHBPV21OUzY4b1BSdVpvNFJ6TEpha0FDTzlTTHcyby95ekZyTkViNmI1dVRoa2hXRVBIbnNTSDdCZTNlZDFSSlkzSk9ieVpzOENrNXlCZTFqYzRUUmxrak1ZdWQyWU9PMmtNcWNXY0ppNFlxbHAwZCtFTHpPOGpZaS9MUWJXa0FOWjVLVFQ0cnJDbTd0QS9iWGxSN0JJWnJlSTRDRmlOMkRHbTgiLCJtYWMiOiJmOGRlYzNkMGMxMWYzMWY4NjZmZTVmYTc2NzEzNTUwZWMwMjAzNjU3Y2Y5MGE4ZWZkNDkyNTYxNTQyMTY4YWRjIiwidGFnIjoiIn0%3D; expires=Tue, 17 Jun 2025 15:29:23 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6IjVGQmllenM3QTZhd29ZSkVSb0Y0L1E9PSIsInZhbHVlIjoiM3ROS0ZEN01xY1FNWm95TEZZeVJxOC9Nc1VXYVhWWitEQmxvQnFJWkJnMmR6dkk1MklMSnZycllQeWJQbVhzdGNWcDdkUHR0NVVrWGgrMzg1M3Z0STJaRjBQTFZ1V2xzWElzWVdETENJaG1ieWxYVjczRXRnWnVrSTNmYWZ2N3RVeUZ4WVJ3WVVEZEtCSUE0MEFRYkNjdHBHajMrNHE5ZGpCOXEydnYxVWJzcHRYQ2RJUDdSSnJIblJ2QUxub3FHRkcxOUlqdVo1NUFnYW1KTDk4bVhIOW9lRE5NNGZ3Z2xuc1JwVmg3VGUzdzcxMTM3UFYxcGlhRzAxUTcrMTV1eEN2Uk9maFpDdi9Xakx1VUVjai9wZnJRN2EwL0R0ZEVMMkFjUkxJZ0dBRnNNNlAwdzFkODY5VmlVK1cvWGZvcGpjdG1JT2MwR2R2WURHcXNYY1VidXhScmtFSWtBUW5seklWNmhLaWN4Y3hhRHhmMUtGSVNtRitnYTRtSC9qVWs3Uzd6RlpPcVMvemhSUjVIRStEL2h6ZE9SdGRMbDdYVWpIOTZsZkp4U0hNVWNCbFRwZ3VpK0czYVhKaDNOcVp3bVVHWjdZK2Y1am1YYVg4b0pzU1NwMjIzUWhsVnVYZ3ZRM0pxVFhtaXQzejVnZ1FRaXZ4djhaaFYyMzExQmF4c0EiLCJtYWMiOiI1NDRjODQwMzM2ZDU0YjQ3YzNjZTg0ZDk2MzNjNTI4ZmQ3ZGIyZDYzZjUzNDljNmIxMDVlOTBiNjEzYTZiZjFlIiwidGFnIjoiIn0%3D; expires=Tue, 17 Jun 2025 15:29:23 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6InhUVjlCcDJ0QU9iNXYzUUVvZmdqelE9PSIsInZhbHVlIjoidUVWMlYyWXVlMjdnM0twL3NHZFB3cFZZK1ZyZGxtTGFDbk9iaGNjVlo5a1JrQVZ4RHZYOVBvN0lWNHhSVU9jaFNOUHorbjczd1AySC8rY3hHRXRUdXMzeVF6dVd6dklWWjExQzhobC9rMGdIZHFUV0IrdGhSQ3Q3SlB4Rm9YLzJSRTNnc1pPZzllVkhybDMzTEw3ZGdFc0dLUmhHUTU1R2ZWNmNSSmhpaXhyQnFieVRJa2ZGZFAxNHhnSVVqaFVCT0JZNVJZTTlrM0l0ZDFOOEtiSG4zZG9KckprN21WaUc0TVBQWHFCeTF5WHRGLzJESEU4UlNrN0FhUEFWNTJmMzlla3FIRloyMGl3Ujd4YlBZMjlqc2ozZDZjeW1zc1lacHF3cWtIZy90ZElWTkV1d0dkNENVWWtSdkdsd1RZZUVYbXVGVG00SkZUTmFoRTIwNnNvTHBZK1czSjRoOHBPV21OUzY4b1BSdVpvNFJ6TEpha0FDTzlTTHcyby95ekZyTkViNmI1dVRoa2hXRVBIbnNTSDdCZTNlZDFSSlkzSk9ieVpzOENrNXlCZTFqYzRUUmxrak1ZdWQyWU9PMmtNcWNXY0ppNFlxbHAwZCtFTHpPOGpZaS9MUWJXa0FOWjVLVFQ0cnJDbTd0QS9iWGxSN0JJWnJlSTRDRmlOMkRHbTgiLCJtYWMiOiJmOGRlYzNkMGMxMWYzMWY4NjZmZTVmYTc2NzEzNTUwZWMwMjAzNjU3Y2Y5MGE4ZWZkNDkyNTYxNTQyMTY4YWRjIiwidGFnIjoiIn0%3D; expires=Tue, 17-Jun-2025 15:29:23 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6IjVGQmllenM3QTZhd29ZSkVSb0Y0L1E9PSIsInZhbHVlIjoiM3ROS0ZEN01xY1FNWm95TEZZeVJxOC9Nc1VXYVhWWitEQmxvQnFJWkJnMmR6dkk1MklMSnZycllQeWJQbVhzdGNWcDdkUHR0NVVrWGgrMzg1M3Z0STJaRjBQTFZ1V2xzWElzWVdETENJaG1ieWxYVjczRXRnWnVrSTNmYWZ2N3RVeUZ4WVJ3WVVEZEtCSUE0MEFRYkNjdHBHajMrNHE5ZGpCOXEydnYxVWJzcHRYQ2RJUDdSSnJIblJ2QUxub3FHRkcxOUlqdVo1NUFnYW1KTDk4bVhIOW9lRE5NNGZ3Z2xuc1JwVmg3VGUzdzcxMTM3UFYxcGlhRzAxUTcrMTV1eEN2Uk9maFpDdi9Xakx1VUVjai9wZnJRN2EwL0R0ZEVMMkFjUkxJZ0dBRnNNNlAwdzFkODY5VmlVK1cvWGZvcGpjdG1JT2MwR2R2WURHcXNYY1VidXhScmtFSWtBUW5seklWNmhLaWN4Y3hhRHhmMUtGSVNtRitnYTRtSC9qVWs3Uzd6RlpPcVMvemhSUjVIRStEL2h6ZE9SdGRMbDdYVWpIOTZsZkp4U0hNVWNCbFRwZ3VpK0czYVhKaDNOcVp3bVVHWjdZK2Y1am1YYVg4b0pzU1NwMjIzUWhsVnVYZ3ZRM0pxVFhtaXQzejVnZ1FRaXZ4djhaaFYyMzExQmF4c0EiLCJtYWMiOiI1NDRjODQwMzM2ZDU0YjQ3YzNjZTg0ZDk2MzNjNTI4ZmQ3ZGIyZDYzZjUzNDljNmIxMDVlOTBiNjEzYTZiZjFlIiwidGFnIjoiIn0%3D; expires=Tue, 17-Jun-2025 15:29:23 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1334248617\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1738757046 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">ixL6BN3l9tyyUscqj0hv99yKIy0daqhD43fAAWcL</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>16</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1738757046\", {\"maxDepth\":0})</script>\n"}}