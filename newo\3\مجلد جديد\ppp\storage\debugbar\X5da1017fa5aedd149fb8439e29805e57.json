{"__meta": {"id": "X5da1017fa5aedd149fb8439e29805e57", "datetime": "2025-06-17 13:33:41", "utime": **********.945272, "method": "POST", "uri": "/warehouse-empty-cart", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.192841, "end": **********.945299, "duration": 0.752457857131958, "duration_str": "752ms", "measures": [{"label": "Booting", "start": **********.192841, "relative_start": 0, "end": **********.846489, "relative_end": **********.846489, "duration": 0.6536478996276855, "duration_str": "654ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.846507, "relative_start": 0.6536660194396973, "end": **********.945302, "relative_end": 3.0994415283203125e-06, "duration": 0.09879493713378906, "duration_str": "98.79ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 46326408, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST warehouse-empty-cart", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\ProductServiceController@warehouseemptyCart", "namespace": null, "prefix": "", "where": [], "as": "warehouse-empty-cart", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FHttp%2FControllers%2FProductServiceController.php&line=1663\" onclick=\"\">app/Http/Controllers/ProductServiceController.php:1663-1673</a>"}, "queries": {"nb_statements": 2, "nb_failed_statements": 0, "accumulated_duration": 0.017099999999999997, "accumulated_duration_str": "17.1ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 16 limit 1", "type": "query", "params": [], "bindings": ["16"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.893847, "duration": 0.016329999999999997, "duration_str": "16.33ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 95.497}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.925938, "duration": 0.0007700000000000001, "duration_str": "770μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 95.497, "width_percent": 4.503}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "16", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]"}, "request": {"path_info": "/warehouse-empty-cart", "status_code": "<pre class=sf-dump id=sf-dump-1466741422 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1466741422\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-34891274 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-34891274\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-141344647 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>session_key</span>\" => \"<span class=sf-dump-str title=\"3 characters\">pos</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-141344647\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-297457726 data-indent-pad=\"  \"><span class=sf-dump-note>array:19</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">15</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1936 characters\">_clck=2gyfy%7C2%7Cfwu%7C0%7C1994; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clsk=1ti4td2%7C1750167218299%7C2%7C1%7Cl.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IjM0YWw0OW5ybGgvbUJUNERCd2FhekE9PSIsInZhbHVlIjoidHA0LzRJWk5FakpJZEh5VG9EdTZJSFBBK0JZRm9BNytRQ25Ba1dJUjdkQTRNb3B1V2ZiQ01ZTXJKYUdMc0xtZ2dMdUNYTlB1ODNwYnd1bFQ1WXZzNDRGVzBkMUFQRGg4K1ZYRytjb2hzRVN4RTltbHFlZ3Buajl2akZkWGpxVEQwQndiNXhkQjlOODZsS1I3UThiSytVRTh4TUg3WDVWWEpIL2lHQmpjaW1vYThrRTZPdXZCMjVMbVk2RDZqZS9YWmJKSWFxN2JUR0JjQk1Cd2lwVzJQczlIM1I3SEp2RG9zREZMb2JFaytVVW1VNWQ3ZTY4WThabE5LVHpoVkNBWVNOSVFvT1dQeFlucWhybDQweHJmOEg3N1Yxbkw5ZkR4dXNKaUpLV1UzLzFxaVVuZUZLV0lpa09ZVzNBcXhmQjZ3ejNSS3RRWmt1TDlXaUkzSmNoekVLZTBxZ3pndkVhdEY2OG1KNllYak53QndIUHRmMXpPR0l4bnkwcGdFVkkrTDFOWWJNdWtoWjIySWEwMHk4T0wwbkpqWHBBekdha0hnc2VFcDNkaXR3RnhUVGcyZmtTWXhiNW8rMlBmRjlwcndhL2pDVktXZ3ZkM0lIbFJaZ1h1L3RNbHVwdFJtVDFPRFQ0MXlpN1hBVUF4MEZkU3ZQZUZVbnpFZU01WkR2UFYiLCJtYWMiOiIwOTQ3Y2RmNzMxMzI5ZmFlNTFjMDk0MTdhZjQ5ZGRmNTEwZTUxOTczMmFiNDhmNjg4MWQ0NzBlYTRmOTM4NGFiIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6ImxSUllWT0R6L2VXRWoybmJmb1R1WkE9PSIsInZhbHVlIjoibmMvNjBVbVJnazE4TW1XOXNOaUNLYlZxcUdiNGtXcnNhMmpPVDhpMEpGM3VTMDJrZ3pzYU5MU2JWajFMbHlXMXBrNUV0M2VyY0htMWJ2eXVYMGRIRkVoeDFtb085VkNqQjJXYjloaGI5T2xTaks5alRhQk90OG5YcVRRL1BXMW1ianlqbDBMYit2REMzVWtESnAvWkNNb2pCdlA4UFE3cjJNajNmTEZXUllib3NCUHlyZGVTdHZSUk84SkovNnFSSjZuN3dkOEY4cVRHd3VhTjhPVCsrbTMva0ZndkI1b1ZnNythZkIrUklqbnR6K0ROUWlmcVU0MmxMQWFVNWJiMjA3bFRtSTJBaHhpQXNhSitPWjlDdWo5QWlQSlJGeHhtSEVpMWVoa0lWN2poV2ZsQ0NlUUlCRWtuSytYQkFhTEpNZlcvSnRkcDdUOVBSUk9VTzlVWTNjbDJ6TDRHay8vRC9RVGs1ekdRS3BiSWoxdUVSNjNyZWhCRENOR2NhdUFnVDZrdlpDL3dCTVV1N0ZDcGtYUDVqak1XSHJwKzg2OHFXQlNmZVJ1ZWlvWDA3NmFGUUhIK0QxN2VRM0xqanNGZ2tJOUZYTEkrQWdtSUNWTjRtM2V1MlpHbzAzS0xpTHpZbHBQUnpIOUlsUGpxbWJuS3VjVG9XSC9oSUZ3c0J2cU0iLCJtYWMiOiI0YjM2NTEzNDYyNzUyNTgyYTU0NGFjNzg3MTkwNTgyNmUwZjg1OWE3ODdhZDdiYTA1NzFmOTgxNzgxNmVkMDBlIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-297457726\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-97048089 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">VloaUQCBjpDKrel7nHeAzB1sTLdTMwteSos1MmAN</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-97048089\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-2141421627 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 17 Jun 2025 13:33:41 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6ImQ3ZjVuT3c2cVJ4c1FoWFJHd3lqVEE9PSIsInZhbHVlIjoiNDZBSXczb2p2UUxlS28vRldRdm83d0NYT2J6aTdUWFZkYkhsNnpzWEZIeHlDTENZdERvL2d6S2l0NU5NZHRYRGFVamU2MnhpWTlZVDFaWW1KYXYrdjRaMVNYbkFIQmwzNkRyQ21nOEh5N2g5cjFZWmFEQjc2akRuSGxNMVpHRzM0RFhJWEhCQUVhODhNRnh6ZG9GbElTV05Jb1oxMGlSd3pORHVEeFQ0OHZtcWhiT3c5b2E4eS9HMnNPREtFVmgrZHpzU3ZJWnFWeCtaaVRFRmNVRFJKOXVpSU9JOFU0K0pCbnFDWEpBZUZwQkQ3aDFpN0hGZlNhc3VSUEJRMDg2WlRLRVBvMEVsSWpXQnZOMmk3MWJBODRlQ0hxZjBQSzhYQ004SmhCRnhZZGx0TDByVUxwSU5JdUdjSWpTTWovdW1XUkFyeElhSjFSL3V4cG9JN3ZxVHNzV1JRWGxiZVFiR2hhOW5xMGdvVTdwaDQxQ24rdnVVZ041WnhtTW9xTWpMQ0cwQ3Y5SVVKemx6UERncThMS0tHcmFoVVo2Qm5hdXNqYWdCTWUyeXU0akNVT2cxWWl5WFZaSVJ4WjNVbXRUMlI0R21SQlhBNytmMW5NQjJpZVF6RzlmaXVwOWc0UCtab0RZVlBnWlQ0ZlFIRDd5Q204YVVDK2Ryc2JQUnhHcE4iLCJtYWMiOiI4NThjZmIzYzA0ZWU2NmI2ZjI2ZmFjNmViNDFkYjUxZmVkMjE4YmU0NWJiMTU0OWZlMTU5NmYxNDFiZmNmZGUwIiwidGFnIjoiIn0%3D; expires=Tue, 17 Jun 2025 15:33:41 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6InJTNGF6Smp2aVJqY0w5ZllTeXVna2c9PSIsInZhbHVlIjoiZmVMRXB4Rm9VU1ByK3dhRFdUZ0F0Sks0bUhjcGphTW96T2d5S2Rmczh0NGFORkVqZW5WVlMwSU5GOUxZSzU0ZDNuaDlEU2NtRUNVa1Uzb0ZBMnVJYmEyb2FvTmh1cVYyK2x6R2g4ODlWOHlKWWJkOWhmVkJab0tmSXFNUXJiL1F5Sk5qK0hBQmdXYWdQREdwenplTDhhY0sraXhmelVrdlRqV0hid2p4MEJ1TStsM2JvUWNsNGloZlFGOFZzeHBtTE1HNnNMOEJ4enY4UkFLeUNONnRaUHpkc2Nxajh3emsvdHNiYXR0dG5zR0NCcTlPSlFBV3JYNlRTZHBXRGJJTjhnaDRhb2xaTHRLaWxFdG14SE1KNnpaMlhVczdsbThwYTVsOEVMU1ExWEdIUThnQjcxY0pnWTM0eStVMkdoaFlDN3F5MmZ2QTl5VkNEdzhOM0pkRGZxTkpaQkh0a295dlBKSURVS2x2MWNJelYwbG4wbkZWdGxRaG5hczI2amtrbHF0SjNLV1JGZlkyUlg4R3kzVnpGK3dWZXJ5aFNMVlhycUgySmFsQld4UnMzMkpuYlczZGhsUFVncnZsWWFLMzAwWGxPMTZLcXIwUTFUaDBtZjh1NmxGVm8vVFFwWEtNUTVpWDZwcUZ3ZjZUdE1KQkpXUFpNdkp6VVBBeFVFZ2ciLCJtYWMiOiI3NzFlYzIwMjY5M2QxY2IwZjc2M2NmNTI4NWU4NGY2ZTE1YTVmMjRjMjc4MmE3YTE2Nzc5ZTMyNGY5ZTRhMTdhIiwidGFnIjoiIn0%3D; expires=Tue, 17 Jun 2025 15:33:41 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6ImQ3ZjVuT3c2cVJ4c1FoWFJHd3lqVEE9PSIsInZhbHVlIjoiNDZBSXczb2p2UUxlS28vRldRdm83d0NYT2J6aTdUWFZkYkhsNnpzWEZIeHlDTENZdERvL2d6S2l0NU5NZHRYRGFVamU2MnhpWTlZVDFaWW1KYXYrdjRaMVNYbkFIQmwzNkRyQ21nOEh5N2g5cjFZWmFEQjc2akRuSGxNMVpHRzM0RFhJWEhCQUVhODhNRnh6ZG9GbElTV05Jb1oxMGlSd3pORHVEeFQ0OHZtcWhiT3c5b2E4eS9HMnNPREtFVmgrZHpzU3ZJWnFWeCtaaVRFRmNVRFJKOXVpSU9JOFU0K0pCbnFDWEpBZUZwQkQ3aDFpN0hGZlNhc3VSUEJRMDg2WlRLRVBvMEVsSWpXQnZOMmk3MWJBODRlQ0hxZjBQSzhYQ004SmhCRnhZZGx0TDByVUxwSU5JdUdjSWpTTWovdW1XUkFyeElhSjFSL3V4cG9JN3ZxVHNzV1JRWGxiZVFiR2hhOW5xMGdvVTdwaDQxQ24rdnVVZ041WnhtTW9xTWpMQ0cwQ3Y5SVVKemx6UERncThMS0tHcmFoVVo2Qm5hdXNqYWdCTWUyeXU0akNVT2cxWWl5WFZaSVJ4WjNVbXRUMlI0R21SQlhBNytmMW5NQjJpZVF6RzlmaXVwOWc0UCtab0RZVlBnWlQ0ZlFIRDd5Q204YVVDK2Ryc2JQUnhHcE4iLCJtYWMiOiI4NThjZmIzYzA0ZWU2NmI2ZjI2ZmFjNmViNDFkYjUxZmVkMjE4YmU0NWJiMTU0OWZlMTU5NmYxNDFiZmNmZGUwIiwidGFnIjoiIn0%3D; expires=Tue, 17-Jun-2025 15:33:41 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6InJTNGF6Smp2aVJqY0w5ZllTeXVna2c9PSIsInZhbHVlIjoiZmVMRXB4Rm9VU1ByK3dhRFdUZ0F0Sks0bUhjcGphTW96T2d5S2Rmczh0NGFORkVqZW5WVlMwSU5GOUxZSzU0ZDNuaDlEU2NtRUNVa1Uzb0ZBMnVJYmEyb2FvTmh1cVYyK2x6R2g4ODlWOHlKWWJkOWhmVkJab0tmSXFNUXJiL1F5Sk5qK0hBQmdXYWdQREdwenplTDhhY0sraXhmelVrdlRqV0hid2p4MEJ1TStsM2JvUWNsNGloZlFGOFZzeHBtTE1HNnNMOEJ4enY4UkFLeUNONnRaUHpkc2Nxajh3emsvdHNiYXR0dG5zR0NCcTlPSlFBV3JYNlRTZHBXRGJJTjhnaDRhb2xaTHRLaWxFdG14SE1KNnpaMlhVczdsbThwYTVsOEVMU1ExWEdIUThnQjcxY0pnWTM0eStVMkdoaFlDN3F5MmZ2QTl5VkNEdzhOM0pkRGZxTkpaQkh0a295dlBKSURVS2x2MWNJelYwbG4wbkZWdGxRaG5hczI2amtrbHF0SjNLV1JGZlkyUlg4R3kzVnpGK3dWZXJ5aFNMVlhycUgySmFsQld4UnMzMkpuYlczZGhsUFVncnZsWWFLMzAwWGxPMTZLcXIwUTFUaDBtZjh1NmxGVm8vVFFwWEtNUTVpWDZwcUZ3ZjZUdE1KQkpXUFpNdkp6VVBBeFVFZ2ciLCJtYWMiOiI3NzFlYzIwMjY5M2QxY2IwZjc2M2NmNTI4NWU4NGY2ZTE1YTVmMjRjMjc4MmE3YTE2Nzc5ZTMyNGY5ZTRhMTdhIiwidGFnIjoiIn0%3D; expires=Tue, 17-Jun-2025 15:33:41 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2141421627\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-685962878 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>16</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-685962878\", {\"maxDepth\":0})</script>\n"}}