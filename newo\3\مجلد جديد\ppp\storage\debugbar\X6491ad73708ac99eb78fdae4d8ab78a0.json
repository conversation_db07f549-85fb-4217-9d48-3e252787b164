{"__meta": {"id": "X6491ad73708ac99eb78fdae4d8ab78a0", "datetime": "2025-06-17 13:48:36", "utime": **********.926905, "method": "POST", "uri": "/empty-cart", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.350921, "end": **********.926926, "duration": 0.5760049819946289, "duration_str": "576ms", "measures": [{"label": "Booting", "start": **********.350921, "relative_start": 0, "end": **********.827366, "relative_end": **********.827366, "duration": 0.47644519805908203, "duration_str": "476ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.827378, "relative_start": 0.4764571189880371, "end": **********.926928, "relative_end": 2.1457672119140625e-06, "duration": 0.09955000877380371, "duration_str": "99.55ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 48583504, "peak_usage_str": "46MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST empty-cart", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\ProductServiceController@emptyCart", "namespace": null, "prefix": "", "where": [], "as": "empty-cart", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FHttp%2FControllers%2FProductServiceController.php&line=1647\" onclick=\"\">app/Http/Controllers/ProductServiceController.php:1647-1661</a>"}, "queries": {"nb_statements": 4, "nb_failed_statements": 0, "accumulated_duration": 0.012379999999999999, "accumulated_duration_str": "12.38ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 16 limit 1", "type": "query", "params": [], "bindings": ["16"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.8655648, "duration": 0.009179999999999999, "duration_str": "9.18ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 74.152}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.8863232, "duration": 0.00133, "duration_str": "1.33ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 74.152, "width_percent": 10.743}, {"sql": "select `permissions`.*, `model_has_permissions`.`model_id` as `pivot_model_id`, `model_has_permissions`.`permission_id` as `pivot_permission_id`, `model_has_permissions`.`model_type` as `pivot_model_type` from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `model_has_permissions`.`model_id` = 16 and `model_has_permissions`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["16", "App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 326}, {"index": 21, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 566}], "start": **********.9084852, "duration": 0.0009699999999999999, "duration_str": "970μs", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:326", "source": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php:326", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=326", "ajax": false, "filename": "HasPermissions.php", "line": "326"}, "connection": "kdmkjkqknb", "start_percent": 84.895, "width_percent": 7.835}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (16) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 312}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 26, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}], "start": **********.9123359, "duration": 0.0009, "duration_str": "900μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 92.73, "width_percent": 7.27}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 1, "messages": [{"message": "[\n  ability => manage product & service,\n  result => true,\n  user => 16,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1949707029 data-indent-pad=\"  \"><span class=sf-dump-note>manage product & service</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"24 characters\">manage product &amp; service</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>16</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1949707029\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.918473, "xdebug_link": null}]}, "session": {"_token": "PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK", "_flash": "array:2 [\n  \"old\" => array:1 [\n    0 => \"error\"\n  ]\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "16", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]", "error": "العربة فارغة!", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/empty-cart", "status_code": "<pre class=sf-dump id=sf-dump-693209596 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-693209596\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-2097925261 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-2097925261\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1987513574 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>session_key</span>\" => \"<span class=sf-dump-str title=\"3 characters\">pos</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1987513574\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:19</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">15</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1936 characters\">_clck=2gyfy%7C2%7Cfwu%7C0%7C1994; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clsk=1ti4td2%7C1750167218299%7C2%7C1%7Cl.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6InJKNWwvcVUwZWlFRVdoMVVESldUQnc9PSIsInZhbHVlIjoib0piSDdBZ3grQ29laER4Rks0eURMYjZEbHBObXQyUHZrUk42dTlXc0h0OE8ybHJZNFZuRkhZUWRiU1NDb0JUK0RrbDRKVXY5dW1WTGhmU0xjU1dlT1VHZHB2VzhWZ0NraDRJYS9mSXJaSDQzcE1SVDh4eEplTlVmN3MwTndhVTBhK2lVektnNjJaS2MzVy9lUkMyMWJFMzNZbzhpMEFmbXo3clR4RXBmMHo0dUFMSWFuWWdiT213OWRmbzhoY0NqTStQYnhuUVA4WFRZOFZhRExFcHd2YWFSWjBubEpFMERpYjA4ejJvbmNCT2VNZ1c5UUJOMFlyWC9QdGs0ellpVVVlekcraFJSMEtaM1l1VzJoc1c2NW1GZndmVkxrT1E5aENRNXAzSTBjQ2MzM2Y4RG9TeE5LYVErcHVWR2NaTXhUUEcyUk1EZG1JY3NabU56WndsUnUvWDNXM00rVXlyWlByWVRQMlBCS05pbDRMR1JVTFRzVzdHemgxK05Ic042cDVvRlJWenZhNjVDeGI2T2ZxSlRuNzgwTVFaR0hORldKM3g2QVEwcXg1UUxSODFXNWtXdVNmOUo5clQyNmdnaFBMODBuQkI3MzFMZGtONE8wR2YvM1AvNXJDdmZnZ0prdldxcGxaOWRobkNVdVdLRGg5ZDZyYTk4bE1wMkhXNXoiLCJtYWMiOiI4OWIzYjBlNzk4NGUxNDNjODA0ODNlYjQzNmFiNTVhNDAyZGFkODE0ZThmNDE2YTk3ZDViNTUxNTYzYTNhZThiIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6IjlSWGJpUndVNzJ5SjEzVUxWTDZXT1E9PSIsInZhbHVlIjoiZ2ZQYStqd29UTVFFNXlOMkVNMHFrMGw5UCs4NEVqSk1vbTM0azFwS2pkN3RIajVGdUFUZXJtcGJHS3JJQnJWQmVPOEVSREtNdDdOS2dFZWpNWjM1N09LU1ExNGsxcWp2WlAxQnByOXoxZ0tScytOamkxTXRiaDA2V3djWnJSMXdMRTR5L3hkUDFHZUZhVERLYzhOamUwZmdoUWptOW80ajA1K2g0b3BqTTZkbmMvT2VhSkwwUlBkb2NZZ3BYanRWSTFTbWQ2SFIveHBPWHREVVMwRW5qdW5mZ0lIbkljTzJkUE1BdVo3czNsb3hVbGRWb1F1cnFaM3lBY29FM1M4MW9zZEZNY1dscm1vZWJ4WWhvTVVsU1BsVmUzUm5vRFoyS3pNdHUwZ1pURitpc3c3U2lMUEVuT0tJTk4rQjBpcmNNenpmYStXWm92cEJLOWUvT01qL1RMaElFYldPaFUwYTNraUt5dW52SVQ4WC85eWhPdk53Q1NPR1JPZ2hYL2lhL3BEaW1EMTkrYW5OUDFtRVBHN0J3eFhqUFVBV2htekM4MnlUdm5uV0UwN0VtaFRrcGNyK0dENHBzSlhZQ2VDSnlvcUQ0elpuTkNCaEoyekwveG5EVmNRRkR6RFNVNjRwMkZ3VXhab0kydTM4UGFvU2sxRU5SNHU5V0VPaGxOajQiLCJtYWMiOiJhOTBkNGYyZWFjMjA0ODY1MTg4MWJiM2EyZjYxYTU2MmVmZjAwMDQ4NjRmYTAwZDMwMDdmYTc2OGZjYzU2NmJlIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-291368081 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">VloaUQCBjpDKrel7nHeAzB1sTLdTMwteSos1MmAN</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-291368081\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1929890974 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 17 Jun 2025 13:48:36 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6ImQ1MFVveVR5Nmdqc2s5NkFwMm9Icmc9PSIsInZhbHVlIjoiRnhhQ1UxaUhDY0JBbXJ6KzFXU2M3Mmw2NDExaXpYSk94MGphNHg5Wm9wVGhvK2FBRlROWStYWmM3bUV4S1FtaEtDZWV5MllZREkyWHJPZHFQTUdBU3l0SVRJM3RrSGdrSFdncVJjbW1rUExuMHRhYlI1aHVra1ZZYURmVm9aMjExNjdVUWdrdzVJRmR1RWhlQXlIS1o1a083TUxmbVFoUnNpZGZwa2I4U3RVOVZ3RmxTMHNmdTY2a1dvUHRZVHZheGV3UHRIZlUyRVhTRHdSWGdpTGZFRkhtK1JZdzZVUWpkcnBsMTExYzV3d1B2cExvc0Jnb0dER0dwWC9OVXBLd0lFejg0UUdRV0lJYURHNEoydTByU0NXQjdoQmZNMm54a2QrRGVaRjZOMmxBTkY5L2ZmS3ROazV3TlhjdEs0c0daNFZ1RWpqS202dXZ3N0FsVVByUFNXRGlYV3hZRUw4Vm93NGpzaFBFUlhRb3A4STgvQU00bTJhVnN6clJvSVQwbnVXVThZUmkwaDNIMkJuZzFPU002QW4rNGkrZ3hyWktvK1prZE0wZWlzek4vRUJ1LzhhZnhEclJmZkxoMkJvRGNLa2ZDdHV5WVZRVExDNnI5WW5xSFlHYk9POEtzR3lNU3hOajZFWEE3SndUUXdCbUwxSXRwdjNmSHovcWU3VmMiLCJtYWMiOiJjZWRiNDkyY2E2NWI1YWJlMjkwYmE4ZmI2OTFhZTU1ZWZlM2Y4OTYwMWYzOGZjNTk3MDNjMzI3YzJjZTAwZjFjIiwidGFnIjoiIn0%3D; expires=Tue, 17 Jun 2025 15:48:36 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6IlZsQmt3UHBCVFdNRGhneWNQejBKSFE9PSIsInZhbHVlIjoiZGJqK05jcFl0MFhZajR0TmhSczljaEFZNlFvTk1EMkJJOERLUlYwUldXZXhGemg1TkxYVFl0eVJORS9xMk9GTzB0ZE1oakNsMjkwZll1MUcxbHhMdElmSUFxaFdlQWg0WDR2a1FVSXZpbW53cityWkVJTTJHSm5NSXhlS2NldmdTRDZIVExkbFduWExGclRDUkJacmY2UlJ3SHN4RDFxbC9jOUhOL1dvNTZYZ0tCQkRmVHdUZk1ZZm9YVUNPWExpYkoyVFNhNUhlWXhaMW9NSjJRYnQ1c0hIalJtRzhiU3Zzc0JCQUFJakIzN2d2dEZsS0trZmVtNlNJOXZqVGIwZCs3VjlCQ2U3WE9mRjBMM2FUSWMrTEdpd0RlSWt6UWdGMFlGV0Q4akRNT3dLVTMwRlpiM3lnUEk0UEZKc3hGRnFlRDVDRE50ZFVxcjl4ZWZEMXR2R0V3K0RGMW1hMisxb2FkampUdWRlcnpaai94L05rY1VjSm9FUncwTnlkT2dMY1QwUWxzZDR3V3lMMmNCMkkwL2M4RzZMekJOVjlDdjdENUhMWnIzNzBrNzhhb1dVYk4zam5TVVQ5dy84N0xUM0xXdjJkeFYyY1MxV3Znd2hSb0JiNGRlMDVTeGhYYUpRbVBQdFJYbGYwUlpqKzVZUzllSWlrN2FBd1dlc0o3NE4iLCJtYWMiOiJlNDg0YjA5OWI5ZjY2NjgxNTQ4YWI1YjdiNTEwNTExY2QwMDE4YzQ0OTM2MzM0NjgwM2UyYzRlMmU5NDAzYjA3IiwidGFnIjoiIn0%3D; expires=Tue, 17 Jun 2025 15:48:36 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6ImQ1MFVveVR5Nmdqc2s5NkFwMm9Icmc9PSIsInZhbHVlIjoiRnhhQ1UxaUhDY0JBbXJ6KzFXU2M3Mmw2NDExaXpYSk94MGphNHg5Wm9wVGhvK2FBRlROWStYWmM3bUV4S1FtaEtDZWV5MllZREkyWHJPZHFQTUdBU3l0SVRJM3RrSGdrSFdncVJjbW1rUExuMHRhYlI1aHVra1ZZYURmVm9aMjExNjdVUWdrdzVJRmR1RWhlQXlIS1o1a083TUxmbVFoUnNpZGZwa2I4U3RVOVZ3RmxTMHNmdTY2a1dvUHRZVHZheGV3UHRIZlUyRVhTRHdSWGdpTGZFRkhtK1JZdzZVUWpkcnBsMTExYzV3d1B2cExvc0Jnb0dER0dwWC9OVXBLd0lFejg0UUdRV0lJYURHNEoydTByU0NXQjdoQmZNMm54a2QrRGVaRjZOMmxBTkY5L2ZmS3ROazV3TlhjdEs0c0daNFZ1RWpqS202dXZ3N0FsVVByUFNXRGlYV3hZRUw4Vm93NGpzaFBFUlhRb3A4STgvQU00bTJhVnN6clJvSVQwbnVXVThZUmkwaDNIMkJuZzFPU002QW4rNGkrZ3hyWktvK1prZE0wZWlzek4vRUJ1LzhhZnhEclJmZkxoMkJvRGNLa2ZDdHV5WVZRVExDNnI5WW5xSFlHYk9POEtzR3lNU3hOajZFWEE3SndUUXdCbUwxSXRwdjNmSHovcWU3VmMiLCJtYWMiOiJjZWRiNDkyY2E2NWI1YWJlMjkwYmE4ZmI2OTFhZTU1ZWZlM2Y4OTYwMWYzOGZjNTk3MDNjMzI3YzJjZTAwZjFjIiwidGFnIjoiIn0%3D; expires=Tue, 17-Jun-2025 15:48:36 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6IlZsQmt3UHBCVFdNRGhneWNQejBKSFE9PSIsInZhbHVlIjoiZGJqK05jcFl0MFhZajR0TmhSczljaEFZNlFvTk1EMkJJOERLUlYwUldXZXhGemg1TkxYVFl0eVJORS9xMk9GTzB0ZE1oakNsMjkwZll1MUcxbHhMdElmSUFxaFdlQWg0WDR2a1FVSXZpbW53cityWkVJTTJHSm5NSXhlS2NldmdTRDZIVExkbFduWExGclRDUkJacmY2UlJ3SHN4RDFxbC9jOUhOL1dvNTZYZ0tCQkRmVHdUZk1ZZm9YVUNPWExpYkoyVFNhNUhlWXhaMW9NSjJRYnQ1c0hIalJtRzhiU3Zzc0JCQUFJakIzN2d2dEZsS0trZmVtNlNJOXZqVGIwZCs3VjlCQ2U3WE9mRjBMM2FUSWMrTEdpd0RlSWt6UWdGMFlGV0Q4akRNT3dLVTMwRlpiM3lnUEk0UEZKc3hGRnFlRDVDRE50ZFVxcjl4ZWZEMXR2R0V3K0RGMW1hMisxb2FkampUdWRlcnpaai94L05rY1VjSm9FUncwTnlkT2dMY1QwUWxzZDR3V3lMMmNCMkkwL2M4RzZMekJOVjlDdjdENUhMWnIzNzBrNzhhb1dVYk4zam5TVVQ5dy84N0xUM0xXdjJkeFYyY1MxV3Znd2hSb0JiNGRlMDVTeGhYYUpRbVBQdFJYbGYwUlpqKzVZUzllSWlrN2FBd1dlc0o3NE4iLCJtYWMiOiJlNDg0YjA5OWI5ZjY2NjgxNTQ4YWI1YjdiNTEwNTExY2QwMDE4YzQ0OTM2MzM0NjgwM2UyYzRlMmU5NDAzYjA3IiwidGFnIjoiIn0%3D; expires=Tue, 17-Jun-2025 15:48:36 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1929890974\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-40180912 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">error</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>16</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>error</span>\" => \"<span class=sf-dump-str title=\"13 characters\">&#1575;&#1604;&#1593;&#1585;&#1576;&#1577; &#1601;&#1575;&#1585;&#1594;&#1577;!</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-40180912\", {\"maxDepth\":0})</script>\n"}}