{"__meta": {"id": "X65124458120b65152ebb9487a9e4e030", "datetime": "2025-06-17 13:53:07", "utime": **********.129019, "method": "POST", "uri": "/chats/favorites", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1750168386.496773, "end": **********.129045, "duration": 0.6322720050811768, "duration_str": "632ms", "measures": [{"label": "Booting", "start": 1750168386.496773, "relative_start": 0, "end": **********.022768, "relative_end": **********.022768, "duration": 0.5259950160980225, "duration_str": "526ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.022784, "relative_start": 0.5260109901428223, "end": **********.129048, "relative_end": 3.0994415283203125e-06, "duration": 0.10626411437988281, "duration_str": "106ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 46117960, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/favorites", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getFavorites", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "favorites", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=454\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:454-477</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.028990000000000002, "accumulated_duration_str": "28.99ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 16 limit 1", "type": "query", "params": [], "bindings": ["16"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.062223, "duration": 0.026850000000000002, "duration_str": "26.85ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 92.618}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.104552, "duration": 0.00101, "duration_str": "1.01ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 92.618, "width_percent": 3.484}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (16) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.1166408, "duration": 0.00113, "duration_str": "1.13ms", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 96.102, "width_percent": 3.898}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "16", "_previous": "array:1 [\n  \"url\" => \"http://localhost/hrm-dashboard\"\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/favorites", "status_code": "<pre class=sf-dump id=sf-dump-1742321100 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1742321100\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1232357158 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1232357158\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1849528032 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1849528032\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1706176336 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"30 characters\">http://localhost/hrm-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1936 characters\">_clck=2gyfy%7C2%7Cfwu%7C0%7C1994; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clsk=1ti4td2%7C1750168382688%7C6%7C1%7Cl.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6ImMweXRhdWE2YklVbVJRdURHUi9qeHc9PSIsInZhbHVlIjoiRHJZbENNZkVYcVZMU25ycVBhNEtVT1JHbkx1VzJZUzR2VTNUNm5CY0p5WWdRUEx4TjNWV21VWWRrZGJ3Uk9kZU5LcmxUQjJvSGdjNU9paWphYXRUM3ptUzcva1doaUdNRlNla0JDSE9rL0pKM2U3RnZZWE81WDVmbWQ1eC8xb3VYMmdZVks1ckFUU1E2RzYyS2Myd2tvWmorNUM1M3NXS25EK3E3eElrTFFwNjU2QjczREZjSGt0N3hjMXhvcklCVzdmSFB4cHVucUQ1VTNpVE9SQUpRcWtDNTkrM295VitQQjQ5NUVkWTJCNk5nR2tnVFg5ZkZYUlQxb3pPbWh4UDBNMTMvVTU3L1hpL1FnQUpkSnRDSkIrSWZESVJYV2I2a2ZidU8wcEQvcDA2T3R3LzRkZlYwclAzUGZoZlROeEk2Q2ZRL1pScEtsdmkwK21NVmd4azhjMjdrcHpiVDFuYmd1MFU1ZXBQV0crQWFmZjNES2RTcEkvNUVyZzh5VEVRWW1YeWFyQ1FRVnI3UGRUVnU1SVM5dkRrMGY4YThDdkhZVWlSQUJDOWdhdnM1YXQ2a0I5YlNlK1FJS1ZtSEhOU3hQamthSEI2RzVHZXBTN2VqODhKSEo0TVR0QkRGQ0lMM3dpSTkzOC80OXdoaitVTmdXRGZyZVVUUXkyR21ET1oiLCJtYWMiOiJiY2JhZTc5ZDZmOTE2MTE5YmRlMTU4NWE4NzAwYzgxMWJhMzdkOTRiZDcwNjlkNGY2NGE1YzFlODNkMDFmZTZlIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6IkZzSWhUdFFuOGVOcklvVkxQOE9OYWc9PSIsInZhbHVlIjoiTFdRVU5UQTVZZS9yQzJ0eHloTDluYm1nN2tqeVJ3UE41TEdESXBOZzdZM1g3TmtkQ0J2VDk0REhDTHlTSk9NWXB2NEc2WHI2eEFNZGVGU04xanRRUCswQTNsNE1DOU5WcHJxY0tnTWxleW9WWmZuR0hTKytCdk5SMzFIWUpmR1l6RFc1d2tINkJjRVNxZGt0Z2tKT3QrWTRlYUptNXpaTVVtUFk5S1l4YmRjdVFMYzE0RmRJS0NZdVNsR1YvUndjdERtK0pYWDNVcTZ5M3ZBdGxBcVBYWkZTQjVKM081aEtMYzRGSzliZ0pxVWFpVG9NeGlwcmNEVndNRm1LcnBaYzh1cHdJTDc2WHlpMlhLWkFqOTdjMjhDRDR6aWNtYnh3UnNiVnBrb1c5cEZDandsVWtRTzhudDN0ZVlqUUc0OG5hZXJBYTZpdWs1bmRQa29zbU9Rb1pzNnlLWCtzVjN1cWFMRVJhcmZZZjJTVmh6K1ZwczVDUGc5TE0zQkZKcmM3NEZnaXpTdDVVWFcyaEMzS0ZXZTJyZEpmQXFCQ0R5OG10aTBqR05DUUN6UU5jUUpzT096NEE1S2tVVGdpV25hREwvajVQUzU5RUdzbFBpQWNzNkVjV2NJaUt0Ylo4ejkzY0xMTlIyeWovQUpnZEFteSt3SGpUR29tNmhGT2RCUkkiLCJtYWMiOiJkMmM0OWMzMTc5ZjcwNjI4NmUyNWViMzY3NjdjMTUyN2E5NTU2NjE0ODllZDU1ZWJkMjJiMDlhMzQ0NTI1NWM0IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1706176336\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-386912264 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">VloaUQCBjpDKrel7nHeAzB1sTLdTMwteSos1MmAN</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-386912264\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-407738363 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 17 Jun 2025 13:53:07 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IkQ3cGg2ODROWjQvMU4rSFBUM1pKd0E9PSIsInZhbHVlIjoiV1prbmIwRDVTV0gzejlHS21DeE43QzAxazA4OS84b1ZPSkRqdHlVZTNvT0dueUsvL0VucnZlNTdiYk90Y0hmaFFBbmVZR2s3REZZUDBjc0dtT25oRzJXakl5ckpNL1JvcWdRTEdFVjhNTCtNMFB1elQ0cm92clNWR2pJTTJuNDNyNW1seDYxMDhSbi9TYVNBd2dpcXV0dldyblFjb29lbnpNWHRSNWx0NStnR3ZvUFhPa3N4VjVWQjMzTmJYTWMvM1lEWlR4dlVlT09jdS9NWHpFRDFTaGxrY2l0bFNzUWZUZmNYSWdZRGN5OTBwYzBPblBQVExYVWRxVU4xMG1HVEFQbmlrVk5YUlRlb0k3NS96RGNVQ2xlbjdvTjBBeEF6eWRXbEdiamVOQUkvOFdkaFNZekppOE9oeVplNFlOaE9IK0xpQ3l4QUNXZU04Z2lldmpVUjNteXNrOE5zNUhjek1ZM3hxeFoySDFpdkZjc1dTNk84RXU3RlpmMzdDVEZDbjlpZHRPTFN5V2tlTy9QZXdvYy8vTEhNa2ZaZnpxbkdSZjdzRDJ5c1lLaEwyT3lIdks3NUx3YXJBdVk3bDZERlBuUlZrcUJoalRnWUJLVE5CSHpISE5pdnVybjQ1eThGK2syQWhHY01XM294NFhaZ2I0L0laakhqRHM1bjRqTjQiLCJtYWMiOiIzZmFiZjc1YjYxZWU2ZTI3NTAzYjY5NTgzZjZlZmE2NDMyY2I4OWQ4OTc3Mjg3OGRkMTFiNDY0YTI2YTdjNTY0IiwidGFnIjoiIn0%3D; expires=Tue, 17 Jun 2025 15:53:07 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6ImdxN0gvUjdidUdtNjBIQmVrV3BtRnc9PSIsInZhbHVlIjoic09BRkNkek0vSmxDdXlsTGs0OUNnUkp4WldhM2hMWmlJQU5EOERSdHBvK2JBeWJJd1Q1eXh5cldUV2JFWjhTQWZLcFVLKzFoVnNhM2hRWnEyeVJMcVZocXRxVzM0U1NwYjJoWnRkQmtjMmJzRmhvQjlBbElZSHpkSmhLWkhDcU5nR0pvaHZONlMwRDNpT3VpSjB2ckRudXExRDJ6RGtwYnNMUmthNm55MVZkcE5WcTQzM0h0djdtMDdlNE9oUzMyOGswWk1laG1iTUkrWEFXVTk2NUlEZzVqbENxT1lQMlhrS3pxditBd3grTGptQitCQWt0bmo5cVFoTWFmYm5PM0o4bU5TRmtsUTA1Mnd1eGlGeWEyM1k1aHdqNXV2bGFsQTJNYnRpZzYvVkpFRkh0Q3JsbXhnRVVuZU5ydnkvdHYrSFdoY2U4bTJCeVdaZ1NZNEVlb3JIUlFJZXNPcTZLZlFFNGg3RkkwbVVpNU1yUTFIem84VmhvUkZ4M0F1d3kyR25JQUFqakRYOGhRS0ZzekFvWjZvVzh2OHdOZ0VLOUlkZ2xoL0ZURG5RMjdFRU1FcGc5RUxFdElIUTJFU3ZVYUtteEt2WW85MzBnNGdtRERRUUhBNTVnRkxRaXhOZjlQNVlWc0hXOGE5N0dOOExYN2JCNE1rYlg0YTYwYXZ1SzUiLCJtYWMiOiJlMWNkMDFkY2NjOTQyZDllZTM3NzYxOTUxZjNkZTlhNzk1OTc1YzQyZTUyYjMxNjhkOTQzOWYxYTViZjg4ZjY4IiwidGFnIjoiIn0%3D; expires=Tue, 17 Jun 2025 15:53:07 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IkQ3cGg2ODROWjQvMU4rSFBUM1pKd0E9PSIsInZhbHVlIjoiV1prbmIwRDVTV0gzejlHS21DeE43QzAxazA4OS84b1ZPSkRqdHlVZTNvT0dueUsvL0VucnZlNTdiYk90Y0hmaFFBbmVZR2s3REZZUDBjc0dtT25oRzJXakl5ckpNL1JvcWdRTEdFVjhNTCtNMFB1elQ0cm92clNWR2pJTTJuNDNyNW1seDYxMDhSbi9TYVNBd2dpcXV0dldyblFjb29lbnpNWHRSNWx0NStnR3ZvUFhPa3N4VjVWQjMzTmJYTWMvM1lEWlR4dlVlT09jdS9NWHpFRDFTaGxrY2l0bFNzUWZUZmNYSWdZRGN5OTBwYzBPblBQVExYVWRxVU4xMG1HVEFQbmlrVk5YUlRlb0k3NS96RGNVQ2xlbjdvTjBBeEF6eWRXbEdiamVOQUkvOFdkaFNZekppOE9oeVplNFlOaE9IK0xpQ3l4QUNXZU04Z2lldmpVUjNteXNrOE5zNUhjek1ZM3hxeFoySDFpdkZjc1dTNk84RXU3RlpmMzdDVEZDbjlpZHRPTFN5V2tlTy9QZXdvYy8vTEhNa2ZaZnpxbkdSZjdzRDJ5c1lLaEwyT3lIdks3NUx3YXJBdVk3bDZERlBuUlZrcUJoalRnWUJLVE5CSHpISE5pdnVybjQ1eThGK2syQWhHY01XM294NFhaZ2I0L0laakhqRHM1bjRqTjQiLCJtYWMiOiIzZmFiZjc1YjYxZWU2ZTI3NTAzYjY5NTgzZjZlZmE2NDMyY2I4OWQ4OTc3Mjg3OGRkMTFiNDY0YTI2YTdjNTY0IiwidGFnIjoiIn0%3D; expires=Tue, 17-Jun-2025 15:53:07 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6ImdxN0gvUjdidUdtNjBIQmVrV3BtRnc9PSIsInZhbHVlIjoic09BRkNkek0vSmxDdXlsTGs0OUNnUkp4WldhM2hMWmlJQU5EOERSdHBvK2JBeWJJd1Q1eXh5cldUV2JFWjhTQWZLcFVLKzFoVnNhM2hRWnEyeVJMcVZocXRxVzM0U1NwYjJoWnRkQmtjMmJzRmhvQjlBbElZSHpkSmhLWkhDcU5nR0pvaHZONlMwRDNpT3VpSjB2ckRudXExRDJ6RGtwYnNMUmthNm55MVZkcE5WcTQzM0h0djdtMDdlNE9oUzMyOGswWk1laG1iTUkrWEFXVTk2NUlEZzVqbENxT1lQMlhrS3pxditBd3grTGptQitCQWt0bmo5cVFoTWFmYm5PM0o4bU5TRmtsUTA1Mnd1eGlGeWEyM1k1aHdqNXV2bGFsQTJNYnRpZzYvVkpFRkh0Q3JsbXhnRVVuZU5ydnkvdHYrSFdoY2U4bTJCeVdaZ1NZNEVlb3JIUlFJZXNPcTZLZlFFNGg3RkkwbVVpNU1yUTFIem84VmhvUkZ4M0F1d3kyR25JQUFqakRYOGhRS0ZzekFvWjZvVzh2OHdOZ0VLOUlkZ2xoL0ZURG5RMjdFRU1FcGc5RUxFdElIUTJFU3ZVYUtteEt2WW85MzBnNGdtRERRUUhBNTVnRkxRaXhOZjlQNVlWc0hXOGE5N0dOOExYN2JCNE1rYlg0YTYwYXZ1SzUiLCJtYWMiOiJlMWNkMDFkY2NjOTQyZDllZTM3NzYxOTUxZjNkZTlhNzk1OTc1YzQyZTUyYjMxNjhkOTQzOWYxYTViZjg4ZjY4IiwidGFnIjoiIn0%3D; expires=Tue, 17-Jun-2025 15:53:07 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-407738363\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1806875806 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>16</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"30 characters\">http://localhost/hrm-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1806875806\", {\"maxDepth\":0})</script>\n"}}