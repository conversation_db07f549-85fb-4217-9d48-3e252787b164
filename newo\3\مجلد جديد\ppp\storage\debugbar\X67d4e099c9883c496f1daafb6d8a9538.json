{"__meta": {"id": "X67d4e099c9883c496f1daafb6d8a9538", "datetime": "2025-06-17 13:33:54", "utime": **********.240508, "method": "GET", "uri": "/customer/check/warehouse?customer_id=10&warehouse_id=8", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1750167233.575314, "end": **********.240541, "duration": 0.665226936340332, "duration_str": "665ms", "measures": [{"label": "Booting", "start": 1750167233.575314, "relative_start": 0, "end": **********.148398, "relative_end": **********.148398, "duration": 0.5730838775634766, "duration_str": "573ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.148414, "relative_start": 0.5730998516082764, "end": **********.240546, "relative_end": 5.0067901611328125e-06, "duration": 0.0921320915222168, "duration_str": "92.13ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 46235904, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET customer/check/warehouse", "middleware": "web, verified, auth, XSS, revalidate", "controller": "App\\Http\\Controllers\\CustomerController@checkWarehouse", "namespace": null, "prefix": "", "where": [], "as": "customer.check.warehouse", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FHttp%2FControllers%2FCustomerController.php&line=383\" onclick=\"\">app/Http/Controllers/CustomerController.php:383-397</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.01241, "accumulated_duration_str": "12.41ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 16 limit 1", "type": "query", "params": [], "bindings": ["16"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.1922228, "duration": 0.01043, "duration_str": "10.43ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 84.045}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.217465, "duration": 0.0009, "duration_str": "900μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 84.045, "width_percent": 7.252}, {"sql": "select * from `customers` where `customers`.`id` = '10' limit 1", "type": "query", "params": [], "bindings": ["10"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/CustomerController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Http\\Controllers\\CustomerController.php", "line": 388}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.225544, "duration": 0.00108, "duration_str": "1.08ms", "memory": 0, "memory_str": null, "filename": "CustomerController.php:388", "source": "app/Http/Controllers/CustomerController.php:388", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FHttp%2FControllers%2FCustomerController.php&line=388", "ajax": false, "filename": "CustomerController.php", "line": "388"}, "connection": "kdmkjkqknb", "start_percent": 91.297, "width_percent": 8.703}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\Customer": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FModels%2FCustomer.php&line=1", "ajax": false, "filename": "Customer.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "16", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]", "pos": "array:1 [\n  2044 => array:9 [\n    \"name\" => \"تجربية\"\n    \"quantity\" => 1\n    \"price\" => \"10.00\"\n    \"id\" => \"2044\"\n    \"tax\" => 0\n    \"subtotal\" => 10.0\n    \"originalquantity\" => 1\n    \"product_tax\" => \"-\"\n    \"product_tax_id\" => 0\n  ]\n]"}, "request": {"path_info": "/customer/check/warehouse", "status_code": "<pre class=sf-dump id=sf-dump-1685415206 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1685415206\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-1509379016 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>customer_id</span>\" => \"<span class=sf-dump-str title=\"2 characters\">10</span>\"\n  \"<span class=sf-dump-key>warehouse_id</span>\" => \"<span class=sf-dump-str>8</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1509379016\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1576394617 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1576394617\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-109553799 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1936 characters\">_clck=2gyfy%7C2%7Cfwu%7C0%7C1994; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clsk=1ti4td2%7C1750167218299%7C2%7C1%7Cl.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6InVJY1RJS1JyMXlXdTZhLy9mUmJ4MlE9PSIsInZhbHVlIjoiQVFjV3A3UFF6L2RocmlpakMrUjlwS09MT09iQWlTL2wyYjRnaS8ydmF0MjNIYU1FY0RQbm1PZDY0SGl1eFNGSjR6RVhrZFNWdFZCbjZkbXpiVDRZaEVKQzVwc09MS0hoUWtaNFlEdXUwNVgrcEtid2dJU3hWQzdNcXVBY0JMUkhid28xb1c2WG5IMHcxamFKay9iSmdpanNYaFdwcnRTVm5qd2dFZ2lMYXN4K0dtQVBJdFFIbGJ4YzJ5ZWc4TUJtUTZOeHZWV0pHeFRrR0l3VnlSL0kvdnJ5dUZvSDY1Sk4zczJmUWhTMS9wcnhtanRGZ1FkUlk2TmpxcU5WK2h2VDYrOWQwMDVYWHQxYUxyM0pqQWZKS2IySWZzS1o3L1dRZzVZbTlzcWtaM2QvajEyUDRXNmU5WGFTU3ZodjdSa1hmQVY2c01BRVZ2OFVWS1NpaStLRjN6UWxNaS96cXlCcVNFcVZpTTZHVkNtYWxDa0tkUllvVTUvS2J4NzdoV05hTDJBeGhrQWd0OS9qZkRyTm9IRStCMzQ5ZHNRRTl3WjJlYWFaOGhIWUJuYjNHNUtnbm9Wa010b0IzOFJZRVE0ZXFVR2FPSVZuVmhVREtJSlVUNXhuV3dhbjBjZXlyZ2xVcDdONGMvc1FWeUJrdUhGZVc5U3YrdGtvTVRrSzljM08iLCJtYWMiOiJiYzliNzQ3M2M3NzY3YTI5NTM5M2UwM2QxMTI5ZWQyOTQ3M2NjNzNmYzcxZTZhYjdmZDE1M2UyZjMyNTYwZWE0IiwidGFnIjoiIn0%3D; test_session=eyJpdiI6InhkS3I2eHVTZ3dPaFF4enpMSnlVb0E9PSIsInZhbHVlIjoiTnM5UTBoY2t4YmFEVmlxaElEd1FBNlI3c3RWVlYzVjViNGRVdkl4YXczc09DMVJDNW5EN1R4WGJ0eERiNnFoYkhCb1V2b3gzZzdkVVg4NStlaVB4MW1NY1g4emRxZXZpVkEzWEcyWWxJc3RGNEFQRjZOWFgwdUVRY2c1Y0JISWFYZmpNN1JGa1Z5aHQrd1c3eHhKdDg1TktmWmVQM3dlSDNPTlJoa25hZWFkcmtIVkhPVStRNHY4TlJJUXhpSWNUeEQ0eERDNXFEN09teWVCOVFzV3czUzU1eENoS2RtcHJsSnhYRVM1NjI2NkZTbVlwNi9JK3NYMUxtaWpMcU93ek8vQjNZTWVBUUJ2YUl1Tm9USk55UEdSRkI3TUpXdmg1UC9MYXBUaEJwdWxBV3VVV2xYcHc4MXZBak1JSncrbVRiR1BnbnlHUGtZbW9FUjVyRG1CRFQwTmdGOUw1alpuVEY4b0wrYURxWFI5Z2FJSllBT21aSEp4cmliY0FkSW02WlNxL1lMSXVOOFZZVC82bjNuTUJuWm1GTHE1b2d0a3hqcEh4NzFpUys0Qklsa2hUNDZCd0wyYW1TVlpSZ3RzYTJ2bU1sREJiWHFXZytvY1dIMjF1c1FXaG5oZUgydi9xUE9KM3ZSSjR4dlZRSVQ0UnNWOTc0SHB4MW9HaU9PYUciLCJtYWMiOiIxYTUyNmM2YTEzYTk3ZTliNGZlNjc1ZDMyMjgyY2U3MDMwNmY2YWYyODUxY2EwMDUzZjA5YmFmZTdmN2Q2MGFlIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-109553799\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-285232334 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">VloaUQCBjpDKrel7nHeAzB1sTLdTMwteSos1MmAN</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-285232334\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-224084283 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 17 Jun 2025 13:33:54 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-methods</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">POST, GET, OPTIONS, PUT, DELETE</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-headers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"80 characters\">Content-Type, Accept, Authorization, X-Requested-With, Application, X-CSRF-TOKEN</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IlBSU1dpWU9VUVEycmNFSitlVzQveUE9PSIsInZhbHVlIjoiK1JSZm5lOVZ4THp6enFVclhuYWpKWlFJUE9Yc2gzQU5QeWdJQUJ6OHkweVJSTkwzZjJ1SXVuNjU3Q2c5SEtUbDN3NUVRRUdmRzJnMUFQa3crVkQ4bE5jUWNqcW0rMDJrMjlmTzI5U1FXTysrRXN4RG51bXlUM0JHUDZ5dzdhN1cvdXdXSzBUK1hrdklING9uVTdPZDhpM0lTeTEzZFduSkR4NUI1ZjkyOStqRnB4bTF5UWx3cVBWa0dmYVZxSi9EQjdwVlkzeTZaU0padTFaMzNyUUpmTzdob1JTRXZKUHhpRUI5R25yQU14M2JEQzltWUhTa3I1Q3lnd0VmWEx6ajFTbXB3YTRacElnQ3dkam01Ylhha05tWkZxZUV1SUJzT084WUR6Q2FLWTN6WWM2OEpieG5JeEs3aVVxdHUwSUx1TzN3cUlud3liTlVBcmFuQzE4YVBnWG5pNmZpcFgwZnEzL0RaTWI2NWNXa0NJeUdadHcveGVzZGVGUnA3TVovYS96bmxibnNnbGRyR2xLb3Q2L3V4aE1yM1ViUXlOV2R1Wkd2Sjl0WUVycmRLNXdrM0FxbytwNW1oMlNab3RhRmZCaXd0SGRpdnhMNUtGRXlPS0gvc25kdU5UUEM0eTBhQWdNSmJTUTRmYlFhVmtmNEdveGpkNnJxeWJEUkV0UTIiLCJtYWMiOiI2OGVmMWEyODZiYzkxOTZmYThlZmI4MzA0OGVhYjhlYWJmOWZkY2YxMzg5NmM2NDU4ZTgzYTI1NDM3ODRhYzgxIiwidGFnIjoiIn0%3D; expires=Tue, 17 Jun 2025 15:33:54 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6InV0VWJYakxIYjF1K1NRbUZBQ29qc1E9PSIsInZhbHVlIjoiYTZDdStENDhaNFNhK0hpVTN3VVc1L3Y1Wnpod1pxRk0xTVAwc29aZFoyRVJwRTNiZ3Bpbk9HT2RjcE55ak9IdTBRdElWeUxMcXV3V0lOYktRbDhkblh5bUg4RmxwTFRPTnpMSHJiT2pmaTEwRGwzS1NrOE5GeTg1aGwwTE9wNXMzQUZwMk9wSFdHOGRJZXVmME43dXFIL3lzS2RVeVNpK2R5aGxSV1pTZ3Rxd1RHTEcvS2VIVVV0WmtQS3cxZGVTUFZkejcySGlDRml2TFRzVGhaNDJKUVBQWjZDaTlQdDZEWURQdnhOc2JoMWtSMlA2cmVnUklDdDZjZ2ZFRkFzMHpoa2Y2R3BDeUc2MmdqM2ZIR2lyNUpxT3pFdVU5WXY1VGpCUlQzbktpSy9jdGN5R21kQ0RsVnExQ1Q5WjlqbW5iSHJ2ZmxzRnhsYXlFUGJUK0Y0dXkxUGY3b3duQ05wcnllRDk5eGdpWkU4RXZDREEzME9GYURpUSt6RlczZVFQd2VQWjUwWjZDSGh1S1ZCT0d0Vmd6OXhnVEdTVFhlSE50YURuRE9uUkFpd3JsN1NXYjlSamRhMDdCOTRCL2NQakxQM3pUTXorQnRpS1J0ejloL05OYWEycGI5Nk5KNjhCYkxlWWdUTGRtSk5oS2lrcXNQV1lzdUZPSEF3b05kQ1giLCJtYWMiOiI1N2Y0OGNlZmZjNTZlNGRkMjgzZDU2OGYwZWEyMTQxNDUyNzJiM2NkYTU3ZTkyYzNmZTdiMWU0YzUzMDExZDMxIiwidGFnIjoiIn0%3D; expires=Tue, 17 Jun 2025 15:33:54 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IlBSU1dpWU9VUVEycmNFSitlVzQveUE9PSIsInZhbHVlIjoiK1JSZm5lOVZ4THp6enFVclhuYWpKWlFJUE9Yc2gzQU5QeWdJQUJ6OHkweVJSTkwzZjJ1SXVuNjU3Q2c5SEtUbDN3NUVRRUdmRzJnMUFQa3crVkQ4bE5jUWNqcW0rMDJrMjlmTzI5U1FXTysrRXN4RG51bXlUM0JHUDZ5dzdhN1cvdXdXSzBUK1hrdklING9uVTdPZDhpM0lTeTEzZFduSkR4NUI1ZjkyOStqRnB4bTF5UWx3cVBWa0dmYVZxSi9EQjdwVlkzeTZaU0padTFaMzNyUUpmTzdob1JTRXZKUHhpRUI5R25yQU14M2JEQzltWUhTa3I1Q3lnd0VmWEx6ajFTbXB3YTRacElnQ3dkam01Ylhha05tWkZxZUV1SUJzT084WUR6Q2FLWTN6WWM2OEpieG5JeEs3aVVxdHUwSUx1TzN3cUlud3liTlVBcmFuQzE4YVBnWG5pNmZpcFgwZnEzL0RaTWI2NWNXa0NJeUdadHcveGVzZGVGUnA3TVovYS96bmxibnNnbGRyR2xLb3Q2L3V4aE1yM1ViUXlOV2R1Wkd2Sjl0WUVycmRLNXdrM0FxbytwNW1oMlNab3RhRmZCaXd0SGRpdnhMNUtGRXlPS0gvc25kdU5UUEM0eTBhQWdNSmJTUTRmYlFhVmtmNEdveGpkNnJxeWJEUkV0UTIiLCJtYWMiOiI2OGVmMWEyODZiYzkxOTZmYThlZmI4MzA0OGVhYjhlYWJmOWZkY2YxMzg5NmM2NDU4ZTgzYTI1NDM3ODRhYzgxIiwidGFnIjoiIn0%3D; expires=Tue, 17-Jun-2025 15:33:54 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6InV0VWJYakxIYjF1K1NRbUZBQ29qc1E9PSIsInZhbHVlIjoiYTZDdStENDhaNFNhK0hpVTN3VVc1L3Y1Wnpod1pxRk0xTVAwc29aZFoyRVJwRTNiZ3Bpbk9HT2RjcE55ak9IdTBRdElWeUxMcXV3V0lOYktRbDhkblh5bUg4RmxwTFRPTnpMSHJiT2pmaTEwRGwzS1NrOE5GeTg1aGwwTE9wNXMzQUZwMk9wSFdHOGRJZXVmME43dXFIL3lzS2RVeVNpK2R5aGxSV1pTZ3Rxd1RHTEcvS2VIVVV0WmtQS3cxZGVTUFZkejcySGlDRml2TFRzVGhaNDJKUVBQWjZDaTlQdDZEWURQdnhOc2JoMWtSMlA2cmVnUklDdDZjZ2ZFRkFzMHpoa2Y2R3BDeUc2MmdqM2ZIR2lyNUpxT3pFdVU5WXY1VGpCUlQzbktpSy9jdGN5R21kQ0RsVnExQ1Q5WjlqbW5iSHJ2ZmxzRnhsYXlFUGJUK0Y0dXkxUGY3b3duQ05wcnllRDk5eGdpWkU4RXZDREEzME9GYURpUSt6RlczZVFQd2VQWjUwWjZDSGh1S1ZCT0d0Vmd6OXhnVEdTVFhlSE50YURuRE9uUkFpd3JsN1NXYjlSamRhMDdCOTRCL2NQakxQM3pUTXorQnRpS1J0ejloL05OYWEycGI5Nk5KNjhCYkxlWWdUTGRtSk5oS2lrcXNQV1lzdUZPSEF3b05kQ1giLCJtYWMiOiI1N2Y0OGNlZmZjNTZlNGRkMjgzZDU2OGYwZWEyMTQxNDUyNzJiM2NkYTU3ZTkyYzNmZTdiMWU0YzUzMDExZDMxIiwidGFnIjoiIn0%3D; expires=Tue, 17-Jun-2025 15:33:54 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-224084283\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>16</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pos</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-key>2044</span> => <span class=sf-dump-note>array:9</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"6 characters\">&#1578;&#1580;&#1585;&#1576;&#1610;&#1577;</span>\"\n      \"<span class=sf-dump-key>quantity</span>\" => <span class=sf-dump-num>1</span>\n      \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"5 characters\">10.00</span>\"\n      \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"4 characters\">2044</span>\"\n      \"<span class=sf-dump-key>tax</span>\" => <span class=sf-dump-num>0</span>\n      \"<span class=sf-dump-key>subtotal</span>\" => <span class=sf-dump-num>10.0</span>\n      \"<span class=sf-dump-key>originalquantity</span>\" => <span class=sf-dump-num>1</span>\n      \"<span class=sf-dump-key>product_tax</span>\" => \"<span class=sf-dump-str>-</span>\"\n      \"<span class=sf-dump-key>product_tax_id</span>\" => <span class=sf-dump-num>0</span>\n    </samp>]\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n"}}