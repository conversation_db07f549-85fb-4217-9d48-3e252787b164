{"__meta": {"id": "X6cc326574d1ebbe6d26d96be8743d58b", "datetime": "2025-06-17 13:27:52", "utime": **********.946273, "method": "POST", "uri": "/warehouse-empty-cart", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.193514, "end": **********.946299, "duration": 0.7527849674224854, "duration_str": "753ms", "measures": [{"label": "Booting", "start": **********.193514, "relative_start": 0, "end": **********.839831, "relative_end": **********.839831, "duration": 0.6463170051574707, "duration_str": "646ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.839846, "relative_start": 0.646331787109375, "end": **********.946302, "relative_end": 2.86102294921875e-06, "duration": 0.10645604133605957, "duration_str": "106ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 46326248, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST warehouse-empty-cart", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\ProductServiceController@warehouseemptyCart", "namespace": null, "prefix": "", "where": [], "as": "warehouse-empty-cart", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FHttp%2FControllers%2FProductServiceController.php&line=1663\" onclick=\"\">app/Http/Controllers/ProductServiceController.php:1663-1673</a>"}, "queries": {"nb_statements": 2, "nb_failed_statements": 0, "accumulated_duration": 0.01761, "accumulated_duration_str": "17.61ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 16 limit 1", "type": "query", "params": [], "bindings": ["16"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.8956, "duration": 0.01682, "duration_str": "16.82ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 95.514}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.929374, "duration": 0.00079, "duration_str": "790μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 95.514, "width_percent": 4.486}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "ixL6BN3l9tyyUscqj0hv99yKIy0daqhD43fAAWcL", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "16"}, "request": {"path_info": "/warehouse-empty-cart", "status_code": "<pre class=sf-dump id=sf-dump-41950469 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-41950469\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-210092111 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-210092111\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-3776153 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>session_key</span>\" => \"<span class=sf-dump-str title=\"3 characters\">pos</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-3776153\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:19</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">15</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">ixL6BN3l9tyyUscqj0hv99yKIy0daqhD43fAAWcL</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1937 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=1pubyvl%7C2%7Cfwu%7C0%7C1993; _clsk=25xk9k%7C1750165707106%7C8%7C1%7Cl.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IlQ2OHZTUGhUS0hDeXY0Z0MyVVhTSlE9PSIsInZhbHVlIjoiSnZha3doL3BOTVB6YW92WWZUWHRKdG5oaHlrRUdxSEJNakl4aisrbUtVREk2WlUvNGN4V1BJK2tMdkgzME9KVUU2ZmFBRDdkdkFUVVA3bU1yM0ZGUTErV2dBcHd2dmE0ZHhIWVo0SUtLRENoUlRLL09XRWVhM0lWYzVZd2pJOG9ER3NxRFE0YllPSU9tSjhqUU9yMklUUURQaTZXOCtmc2NBbXJTMlROUHk5THJ2UTZQUTRtVHpmZ1Q0TVdoTmwySEdlcGxoYVk4V0p0OUk2S3crT2kxclphVFRYbjhJbDJlMlNLWUJLbmsvTTVnT2xMS2VFN3pvN0hhdlZFZTN5MjB3Y21oSks2ODl6c1Y4Z056clgwSEhRY2I0Tmo1Rkl6d1JnNzNiY3JsWWR4K1c2ckE2YlIzWEtNR3htbXVoRTdLd3NsMEtxZ25WMTlGYmFkTjh1NEZiU3ZzSG1oWTRoVTRaR3gvbUFjNmdHYlVFZjhlK0FNUjhLajBJZXFzZEdjUVFHRVhtWm81M3VJUFBWdFYxMlpaeHZwUVhoWEc4QkRQTVhzazFxb05Vd2wzRGR2YnFtRmVuMjZFei9pczlCUU03WmlXcUNHTEVPc3hYZW5qeEpHOXZLd2RmLzZCbDl3Z0IyTkErM08zR25VMDFjWWNDbFdQdkh2QXdXNm5hZWkiLCJtYWMiOiI5MWZmMzdhOTBiNTliN2YyZGMzZjM1ZGRjZjAwMjVlYzY4YWQzYmU4OGM3YTFkMjUzNTk1ZGRlMTQ5ZGE3MTQ3IiwidGFnIjoiIn0%3D; test_session=eyJpdiI6IlE3aHdqaDk3SXVNY29ucURnTzVLbmc9PSIsInZhbHVlIjoieTc4NU8wYlJGemlvZTR5RG5RUldOalM4aTJnTVdIOHY1UFNOOU5BeGtsbFZaRjRxMVpiM3M1TlpEdXR5Q3Y2WHlwdXRLWGRSeTBCQUV3QmhGVlZFWWptOEtOUUlyY1J0Zlo4Q1JPK2JnRmdhK2g3bVpHODNxNzdlU01Gc3BYK0hQSmJ0Mm02bFRPc2pmakhJcnkxYnYwNG9UR1NxSkVUUWxQOENmYUFlUFpJTm5NVysvT2NKbnplaGg0Nys5VjdQS2ZMeXhKYkR2RTkycmdEcEgyWHUvNnFXb1VzSFRhTFNHVE1VSG90Q25iRGNDWjRDWU9PbzhKdnRHVEpvdmJNYnJkeTkvT2dCcFplTE5TRGdKcUZKdFB6ay84R0NNYTZRMUIzNkttaHNHcVNjTVRqaVUwK3RMN005c2VUbThBNk9PTUdoTXFoSkZ1OVJrUXZNU3hMb2YxUDQwMXB4OUJzMTVEem9FSDNzWEd3N2xqb0NjT2lrOUFUWTBXL0g4YnI2cGJ3VDRjTEQ3Q0dWUEUyUXBxR0huWURqNzJQdUNYVndlTjlieVhYU29IWmhyUmR6bVVIVnJaVFdtRENEUHdZeHFJTnBpTEJPazdodnZjbS9ham1mWk5oWkVXQTl5SnNTbGRYOTB5T2hWa2U5U0VlVVF2VE5QSEIrWXFGdFYrdXYiLCJtYWMiOiI1ZGI5NDc1NjUxOGY2OTdlNGVlM2UyODVhMjVjN2M3MDFmMGZmNDg5N2MwMTZmMWZjMWZkMWFhN2RmMjM5OGU4IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-96291028 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">ixL6BN3l9tyyUscqj0hv99yKIy0daqhD43fAAWcL</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">coJ97Z8YyNCeFgF4h0jUL1PrpYBjauguIcJbJbm8</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-96291028\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 17 Jun 2025 13:27:52 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Inc1TDZGTHpKZFVPWUlmYUs4TmJvUGc9PSIsInZhbHVlIjoic3hGRi91c1Vmdmc0UWpVRU1qQ0RWbVEwQ1RabnpRMjM0UG0vZUdHUzVTaW1ZU3l6NVZ2UjBHdUp2cHN0Z2JpQnRHOHdFZmVZZ0dYSk9QK1RKTFJDdm1WVVRiOVJCMVo4YjkvcG00amZaek1YSUluVnYvNU04TDF2b1Job2VOcnE1alJqMXkzekFLWkRsWW9WZVNXV0VZUFNVTGhpNk1iNEhDbjBiMHphK1I5TGNwMEdINi9WdXhjVjVRUDBTTW1QWU41dmRkbmJPaDlDc2hRZjZNWnhtUFBXK3RpdUxvdHB2dzBWRFpEM1AxRDIyY3NDU3dmRWZTWjllTXFxS3BuVW80YWJFVkFQRGx2bk9veVpCTXR1eG01eTlRWE1WYXd5K1RHTDJhQnNtcWt2Sk00WXlWeks1VXRlVE13YnNLcGNjSGVHRzRGeVMwNDdyUGZUU3JPdVZKUzR1eEZkSVBFS00wd3lYTnR6dGZBVzJ4VjBobE1ObHhjZEJiaUxRSml3SHpnNEtONkRsMUFCaHNRZzgwR3dIZnk1WlVXZXJwcUdkMTh4VElKK09yNU9QWmdiUE9aNHBLNmVtTkV6KzZ2akZlbkZLeWM5WlF5RWlPQVZpcWFKMW42NGlQa2tNdEkyNjBkYUlaaksyUFg3dXc0THl5aW41dUEvK1EyeHRzN20iLCJtYWMiOiJiZTQzNDRmMDkwNGRmYjdiODQzNzU4ZmQ4NTJkODM0YTVmOTcyOWJkZTY3NWFjZTIwYzg5OTVhZjdlZDc2MTdjIiwidGFnIjoiIn0%3D; expires=Tue, 17 Jun 2025 15:27:52 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6IjJLMkhDUnZVWUxVWjRpWUN1RkxlL0E9PSIsInZhbHVlIjoiazBBWC82dWk0MEkwNkNLSFFXYS9jZU9IL2ZETmhpM1pIWmtBZzRWV0RrM093UUJwSEI0R2QzSm9zdUptOUNaY0RNWURpRTVTOG1KN2psa2VEeW9oMnpQazBsb09jMFlaMTd2cEdJQ2tHYVNNeDFraXlKV3Vkek9CeFRYclEzMU1GK3NMczgrR1JwZFo2bzNWb3NqVDgyQ2IzWExXSWFhT3ZCOHhYQ0V2ZkFzK1pKa2JlMVc2TjRwQjMyQjZveXZJWHVMcStSWXNMZkNmVW1JUGw2QURKWjY5b3h2YXJGRHZQUS9oQ21ndDFwMjJZcDVPYzQyYkQxbS9qN2xZUFhIRmpZU3pQNG1BOHBCeit2QklSam9rYlpmMm11Y0ViVFR2LzJ4ZzJ1SUVIWS9Nc2loSURsQ0dEZGlnTDlLVHJPZmJrL05yeGxGNkRVQmx3OEV3a1U5RExyam1hMkNoQW1hMGkrVHZ2bkMyVU5lbW4wb1RRbEtFd1VSREYwd2NJSlIrREJkc3ZqMVZLbVB6UUVxaDEzbXJqb25JUzZYbXA4VkFRczVQZWJMaWZySTgveVFqS1pXN0FVWkUwODZKNVZ3SmhhcGVEaXNOWTVFYmpJTDF2NWI2NnNub3FKNkZrdmM1TnRDbVkyNjFRQjVZQStmWmIyYVFneGlWelZ0NzBodEIiLCJtYWMiOiI2MGVmZjBmZWIyMzAyNGRjZTE5MzBiYTA4YTk5N2QyN2Q0OTM5MzhmMWJlOTRiOTZhOTVjNDhhOTAxNWMxOWFiIiwidGFnIjoiIn0%3D; expires=Tue, 17 Jun 2025 15:27:52 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Inc1TDZGTHpKZFVPWUlmYUs4TmJvUGc9PSIsInZhbHVlIjoic3hGRi91c1Vmdmc0UWpVRU1qQ0RWbVEwQ1RabnpRMjM0UG0vZUdHUzVTaW1ZU3l6NVZ2UjBHdUp2cHN0Z2JpQnRHOHdFZmVZZ0dYSk9QK1RKTFJDdm1WVVRiOVJCMVo4YjkvcG00amZaek1YSUluVnYvNU04TDF2b1Job2VOcnE1alJqMXkzekFLWkRsWW9WZVNXV0VZUFNVTGhpNk1iNEhDbjBiMHphK1I5TGNwMEdINi9WdXhjVjVRUDBTTW1QWU41dmRkbmJPaDlDc2hRZjZNWnhtUFBXK3RpdUxvdHB2dzBWRFpEM1AxRDIyY3NDU3dmRWZTWjllTXFxS3BuVW80YWJFVkFQRGx2bk9veVpCTXR1eG01eTlRWE1WYXd5K1RHTDJhQnNtcWt2Sk00WXlWeks1VXRlVE13YnNLcGNjSGVHRzRGeVMwNDdyUGZUU3JPdVZKUzR1eEZkSVBFS00wd3lYTnR6dGZBVzJ4VjBobE1ObHhjZEJiaUxRSml3SHpnNEtONkRsMUFCaHNRZzgwR3dIZnk1WlVXZXJwcUdkMTh4VElKK09yNU9QWmdiUE9aNHBLNmVtTkV6KzZ2akZlbkZLeWM5WlF5RWlPQVZpcWFKMW42NGlQa2tNdEkyNjBkYUlaaksyUFg3dXc0THl5aW41dUEvK1EyeHRzN20iLCJtYWMiOiJiZTQzNDRmMDkwNGRmYjdiODQzNzU4ZmQ4NTJkODM0YTVmOTcyOWJkZTY3NWFjZTIwYzg5OTVhZjdlZDc2MTdjIiwidGFnIjoiIn0%3D; expires=Tue, 17-Jun-2025 15:27:52 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6IjJLMkhDUnZVWUxVWjRpWUN1RkxlL0E9PSIsInZhbHVlIjoiazBBWC82dWk0MEkwNkNLSFFXYS9jZU9IL2ZETmhpM1pIWmtBZzRWV0RrM093UUJwSEI0R2QzSm9zdUptOUNaY0RNWURpRTVTOG1KN2psa2VEeW9oMnpQazBsb09jMFlaMTd2cEdJQ2tHYVNNeDFraXlKV3Vkek9CeFRYclEzMU1GK3NMczgrR1JwZFo2bzNWb3NqVDgyQ2IzWExXSWFhT3ZCOHhYQ0V2ZkFzK1pKa2JlMVc2TjRwQjMyQjZveXZJWHVMcStSWXNMZkNmVW1JUGw2QURKWjY5b3h2YXJGRHZQUS9oQ21ndDFwMjJZcDVPYzQyYkQxbS9qN2xZUFhIRmpZU3pQNG1BOHBCeit2QklSam9rYlpmMm11Y0ViVFR2LzJ4ZzJ1SUVIWS9Nc2loSURsQ0dEZGlnTDlLVHJPZmJrL05yeGxGNkRVQmx3OEV3a1U5RExyam1hMkNoQW1hMGkrVHZ2bkMyVU5lbW4wb1RRbEtFd1VSREYwd2NJSlIrREJkc3ZqMVZLbVB6UUVxaDEzbXJqb25JUzZYbXA4VkFRczVQZWJMaWZySTgveVFqS1pXN0FVWkUwODZKNVZ3SmhhcGVEaXNOWTVFYmpJTDF2NWI2NnNub3FKNkZrdmM1TnRDbVkyNjFRQjVZQStmWmIyYVFneGlWelZ0NzBodEIiLCJtYWMiOiI2MGVmZjBmZWIyMzAyNGRjZTE5MzBiYTA4YTk5N2QyN2Q0OTM5MzhmMWJlOTRiOTZhOTVjNDhhOTAxNWMxOWFiIiwidGFnIjoiIn0%3D; expires=Tue, 17-Jun-2025 15:27:52 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">ixL6BN3l9tyyUscqj0hv99yKIy0daqhD43fAAWcL</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>16</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n"}}