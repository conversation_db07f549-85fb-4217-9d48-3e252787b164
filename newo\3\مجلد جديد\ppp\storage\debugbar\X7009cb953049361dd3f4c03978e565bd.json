{"__meta": {"id": "X7009cb953049361dd3f4c03978e565bd", "datetime": "2025-06-17 13:50:01", "utime": **********.190709, "method": "GET", "uri": "/product-categories", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1750168200.555053, "end": **********.190737, "duration": 0.6356840133666992, "duration_str": "636ms", "measures": [{"label": "Booting", "start": 1750168200.555053, "relative_start": 0, "end": **********.045624, "relative_end": **********.045624, "duration": 0.4905710220336914, "duration_str": "491ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.045637, "relative_start": 0.4905838966369629, "end": **********.19074, "relative_end": 3.0994415283203125e-06, "duration": 0.14510321617126465, "duration_str": "145ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 49310352, "peak_usage_str": "47MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET product-categories", "middleware": "web, verified, auth, XSS, category.access", "controller": "App\\Http\\Controllers\\ProductServiceCategoryController@getProductCategories", "namespace": null, "prefix": "", "where": [], "as": "product.categories", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FHttp%2FControllers%2FProductServiceCategoryController.php&line=203\" onclick=\"\">app/Http/Controllers/ProductServiceCategoryController.php:203-229</a>"}, "queries": {"nb_statements": 6, "nb_failed_statements": 0, "accumulated_duration": 0.03418999999999999, "accumulated_duration_str": "34.19ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 16 limit 1", "type": "query", "params": [], "bindings": ["16"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.091578, "duration": 0.02445, "duration_str": "24.45ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 71.512}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.1278598, "duration": 0.00147, "duration_str": "1.47ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 71.512, "width_percent": 4.3}, {"sql": "select `permissions`.*, `model_has_permissions`.`model_id` as `pivot_model_id`, `model_has_permissions`.`permission_id` as `pivot_permission_id`, `model_has_permissions`.`model_type` as `pivot_model_type` from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `model_has_permissions`.`model_id` = 16 and `model_has_permissions`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["16", "App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 326}, {"index": 21, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 566}], "start": **********.1521692, "duration": 0.0009699999999999999, "duration_str": "970μs", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:326", "source": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php:326", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=326", "ajax": false, "filename": "HasPermissions.php", "line": "326"}, "connection": "kdmkjkqknb", "start_percent": 75.812, "width_percent": 2.837}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (16) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 312}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 26, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}], "start": **********.1560729, "duration": 0.00079, "duration_str": "790μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 78.649, "width_percent": 2.311}, {"sql": "select `product_service_categories`.*, COUNT(pu.category_id) product_services from `product_service_categories` left join `product_services` as `pu` on `product_service_categories`.`id` = `pu`.`category_id` where `product_service_categories`.`created_by` = 15 and `product_service_categories`.`type` = 'product & service' group by `product_service_categories`.`id` order by `product_service_categories`.`id` desc", "type": "query", "params": [], "bindings": ["15", "product &amp; service"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/ProductServiceCategory.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Models\\ProductServiceCategory.php", "line": 116}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/ProductServiceCategoryController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Http\\Controllers\\ProductServiceCategoryController.php", "line": 206}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.166145, "duration": 0.00405, "duration_str": "4.05ms", "memory": 0, "memory_str": null, "filename": "ProductServiceCategory.php:116", "source": "app/Models/ProductServiceCategory.php:116", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FModels%2FProductServiceCategory.php&line=116", "ajax": false, "filename": "ProductServiceCategory.php", "line": "116"}, "connection": "kdmkjkqknb", "start_percent": 80.959, "width_percent": 11.846}, {"sql": "select count(*) as aggregate from `product_services` left join `product_service_categories` as `c` on `c`.`id` = `product_services`.`category_id` where `product_services`.`type` = 'product' and `product_services`.`created_by` = 15", "type": "query", "params": [], "bindings": ["product", "15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/ProductServiceCategoryController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Http\\Controllers\\ProductServiceCategoryController.php", "line": 209}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.17558, "duration": 0.00246, "duration_str": "2.46ms", "memory": 0, "memory_str": null, "filename": "ProductServiceCategoryController.php:209", "source": "app/Http/Controllers/ProductServiceCategoryController.php:209", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FHttp%2FControllers%2FProductServiceCategoryController.php&line=209", "ajax": false, "filename": "ProductServiceCategoryController.php", "line": "209"}, "connection": "kdmkjkqknb", "start_percent": 92.805, "width_percent": 7.195}]}, "models": {"data": {"App\\Models\\ProductServiceCategory": {"value": 26, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FModels%2FProductServiceCategory.php&line=1", "ajax": false, "filename": "ProductServiceCategory.php", "line": "?"}}, "App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 28, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 1, "messages": [{"message": "[ability => create purchase, result => true, user => 16, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1499378648 data-indent-pad=\"  \"><span class=sf-dump-note>create purchase</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"15 characters\">create purchase</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>16</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1499378648\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.163367, "xdebug_link": null}]}, "session": {"_token": "PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "16", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]"}, "request": {"path_info": "/product-categories", "status_code": "<pre class=sf-dump id=sf-dump-1352881170 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1352881170\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-1249469949 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1249469949\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-706593541 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-706593541\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1805205257 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1936 characters\">_clck=2gyfy%7C2%7Cfwu%7C0%7C1994; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clsk=1ti4td2%7C1750167218299%7C2%7C1%7Cl.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IjF1eGpkU2VhRXgwNVVnUWVTVWJqMGc9PSIsInZhbHVlIjoiclZwb3lVNThJZ3hPUnk1ODhGLzdNRmNubVd0Vk1JTXhnVEU0NjR5eU4wV0FwTnV2LzNHRVFLSCtYMk5xM1Z2RHBuL0ZRRUNua1BCZnlIQjl6eDAvU2FEY0NtSXN5NTFtdytkdGU3ZzN0S1ZPTHRraWlYOFhZM1hSSEdNQzBqeHpaaGJDeE9DRVBHbDUrTVZnSnpZZTVadzhsS1F3T1VGMXJWT2JGSk80b2RLTFZPdGpjVmtlSnZtZkpYVWxDcExLUVN6OXJua1JyalpmZWdCQ1lvWFJDT2huT0hwR0V6d3NaQ2tJY2JCZjNhTTlkUTNQRHdpcFhaVS8vUm9HdkZmeUJvTGs0Um8zZnU2WHRJQXJtQmxCbjhGRnhUMmNBMTltaFVuWVh2WkdET2VNOFhxYktMQVkrM0NrV3p2VjVLaEJ1Ny82bkgyRURySlFlOVU1MldYU2JWZTNCRUs5d3B1bURSbU5hWUF4K0hMeE5MRzRpMzZZQTRIaUFvRHh4S05NSktHaTVkL2dmZjJLMWhHa2JaK0FQaVJSNVNiZ0s4MWtqb1pVcGQvV2pGZ2RCMEFpVWZ2Vk5ITWZRLzRNVGhJdlNCbnBTd2VNZTYzTHM1MCsydmxMYUNjS3lXSk9zYVVlL3ovTVZmQjkyOHovenNZNkIzVXU4R3l0RkFsV29WNmQiLCJtYWMiOiJkZDljOTc5MzZiZWIwNWNmM2I5ODVjMTE2NTUxMWUwNWIxZDhiNzEwOTAzM2QzMjFhZDU0ZDVkNWZmODRhMmI0IiwidGFnIjoiIn0%3D; test_session=eyJpdiI6InRqSFR2VW9aM0QrMGh0bkNjVVNDZnc9PSIsInZhbHVlIjoiL3AzWkRmKzdzcXEwL05lTEFNZHVxdGhrOUdPaDZ4TzVzaDdDWEZ2aXVmREFzMFcxYUhvZWg1a2JNNVVoSkhUMWlmdjhCS3NZUE1GY1ZxTzM0bGNCUGRHSGhHdnM3ZjZMcWFkekFOcU1NVDQxNzNlN0p2VVdBS1FzbXJCUnZVWEl1eEpoQStwNXFKN1hwc3QrM1FWckNKUHhwbm9aREdhdEFPS0kvOVdNQzJHcFBQRlM4VTdPNUYzejlidk1HVzdHdzIwTGgzSDlTL3BiVUw1dWtKMHlzaFRsbmhFcGxaRzg3ZjVDV1V3YUM5dkxWc1FMWjRHQlNhTGQ2cUN2NE5kTWthaE82alNnR1MvM21sOHNvVzBDaW1GOUtwSmJKUU55aEN4YjRMdC9Nd04rZkpwSjVhMGRUNWF2QUI5Q09saEZOY1JhSDZyQ2w5eVJlSnVGcWRDL05SMUVRb3V3ZW9EelUvRW9yVFAya2luOUJ4QmlkY01vVHRrdGErUmRaNGtIMnRYSklMdG9pWHEzbHhyUFlMSS93bmJ3RnV1UUdkOXloSEdiODZjQ0tTUU16dGRVZGErb2F2L09hWnEvQis0MnVjS3dCTjJBaVdlYjJtc1ZtelM4QXg5YmIzSm9wTWdWMVVKS0F6OE04SUxtTThGUlZIa0VQMjVrZ1hNRnd6RkYiLCJtYWMiOiIwZWMyM2Y4ZGEwNTE0NDk2YWMzOWI2MWUyYjM2YTQ3ZDhlOTEzYWUxNzM5OTEyZThhNTFiYTBiOTI2YzM4MDQ2IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1805205257\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-24003224 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">VloaUQCBjpDKrel7nHeAzB1sTLdTMwteSos1MmAN</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-24003224\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 17 Jun 2025 13:50:01 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IlR2SFFSd3Z6RjFKSjNuTjZ2Z21hcXc9PSIsInZhbHVlIjoiZEVvOG13bkVYNE00ZHMvMkhsY0p6aHZPRmJBak0yYURKc1puNnJVMEk3TWYvNHl5TmlpbHVab2lIcHJkRC9aSXpOQk16OGgvdHpWS0gzTm1mWU5DcTU2UlBXRXhlMlF2S2trSk83b0xjVGJRdUtBYnAzd2QwM1p4WGJuRlB5MGVzTW5IdU5iMUlrZThIcmpWZFRDM2xXZnRiYWtsWUZTSEdlWWtZZmt1VjhadkFGekN1QWJsSmtCRGJud0MycGlpT3dIRUl0WjgxbWJ2R3hnWEVSeEdvclY3K09CQmtTbDBsRkdWN3BQQ1F6YXBlYldoNEdoalduQzArZ09EVjYxT1VETXFvVVhFTllPTFVkTmJwU1hlellIbGcwNHRIeXJBSkp5NVZuN2xpR25QTm5FUjUvRjhmcWJEUHJSZzNKa3dVRUtoWVlZRXRhTXgzbTAxNjZ1Z1dDWlVLbnRwbjF6disxbkNTdG81Umxib08rQVgrdDFUbyt1VlppSE85aTRoLzVMNVpwdjMxRVJoRGZRcWZPcUdScGlBb1EwdDB3UnZKTytJWkxxVkJxVWZMU200N3VweVF3aGlLWjUzWnVmaWVIUU9ZOElDY21KbmZvRHo2WGY4cmQwSitQWlRDSVRYRGl0a2dueGsvRjlCRGYwY0JWUkxnZjRTenVUQnVjQVAiLCJtYWMiOiJiNWU3YzViMzZlMGYyOTUyZmJmMmM1Y2FmZDdmZmEwN2ZhNDNiMWNlMjEyMDIzN2U0ZDEzN2QyZGEyMzk5NzJiIiwidGFnIjoiIn0%3D; expires=Tue, 17 Jun 2025 15:50:01 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6ImlZV2FBZUNKbmJ0MWMvVXZvS2lUQ0E9PSIsInZhbHVlIjoieTJvOTlpbWFkNlBhOFp3eGRFUll2S2FrVWtOUXpqK1VqcStwSTBOa2c0ay8zdlVaRU01QkszelJtZEdwOWVBeU5TMnE0NXVRa3FWcm02VW5hU1lpdmkwU2FCbkJsMXRCMkFJZ0tSSEV2UWNNZERhdUtleGxQWEtVenJacmNoKzE2SUkxR05ZZXYvRlRlaVNKRUNMRldKMUJVZUNpeTh3eXpHenBJVjRicmJMZ0tuaEgzajIyZTZCU3QvUnpIUDYyVFZwQWU0aGdtYWNyVHV2OWFvY0o1VzliS01YWmJNUnZyRXRPSE9SL1IrZHNDd1BsbWYxS3dCT2FSM1hvUXUzejdTdWxERnYyVWNubERnMVAyNldqV201WEJaRGRNU0toZlNLaUdhTWlaL2hHb0F0NjBXVWtjcmVKQU5IaHltcU0wWUdBd3lMTFBqMVhGU2ZRSUlxek91RXo5alRHemF6dzAxU3Qvb09jQnBzYmlhS2t1dm1NZVhvVmwzTGZnc0doVDRlcG5XWlU0WnJLY1RmUzZvMUJwZVlQM1BqRTJaMU9aQUxUTFowTU1uSDV0R0hWWlB4dEpFaHZzdW5OUjY4eW9uZ3FJZWR6cU5qeGtvSmwwZW5hUTNRcWJPZHMzdE12ZnNXR2E1S2JSa0FndzhOMFEzWDJoRkFNa1VJS0hGeTAiLCJtYWMiOiI2Yjc3NTNkYjdkM2U3NGJiMTRiYzk1NjRiNThkZTZmODg5ZGYwYjAyNzA3N2M0ODIwNDM5MGY1MTQ5MzlmM2YyIiwidGFnIjoiIn0%3D; expires=Tue, 17 Jun 2025 15:50:01 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IlR2SFFSd3Z6RjFKSjNuTjZ2Z21hcXc9PSIsInZhbHVlIjoiZEVvOG13bkVYNE00ZHMvMkhsY0p6aHZPRmJBak0yYURKc1puNnJVMEk3TWYvNHl5TmlpbHVab2lIcHJkRC9aSXpOQk16OGgvdHpWS0gzTm1mWU5DcTU2UlBXRXhlMlF2S2trSk83b0xjVGJRdUtBYnAzd2QwM1p4WGJuRlB5MGVzTW5IdU5iMUlrZThIcmpWZFRDM2xXZnRiYWtsWUZTSEdlWWtZZmt1VjhadkFGekN1QWJsSmtCRGJud0MycGlpT3dIRUl0WjgxbWJ2R3hnWEVSeEdvclY3K09CQmtTbDBsRkdWN3BQQ1F6YXBlYldoNEdoalduQzArZ09EVjYxT1VETXFvVVhFTllPTFVkTmJwU1hlellIbGcwNHRIeXJBSkp5NVZuN2xpR25QTm5FUjUvRjhmcWJEUHJSZzNKa3dVRUtoWVlZRXRhTXgzbTAxNjZ1Z1dDWlVLbnRwbjF6disxbkNTdG81Umxib08rQVgrdDFUbyt1VlppSE85aTRoLzVMNVpwdjMxRVJoRGZRcWZPcUdScGlBb1EwdDB3UnZKTytJWkxxVkJxVWZMU200N3VweVF3aGlLWjUzWnVmaWVIUU9ZOElDY21KbmZvRHo2WGY4cmQwSitQWlRDSVRYRGl0a2dueGsvRjlCRGYwY0JWUkxnZjRTenVUQnVjQVAiLCJtYWMiOiJiNWU3YzViMzZlMGYyOTUyZmJmMmM1Y2FmZDdmZmEwN2ZhNDNiMWNlMjEyMDIzN2U0ZDEzN2QyZGEyMzk5NzJiIiwidGFnIjoiIn0%3D; expires=Tue, 17-Jun-2025 15:50:01 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6ImlZV2FBZUNKbmJ0MWMvVXZvS2lUQ0E9PSIsInZhbHVlIjoieTJvOTlpbWFkNlBhOFp3eGRFUll2S2FrVWtOUXpqK1VqcStwSTBOa2c0ay8zdlVaRU01QkszelJtZEdwOWVBeU5TMnE0NXVRa3FWcm02VW5hU1lpdmkwU2FCbkJsMXRCMkFJZ0tSSEV2UWNNZERhdUtleGxQWEtVenJacmNoKzE2SUkxR05ZZXYvRlRlaVNKRUNMRldKMUJVZUNpeTh3eXpHenBJVjRicmJMZ0tuaEgzajIyZTZCU3QvUnpIUDYyVFZwQWU0aGdtYWNyVHV2OWFvY0o1VzliS01YWmJNUnZyRXRPSE9SL1IrZHNDd1BsbWYxS3dCT2FSM1hvUXUzejdTdWxERnYyVWNubERnMVAyNldqV201WEJaRGRNU0toZlNLaUdhTWlaL2hHb0F0NjBXVWtjcmVKQU5IaHltcU0wWUdBd3lMTFBqMVhGU2ZRSUlxek91RXo5alRHemF6dzAxU3Qvb09jQnBzYmlhS2t1dm1NZVhvVmwzTGZnc0doVDRlcG5XWlU0WnJLY1RmUzZvMUJwZVlQM1BqRTJaMU9aQUxUTFowTU1uSDV0R0hWWlB4dEpFaHZzdW5OUjY4eW9uZ3FJZWR6cU5qeGtvSmwwZW5hUTNRcWJPZHMzdE12ZnNXR2E1S2JSa0FndzhOMFEzWDJoRkFNa1VJS0hGeTAiLCJtYWMiOiI2Yjc3NTNkYjdkM2U3NGJiMTRiYzk1NjRiNThkZTZmODg5ZGYwYjAyNzA3N2M0ODIwNDM5MGY1MTQ5MzlmM2YyIiwidGFnIjoiIn0%3D; expires=Tue, 17-Jun-2025 15:50:01 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-169931356 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>16</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-169931356\", {\"maxDepth\":0})</script>\n"}}