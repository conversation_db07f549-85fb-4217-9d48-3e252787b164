{"__meta": {"id": "X7b49ad96b66d2d91b89164fcb188b979", "datetime": "2025-06-17 13:34:41", "utime": **********.818259, "method": "POST", "uri": "/warehouse-empty-cart", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.140537, "end": **********.818286, "duration": 0.6777489185333252, "duration_str": "678ms", "measures": [{"label": "Booting", "start": **********.140537, "relative_start": 0, "end": **********.719345, "relative_end": **********.719345, "duration": 0.578808069229126, "duration_str": "579ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.71936, "relative_start": 0.5788230895996094, "end": **********.818289, "relative_end": 3.0994415283203125e-06, "duration": 0.09892892837524414, "duration_str": "98.93ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 46326408, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST warehouse-empty-cart", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\ProductServiceController@warehouseemptyCart", "namespace": null, "prefix": "", "where": [], "as": "warehouse-empty-cart", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FHttp%2FControllers%2FProductServiceController.php&line=1663\" onclick=\"\">app/Http/Controllers/ProductServiceController.php:1663-1673</a>"}, "queries": {"nb_statements": 2, "nb_failed_statements": 0, "accumulated_duration": 0.01937, "accumulated_duration_str": "19.37ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 16 limit 1", "type": "query", "params": [], "bindings": ["16"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.769263, "duration": 0.01825, "duration_str": "18.25ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 94.218}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.80381, "duration": 0.0011200000000000001, "duration_str": "1.12ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 94.218, "width_percent": 5.782}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "16", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]"}, "request": {"path_info": "/warehouse-empty-cart", "status_code": "<pre class=sf-dump id=sf-dump-1251548306 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1251548306\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-1345476781 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1345476781\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1855613330 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>session_key</span>\" => \"<span class=sf-dump-str title=\"3 characters\">pos</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1855613330\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-125871777 data-indent-pad=\"  \"><span class=sf-dump-note>array:19</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">15</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1936 characters\">_clck=2gyfy%7C2%7Cfwu%7C0%7C1994; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clsk=1ti4td2%7C1750167218299%7C2%7C1%7Cl.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6ImUrbU9iWDlabDVsbzl1S2pZV1c1cmc9PSIsInZhbHVlIjoiSDdiall5ZVFGSVVST3huKzZublgvMi9YT25CWjZHV0lrTzlDRCtlWjBFTFFkVDFtTHJ4S01Kc2NCWmNpUE1SZGl1bGdIM3pNUmowQ2tScDN6K1l3d1B3ZDdxbHQwM3FTejlFV0ZOSXZWK00xcTRVSUJZZERsYUJwWWlSQUV3ZUwrN0xZTXQwSjQzcXlNa3dmT0FKVTYvUGR2WVBkeTdMYjlNMnZZekF3Wkg3bDZaRmpqaUV6UFdlRjc5MzdOcFd2Ryt2WDBBVnl5MWpHUmdsRzIrM1VLdEtVY244Zk9hS2ZsRnJ0bkV1SFNqdDJKTTVlMmJXMnA1Yjd3WC9aNDAzcUgxcXg3bWEzamZUelZFTW1JZWFVbEdFUnR3ZGs3cVVTbkpldFRvRkNFNjZsUFA1OVpLM0QvRWE4eXQ4V1Bsd285ZzhaczR3dlRZTkIwcTJlYXJjcTBKbFJRYUN1QndFZHlIRnBHT3Mwam95UjNkMzhpV0RSZnk4aEQwV2tIWW5URnRFc3RyMXJKa0tHK0l1NFE5d2FtSzcyT2gxVTJUU1ViYzRrQStKZHBGTzhWcTZBVTlzOUVEbFVsODJHOC8xSXZweXNDNkdhVGNXdXo1OU0wVGc2NUM1L2VUY2xRS0lEdWNUWXQ2Yy96QkZabnFtMXNyUmp3NUtnc3JaRDhLYjAiLCJtYWMiOiJhNGQwMDZjZGU0ODlkNDU0N2VkZjhhYmIyODhmNTRkNzQ1Y2E0MTg5MDgzOGI4Yjk1Y2M4MjY4ODYxNjIxNDY2IiwidGFnIjoiIn0%3D; test_session=eyJpdiI6ImVXVzFWWDBNQ29lYUZnZGgvWXJTeUE9PSIsInZhbHVlIjoiRzFoOXVlcTNiZ1E4WklmRGlDNENTVWFJM1VBY2srOU5ieWk5blNLNWtiRVh0bmgyUnl1ZGxtMng4WEdoTTd3andpeWZTY01XNmNoZitWd2FqQU9MN3ZVZFpwT0l4cjR6bVkxV3hlMmhQRUtHWEVHVFhEYThhSjUzWFZDcU00OStMeVc0S2dMNC9kYzRCUm5naWtTcWw0N1BMVWEvWElSMWhCN2VoazA2Z2xkQVc0K1BzM0tmekJhb1B5ei83andQd09yNk1GSTR5aXkwNEhUVWtvMWd1Vlk2d0ZDTEsvSUJOYkNPY3QxZGkrMWQzVWN3Z09meUg2bno0WlVISlBhYUlxN1hmaGE2VVdRWmNoWjVxUWtFV2orRmlnR0dWQldxQ3FmZlpXMzhvbi9ELzFRRDY2bWxwK2xiRzY4T28yS2xkcytTOFJBdnBBVEdacWZsRjVBYWNFVkVPejVZWjRyZkhOMjNoekJYdFU3ejlQbXdzWGRUd01NeXhVbTNaL2RQRlE5QVFhb0RWOHZORUNMa3BSZ2Uzblc3NFBzTkJCWE4rWmgyaVlSTi9aUElqS1hWNVVyVnF4dTFhNklOS0FWS01aR3VsY2hSV3ExNldCdURidDNOdmJ3QndyclRhdlVxNmxFMFhCTTJneWFFakd3bzhHbUJGZGpZZkI3UlIwc0giLCJtYWMiOiIyMDg5MjIwOWUwYWVjYmFmNGNiN2RhZTkzMDg2Y2IzNWI2YmEzMjA5ZGE1MTEwMjkzNjZkNDBlNzcyYTc1OTA2IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-125871777\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1505580577 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">VloaUQCBjpDKrel7nHeAzB1sTLdTMwteSos1MmAN</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1505580577\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1806527833 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 17 Jun 2025 13:34:41 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IlA5am1yZDA3ZUV3UE50Yld6TUZoMkE9PSIsInZhbHVlIjoicENRUTJuNVBoRVhrWXhZeUp1VjlXU0ZXQXFRUXpuQUNCMU5hR3ZOSlR4RjhrRGFyWEZkNUdkakc2QytHdER3U2JZRHN4TExqNHIzUDFGT2RpNXlONjd6Z2dPVEU5aDVyN2dkbGo3YjBKcStBMjluUHpwcEo1RXJkYXM2a1psSUlMZk9RMkNISjRQOUJVTGFaVDZmVXpXUVRqalpQZFpvYmNaQ3BYZTgyRkI1U2QxdFRpZXRnWUc3RDRnYnNHVmZBeFV3M1NZZXg5eHFyT3p6cjB2VVNSb05NSXlveHczdnRtbjNtSXpPUmo4VGN2Q1dSbkM0MS9rcHJFbVRUcjBwTlpVLzVsenlRVGJJUCtlV3JxbkxOTzIwU0NRRVp6ay84UnhTRmtjdDlJRTVuSyszT01TR3F5bXNISXEybUJ0UFNSTFRtdkxCdFpHcEpQdkUxakthNEhYNVVLRnkyQ3NhUUJmZ0dOQ3dXUjhGSGE3aU1IbmxWNTAyM1NlR3RrYU85SnA5eVJJSmxSRi8rcWc0ck14UlJLMGs2YkV0N0FsQWZJM09WSitPdlA2UDhvODd5RUQ2Q0tudjg0S2pGTWtyQ2NjY0lRbHR0emxPQTYrSmd3OEo3eVNGNHZUNXBBc3pPdlFTN1VBVVJwVkxkZWxvYXVtMTh1QWlSWmJnMXVLUTIiLCJtYWMiOiJkODE2ZDUxMzBjOGI3NDcwMzk0YzQ5Zjg5YTg3YzY3ODYzNzU5Mjg5ZDBiMjcyNmNmMWZkYzlhZjY4NjVmYTc0IiwidGFnIjoiIn0%3D; expires=Tue, 17 Jun 2025 15:34:41 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6IlhGVDZya0hGR2FnaytRS3BjSmtHS3c9PSIsInZhbHVlIjoiV0JvOHQzYnR2Vlk0Z3hwTDNHLys2aGhNNDc1L3AxY3JZdzdtanhtazVtbTcxWmY3WkZ0OXRwZGd5cThpU2xYVXpobE1ReHV5c29LTVJRVUpEejdIeTZsOFp4b2hvM09aY2JTV2RFWjI4MlhJZk9XbjQ0WCsvWjQ1NWJDWnVDNHFMcnQ1Qm1KR2x5TjBtY0lGQWdsODdFcEwzNjVzT0plZWVIZ2hBUjl2djhCVjJacGZ1UGZaWVpJcGs2TWtMREdodk5uRUtTeGlpZzhJODAxUkZjRkxpbHBXdGxJUnFjQUtzMFFjckxCMXkvakNSOFFNWGpzNjd5b2ZXUFN0WFpHUWJPYUZzRHoxYjlwdlVoQks2TUNCRGpBeHlBS3owa01seEUxZU9tT2w5RXlWTFlUOVlEaXlhdVVvc2hNeGFPb21MSCtkOFkrd0dBQitaeTJFa3FFbnBnWGVsamZ1b1VyWGV0aHd3cytuaXU5dXdzcEprRmhDejNNMkZOd0VuemZ4anY1dkgvS200cXl4dlkvMCtiTzBNbVMyeWU4dktmQm5kb0cvTmo4Mm4yVzlwN0hXdkk2SnhFQ1ZkM2RzU3RMbyt1akdjY3dwT0hhTDZDUTJjWWR0NTZEM09UWmtnc3YrNXVtSzB4dnQ1UFl0RnJ0NmxIaWtWdDI5QVVoKzM4cEMiLCJtYWMiOiJjMWIyOTZhMmQzZTIzYjBlMjgzOWY4MjUxMzg0YjZjNWJhNDU1ZWRjODg4ZWJmMDA1MjkzYTc1NzBiODRkN2ZlIiwidGFnIjoiIn0%3D; expires=Tue, 17 Jun 2025 15:34:41 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IlA5am1yZDA3ZUV3UE50Yld6TUZoMkE9PSIsInZhbHVlIjoicENRUTJuNVBoRVhrWXhZeUp1VjlXU0ZXQXFRUXpuQUNCMU5hR3ZOSlR4RjhrRGFyWEZkNUdkakc2QytHdER3U2JZRHN4TExqNHIzUDFGT2RpNXlONjd6Z2dPVEU5aDVyN2dkbGo3YjBKcStBMjluUHpwcEo1RXJkYXM2a1psSUlMZk9RMkNISjRQOUJVTGFaVDZmVXpXUVRqalpQZFpvYmNaQ3BYZTgyRkI1U2QxdFRpZXRnWUc3RDRnYnNHVmZBeFV3M1NZZXg5eHFyT3p6cjB2VVNSb05NSXlveHczdnRtbjNtSXpPUmo4VGN2Q1dSbkM0MS9rcHJFbVRUcjBwTlpVLzVsenlRVGJJUCtlV3JxbkxOTzIwU0NRRVp6ay84UnhTRmtjdDlJRTVuSyszT01TR3F5bXNISXEybUJ0UFNSTFRtdkxCdFpHcEpQdkUxakthNEhYNVVLRnkyQ3NhUUJmZ0dOQ3dXUjhGSGE3aU1IbmxWNTAyM1NlR3RrYU85SnA5eVJJSmxSRi8rcWc0ck14UlJLMGs2YkV0N0FsQWZJM09WSitPdlA2UDhvODd5RUQ2Q0tudjg0S2pGTWtyQ2NjY0lRbHR0emxPQTYrSmd3OEo3eVNGNHZUNXBBc3pPdlFTN1VBVVJwVkxkZWxvYXVtMTh1QWlSWmJnMXVLUTIiLCJtYWMiOiJkODE2ZDUxMzBjOGI3NDcwMzk0YzQ5Zjg5YTg3YzY3ODYzNzU5Mjg5ZDBiMjcyNmNmMWZkYzlhZjY4NjVmYTc0IiwidGFnIjoiIn0%3D; expires=Tue, 17-Jun-2025 15:34:41 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6IlhGVDZya0hGR2FnaytRS3BjSmtHS3c9PSIsInZhbHVlIjoiV0JvOHQzYnR2Vlk0Z3hwTDNHLys2aGhNNDc1L3AxY3JZdzdtanhtazVtbTcxWmY3WkZ0OXRwZGd5cThpU2xYVXpobE1ReHV5c29LTVJRVUpEejdIeTZsOFp4b2hvM09aY2JTV2RFWjI4MlhJZk9XbjQ0WCsvWjQ1NWJDWnVDNHFMcnQ1Qm1KR2x5TjBtY0lGQWdsODdFcEwzNjVzT0plZWVIZ2hBUjl2djhCVjJacGZ1UGZaWVpJcGs2TWtMREdodk5uRUtTeGlpZzhJODAxUkZjRkxpbHBXdGxJUnFjQUtzMFFjckxCMXkvakNSOFFNWGpzNjd5b2ZXUFN0WFpHUWJPYUZzRHoxYjlwdlVoQks2TUNCRGpBeHlBS3owa01seEUxZU9tT2w5RXlWTFlUOVlEaXlhdVVvc2hNeGFPb21MSCtkOFkrd0dBQitaeTJFa3FFbnBnWGVsamZ1b1VyWGV0aHd3cytuaXU5dXdzcEprRmhDejNNMkZOd0VuemZ4anY1dkgvS200cXl4dlkvMCtiTzBNbVMyeWU4dktmQm5kb0cvTmo4Mm4yVzlwN0hXdkk2SnhFQ1ZkM2RzU3RMbyt1akdjY3dwT0hhTDZDUTJjWWR0NTZEM09UWmtnc3YrNXVtSzB4dnQ1UFl0RnJ0NmxIaWtWdDI5QVVoKzM4cEMiLCJtYWMiOiJjMWIyOTZhMmQzZTIzYjBlMjgzOWY4MjUxMzg0YjZjNWJhNDU1ZWRjODg4ZWJmMDA1MjkzYTc1NzBiODRkN2ZlIiwidGFnIjoiIn0%3D; expires=Tue, 17-Jun-2025 15:34:41 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1806527833\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1156072874 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>16</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1156072874\", {\"maxDepth\":0})</script>\n"}}