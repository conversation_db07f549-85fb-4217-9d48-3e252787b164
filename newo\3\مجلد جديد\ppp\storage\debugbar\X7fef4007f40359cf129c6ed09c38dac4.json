{"__meta": {"id": "X7fef4007f40359cf129c6ed09c38dac4", "datetime": "2025-06-17 13:55:10", "utime": **********.760933, "method": "GET", "uri": "/customer/check/warehouse?customer_id=10&warehouse_id=8", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.106459, "end": **********.760952, "duration": 0.6544930934906006, "duration_str": "654ms", "measures": [{"label": "Booting", "start": **********.106459, "relative_start": 0, "end": **********.64441, "relative_end": **********.64441, "duration": 0.5379509925842285, "duration_str": "538ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.644434, "relative_start": 0.5379750728607178, "end": **********.760955, "relative_end": 3.0994415283203125e-06, "duration": 0.11652112007141113, "duration_str": "117ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 46219624, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET customer/check/warehouse", "middleware": "web, verified, auth, XSS, revalidate", "controller": "App\\Http\\Controllers\\CustomerController@checkWarehouse", "namespace": null, "prefix": "", "where": [], "as": "customer.check.warehouse", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FHttp%2FControllers%2FCustomerController.php&line=383\" onclick=\"\">app/Http/Controllers/CustomerController.php:383-397</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.03217999999999999, "accumulated_duration_str": "32.18ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 16 limit 1", "type": "query", "params": [], "bindings": ["16"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.695431, "duration": 0.030449999999999998, "duration_str": "30.45ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 94.624}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.741223, "duration": 0.00115, "duration_str": "1.15ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 94.624, "width_percent": 3.574}, {"sql": "select * from `customers` where `customers`.`id` = '10' limit 1", "type": "query", "params": [], "bindings": ["10"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/CustomerController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Http\\Controllers\\CustomerController.php", "line": 388}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.749018, "duration": 0.00058, "duration_str": "580μs", "memory": 0, "memory_str": null, "filename": "CustomerController.php:388", "source": "app/Http/Controllers/CustomerController.php:388", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FHttp%2FControllers%2FCustomerController.php&line=388", "ajax": false, "filename": "CustomerController.php", "line": "388"}, "connection": "kdmkjkqknb", "start_percent": 98.198, "width_percent": 1.802}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\Customer": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FModels%2FCustomer.php&line=1", "ajax": false, "filename": "Customer.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "16", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]"}, "request": {"path_info": "/customer/check/warehouse", "status_code": "<pre class=sf-dump id=sf-dump-459319532 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-459319532\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-1519857269 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>customer_id</span>\" => \"<span class=sf-dump-str title=\"2 characters\">10</span>\"\n  \"<span class=sf-dump-key>warehouse_id</span>\" => \"<span class=sf-dump-str>8</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1519857269\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1665971272 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1665971272\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1936 characters\">_clck=2gyfy%7C2%7Cfwu%7C0%7C1994; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clsk=1ti4td2%7C1750168503247%7C8%7C1%7Cl.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6ImtBK2FxNEFMUmdad2dMTHF3M2pSR1E9PSIsInZhbHVlIjoiK0RQRW4wMklFaTlTVE8yckRUTyt3eC9WSDV0c3RVYThERDFzL3llcVJ0c3ViT2tPUFcxdm04ektDVnhzbExQSkozZVZlRHB3VEJsVmtEOHJ1WE1OZFcxSENSMkR6SkZYSy9IaFQ0QmZ0blNtRlVhanVBZ2dKTERWYTZMcTNjaXhwSFZjeXBEcnkxbFJmTVpyZ0VOMmlKbEUxbTJ1UkhBR3ZzMEZ6TEVwbzNLL1FiOEsrU1BtdG9Qb0JRZlduaUdxTXFDYXYrRVQyS1p6VGUrdXN1WWpIWU9KbGtqaTRsRERKSXNNeUZmNU4vYjVtUUNEeFdnRC8xZUlub2tJTkFuQnFFNHRSL3EyUWNDZVBONkQ2ZjZlYTBGYXFaYUdpQ3lqa0Y3eXg1eExsSDhjcHZMV21zNk9IZ0tHOXFERkw3ZWo5OW4rT2NhZk5qbEpPRWxRcHZTSVdGWFpaQU5iRUpmdzVjcEtqb0xoem82emFaTGt0TEVmelBONjRMeEdPRlM0blpUV0loY2p5cHhnSUNpNGxqVlJxWVFoVzE1aWJYOUtwS3MyOU8zV2pJaDN0T2VZTm5taHV3L3JBdzNEY2pXV3Zoc1h2SGJlR3NNL2czMzVnb3ZiNElsTmZWSEhLWlF1NGNyODNRdUpxM1FzVE10dng5OGpzZEpJWWdJcXRLVU0iLCJtYWMiOiIzZjZjNjY5NzRjMjQ1MDMxNDBjOTBmMjBlMDZhNjk3OTQwOGNmYWRiMjE4NDhmYzViMmY1MThiNjdiYjJjMzQzIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6InRLQmgzR2ptYmUyRkNTL0xxZ0dpT1E9PSIsInZhbHVlIjoiU2ZTeWJoeGV1L3JYRldSamJISDc2UjZnNmtxUWhrbkdON29ISEdKczhXTTNCNWUrZzQvVEJicGZyeGFkZGZ2WU0rTzlVbjdGUkpiTFdZT2ZITGxiazhGMWdIVWtySXE1bnF5QWVVYWxtcHNIR0RseURqUkcrNE80MkZJRXhzc1YwSlRCdGdBZTYvUk9VTi8wdHVBejRSV3owalo5WTJFNS84ZFNQK3dPRjNiWXpOdWZhWmVKWW9SNUwya1dOMVV0Nys4M1hoYlRDT1EyTDBsU0NBQ3pQSDBtb21qTllWZS8veURJS2w3bzlVdFFCTzc0eVcxTjZKMWRRT3NPZjFUUXRsSVAzdC9mNlFGdWdKTGlPMitFN0pxVG9sY0pNdXVSQVNCK0RtZ3Y2VXBhcllqcHFyckVZWG1INGxRQWJXbCs0UlpiSjZXbFRwQm4rMmR2WWtvUUgvU0xrK2cvbXNzTUgzTVhrc1U1RzNhUXdFdDNKWGVjTFJlZTJTeExiK1NZS3pkNTh0V2g1ckIwMXh4U0FPaXJVWEZZM2xEQXFmTTNoS005NjM0d3Z2S000Ny9MM1ZxbU5VbjQvNkVOdWErV2FiTHcwSFI4ZW9reHAyOFE0dUpVMFJTZk5rU05NK0MrVmxNNVU3Vll4eFZDM1BIeTI2RVJlNHlUVjdkWXgwK00iLCJtYWMiOiI5OTVkOTNmN2RlZWQ2ZjA1ZDM3ODQ2NzUyMGQwZTQ4MWNiZTc2MzQzYzQ3M2IwNGNmYzRiNzhlNTAyNzdlZjc3IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1509316676 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">VloaUQCBjpDKrel7nHeAzB1sTLdTMwteSos1MmAN</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1509316676\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-618103712 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 17 Jun 2025 13:55:10 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-methods</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">POST, GET, OPTIONS, PUT, DELETE</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-headers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"80 characters\">Content-Type, Accept, Authorization, X-Requested-With, Application, X-CSRF-TOKEN</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Ii9iSFhISnFKR2RkL2Q5VkFxaEoxc0E9PSIsInZhbHVlIjoidG11TjNrWTExeVpZbHQyVWJQcG1lYVZZKzNBbCtOVG5WYXVjcURteG9TUDFYclRqSFFhVXROVFhOTzdIcEtKcmJ3SFZHaWI0aVJOVHpwMlpyT0RtVEowdG5sZTdUUERFMW5FL1NRcU1DM2oxSUhlUkF4a3RpeElQRzFVZFk5TDdUSzVRVXBibjJnWlNCUFFBdC9FcXJBdU5ta1R0NFdDRGR6STRKUW80QkN0L0xuaXMzRTd3ZnV1SzVtYVVlZWVWdDJ1emRmMC9nanFnTS9ObHFQZldiNWpTb0dSRXZHZEx0aGxoSm5DOXUrOC9XbjI0amU1aTYwaTlLeExvM293U0RqdHpaNVg3NGZ4UVJoYUx2djNMNll3Z0swSkdpaDlBTmVTQjRTS1IreHo4Zjd6cmMybzZnZWppVE1yNE43Sy9tNTgrWnh6TFNSWnc5MytFNDhaZy9kRzlwcWlmdGJpK1dBay85bkk4ODdHNC9BV2Rxd3lIN0V2cnI0SGROZTNDSENqNWFpU3VwR0hFQzNUMmJGK3B5dzN1TGkvY1FsblhKTk5rYXJuWitPOHdZdU5VZ0VWRlZNTmR4bWd2MEpNN3k2cmhvVjFTUFNKYnFhdE8zTmZad01qdGpFY3pNZGFJdXRqd29mcDVad0VLWmFWNVBGMnE0OFdiNFI0bk00TXMiLCJtYWMiOiI4MWI1YjhjNDI1YTVkM2NlNzk0YWFlZTc1NTZiODRhN2I2NDQzZmU0NGU3YzcxNTdjNjlkZDNlOTM5YzYwMWQyIiwidGFnIjoiIn0%3D; expires=Tue, 17 Jun 2025 15:55:10 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6InY2dnE3N2Qvait1N2oxRm9uckdCY1E9PSIsInZhbHVlIjoiR3p4TTIrOGQ4RlAxT2FodDEyR01mU2JKajg3MlFXRXpCcDVPYlZYVGtaS2V0eEt4NTRUTi9ETjY5QTRFWEhOSkxRRXpTd04zbjJuNnZ1SngwdnRWakYydUR3czVVcS9VVXZYa0dqRVRrMmNVZ2pqM2dxVXpvQ1gxdWt4b1NQZnAyTWt3aTRFOXVFd0tCRGVEenVwVFVTbFdsYk4rMklkRGhlTEdVcU1vamZFZGszOHBxKzF4ZUxqMDBFazVUdTVTOGhjTzZmZDFHdzNSS0FFMnp6ZVpuYnp0NzloS0ZhV05YRUxHUk9rNnBwZm5sMnpwZlM3VUFrcTdjRUVzSUxMeUU0WXhqRS9mTXdPNVBjcXllWVZTblJuM291S05xRHYvYUNTQUF0eWFDUk1mWlp2a1BieEVITnd1SlEydlJHcmdsQXgrMHhIQ2R5REZCODJqUFIvZlRlbTFJdWNMZmtSNXI4T2FqemhhSDF0YjVyZUlkeHZYaEJZZzJKdTZDWTZWMzkxK0NPT0J3bDAwZE9hTVFPMTNxZVlLWGRoaFpTOHBYeWhUSUZmenFqYlJsVi9pUEM0V0Y4SHhQNjZDd2E2czZCZlRBcFNwNXdDdHcraERsbXhyM3hIMW5Ma1hmdjM0aDZBQVJZc0lJYngwMUxjYlZocklDeWswVm5HQlRtRkQiLCJtYWMiOiIzYzFiNjgyMTg1ZDU2NTgwMDRlZTVjN2Y1MTExMGU4NzkyM2I2ZGZhZjZmYjk3NTdlNTRhZDMyYzk1Zjc2ZTY2IiwidGFnIjoiIn0%3D; expires=Tue, 17 Jun 2025 15:55:10 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Ii9iSFhISnFKR2RkL2Q5VkFxaEoxc0E9PSIsInZhbHVlIjoidG11TjNrWTExeVpZbHQyVWJQcG1lYVZZKzNBbCtOVG5WYXVjcURteG9TUDFYclRqSFFhVXROVFhOTzdIcEtKcmJ3SFZHaWI0aVJOVHpwMlpyT0RtVEowdG5sZTdUUERFMW5FL1NRcU1DM2oxSUhlUkF4a3RpeElQRzFVZFk5TDdUSzVRVXBibjJnWlNCUFFBdC9FcXJBdU5ta1R0NFdDRGR6STRKUW80QkN0L0xuaXMzRTd3ZnV1SzVtYVVlZWVWdDJ1emRmMC9nanFnTS9ObHFQZldiNWpTb0dSRXZHZEx0aGxoSm5DOXUrOC9XbjI0amU1aTYwaTlLeExvM293U0RqdHpaNVg3NGZ4UVJoYUx2djNMNll3Z0swSkdpaDlBTmVTQjRTS1IreHo4Zjd6cmMybzZnZWppVE1yNE43Sy9tNTgrWnh6TFNSWnc5MytFNDhaZy9kRzlwcWlmdGJpK1dBay85bkk4ODdHNC9BV2Rxd3lIN0V2cnI0SGROZTNDSENqNWFpU3VwR0hFQzNUMmJGK3B5dzN1TGkvY1FsblhKTk5rYXJuWitPOHdZdU5VZ0VWRlZNTmR4bWd2MEpNN3k2cmhvVjFTUFNKYnFhdE8zTmZad01qdGpFY3pNZGFJdXRqd29mcDVad0VLWmFWNVBGMnE0OFdiNFI0bk00TXMiLCJtYWMiOiI4MWI1YjhjNDI1YTVkM2NlNzk0YWFlZTc1NTZiODRhN2I2NDQzZmU0NGU3YzcxNTdjNjlkZDNlOTM5YzYwMWQyIiwidGFnIjoiIn0%3D; expires=Tue, 17-Jun-2025 15:55:10 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6InY2dnE3N2Qvait1N2oxRm9uckdCY1E9PSIsInZhbHVlIjoiR3p4TTIrOGQ4RlAxT2FodDEyR01mU2JKajg3MlFXRXpCcDVPYlZYVGtaS2V0eEt4NTRUTi9ETjY5QTRFWEhOSkxRRXpTd04zbjJuNnZ1SngwdnRWakYydUR3czVVcS9VVXZYa0dqRVRrMmNVZ2pqM2dxVXpvQ1gxdWt4b1NQZnAyTWt3aTRFOXVFd0tCRGVEenVwVFVTbFdsYk4rMklkRGhlTEdVcU1vamZFZGszOHBxKzF4ZUxqMDBFazVUdTVTOGhjTzZmZDFHdzNSS0FFMnp6ZVpuYnp0NzloS0ZhV05YRUxHUk9rNnBwZm5sMnpwZlM3VUFrcTdjRUVzSUxMeUU0WXhqRS9mTXdPNVBjcXllWVZTblJuM291S05xRHYvYUNTQUF0eWFDUk1mWlp2a1BieEVITnd1SlEydlJHcmdsQXgrMHhIQ2R5REZCODJqUFIvZlRlbTFJdWNMZmtSNXI4T2FqemhhSDF0YjVyZUlkeHZYaEJZZzJKdTZDWTZWMzkxK0NPT0J3bDAwZE9hTVFPMTNxZVlLWGRoaFpTOHBYeWhUSUZmenFqYlJsVi9pUEM0V0Y4SHhQNjZDd2E2czZCZlRBcFNwNXdDdHcraERsbXhyM3hIMW5Ma1hmdjM0aDZBQVJZc0lJYngwMUxjYlZocklDeWswVm5HQlRtRkQiLCJtYWMiOiIzYzFiNjgyMTg1ZDU2NTgwMDRlZTVjN2Y1MTExMGU4NzkyM2I2ZGZhZjZmYjk3NTdlNTRhZDMyYzk1Zjc2ZTY2IiwidGFnIjoiIn0%3D; expires=Tue, 17-Jun-2025 15:55:10 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-618103712\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1786925337 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>16</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1786925337\", {\"maxDepth\":0})</script>\n"}}