{"__meta": {"id": "X95f856e24041202d6eef5b828f572199", "datetime": "2025-06-17 13:53:02", "utime": **********.73514, "method": "GET", "uri": "/pos-financial-record/opening-balance", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 2, "messages": [{"message": "[13:53:02] LOG.info: Opening Balance Request Started {\n    \"user_id\": 16,\n    \"warehouse_id\": 8,\n    \"is_sale_session_new\": 1,\n    \"has_manage_pos_permission\": true\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.710584, "xdebug_link": null, "collector": "log"}, {"message": "[13:53:02] LOG.info: Returning opening balance view", "message_html": null, "is_string": false, "label": "info", "time": **********.712523, "xdebug_link": null, "collector": "log"}]}, "time": {"start": **********.042814, "end": **********.735169, "duration": 0.6923549175262451, "duration_str": "692ms", "measures": [{"label": "Booting", "start": **********.042814, "relative_start": 0, "end": **********.589431, "relative_end": **********.589431, "duration": 0.5466170310974121, "duration_str": "547ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.589448, "relative_start": 0.5466339588165283, "end": **********.735175, "relative_end": 5.9604644775390625e-06, "duration": 0.14572691917419434, "duration_str": "146ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 53255272, "peak_usage_str": "51MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 1, "templates": [{"name": "1x pos.financial_record.opening-balance", "param_count": null, "params": [], "start": **********.722618, "type": "blade", "hash": "bladeC:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\resources\\views/pos/financial_record/opening-balance.blade.phppos.financial_record.opening-balance", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fresources%2Fviews%2Fpos%2Ffinancial_record%2Fopening-balance.blade.php&line=1", "ajax": false, "filename": "opening-balance.blade.php", "line": "?"}, "render_count": 1, "name_original": "pos.financial_record.opening-balance"}]}, "route": {"uri": "GET pos-financial-record/opening-balance", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\FinancialRecordController@opinningBalace", "namespace": null, "prefix": "", "where": [], "as": "pos.financial.opening.balance", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FHttp%2FControllers%2FFinancialRecordController.php&line=262\" onclick=\"\">app/Http/Controllers/FinancialRecordController.php:262-323</a>"}, "queries": {"nb_statements": 4, "nb_failed_statements": 0, "accumulated_duration": 0.01434, "accumulated_duration_str": "14.34ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 16 limit 1", "type": "query", "params": [], "bindings": ["16"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.64481, "duration": 0.01094, "duration_str": "10.94ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 76.29}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.671443, "duration": 0.00092, "duration_str": "920μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 76.29, "width_percent": 6.416}, {"sql": "select `permissions`.*, `model_has_permissions`.`model_id` as `pivot_model_id`, `model_has_permissions`.`permission_id` as `pivot_permission_id`, `model_has_permissions`.`model_type` as `pivot_model_type` from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `model_has_permissions`.`model_id` = 16 and `model_has_permissions`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["16", "App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 326}, {"index": 21, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 566}], "start": **********.697412, "duration": 0.00111, "duration_str": "1.11ms", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:326", "source": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php:326", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=326", "ajax": false, "filename": "HasPermissions.php", "line": "326"}, "connection": "kdmkjkqknb", "start_percent": 82.706, "width_percent": 7.741}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (16) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 312}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 26, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}], "start": **********.701741, "duration": 0.0013700000000000001, "duration_str": "1.37ms", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 90.446, "width_percent": 9.554}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 2, "messages": [{"message": "[ability => manage pos, result => true, user => 16, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-215569503 data-indent-pad=\"  \"><span class=sf-dump-note>manage pos</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"10 characters\">manage pos</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>16</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-215569503\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.709538, "xdebug_link": null}, {"message": "[ability => manage pos, result => true, user => 16, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-950008629 data-indent-pad=\"  \"><span class=sf-dump-note>manage pos</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"10 characters\">manage pos</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>16</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-950008629\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.712307, "xdebug_link": null}]}, "session": {"_token": "PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "16", "_previous": "array:1 [\n  \"url\" => \"http://localhost/hrm-dashboard\"\n]"}, "request": {"path_info": "/pos-financial-record/opening-balance", "status_code": "<pre class=sf-dump id=sf-dump-1989568884 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1989568884\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-1424770128 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1424770128\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-408843008 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-408843008\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-473111890 data-indent-pad=\"  \"><span class=sf-dump-note>array:15</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"30 characters\">http://localhost/hrm-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1936 characters\">_clck=2gyfy%7C2%7Cfwu%7C0%7C1994; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clsk=1ti4td2%7C1750168380481%7C5%7C1%7Cl.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IkdWY1AxaGhyaVRYVGg2blVuWHg3c3c9PSIsInZhbHVlIjoiUW55WFNuZ2trM3JLNUJxbENUL2tXSXFIQy9HVjhWYVpBS3pOQTlXbm5zc2Vjd0taeFc3TVQ1VmdUejRndEFJSHNxNk5UQ2NDVHRDblJ4QnFQcFFqMTBOdjljMmtJbU9GWm5CbVJMSVZsdy9UdUYzcDlTQTVsQm90SmpZdGZSWDExZUEvWjJSNU1SUHgwZyt0VnpPWjd2eitXTEdyWTZsY2N2VHhDQW9hSWhCbjNVNzYvSEVnM0MzeUZrQ0RmMmhEYjlKZy9zUVBpeCt0eFUyUEV3U1ZPbEtUK2ErSW5MUjZ0enhOVUJUbTVnc2R4YTlCU281UzlzelF1Q1k2aUV0REtyUlc2eXRnMVFhSWFCM1c5NUtDc1V3Nk1SSURDVlFuWVhhL3RnMmNaQ3Z5QUZnb2dtS3RwMHVRdGozdGFOdGJkTVY5K1FzWnBiYUFqbXM4c1pCeld3UVYybDkzMUtHUjVMMVFTNCtsa3piZ0VDb1I0VjdUc2RiQ1lNbFAva0FIWHAyb0tHM28yNW1zVTk4aVhIZ3RZa0RtdUxOY1ZjbmpHbnBIeEZ3UzNFWUhIV05PZC8zdW45VW1NYmhybzcvMnhCSWNBQmFFcmxEdnFCbVFFN2FYcVp0WGVJZEZ6cjl2aDBleXdpTjA2NlgyNlQwTUIxWWlZQm9UV3UwR2YyUGUiLCJtYWMiOiJmNjhhYTI5MjBjY2VmODEyYWEzMDA3NDAwMjVmYTdjZGY0NjMxN2M5ZjAwMGFkZDBjYTg2MWQxNjVjOTI5MzZmIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6ImVWbHV5bTBLOFREV2t5S2lGR3VKNnc9PSIsInZhbHVlIjoiUmh6czZpSE1VQ1lDRnJ3SVpHYXhTMFMrSTZtU3dwaXJDVFZnaGF5WG5rVzNtRkZJZlJsMXNkVjdueFZ4ZThTRW44WFVGSTBxYzRPbzk1bXlPZHc5ZzJhL1ovQjByMUZpb1FJUU5ZOG5iOUN5emRnWWRuLzYzMmlSYU1BOFV2Nmo4ZHk2US9GdE5sZ0NTWHRnNDdMckpCUktPRTVHNGZpZXFqWW1QdHF4VmhJVlYvNWVXeTlLdXZ4cG9WSE16NUNiV0t1R29tR3hSV0dDa1ZtVXZsempqUWlRTi8xaUVqMnZVS0IwUWxRM1I4UURyUDVDMWJqcWovd2REaTlBMVRHSUNUc2pIU01Gay9GTHN5Q0lIYWtuaGhwVnZWR05VVWswVUt3bU1pZnA2eTgvN1pYRXliS1AvcUpvVEMweUdPYWNicGhDZU1HQitMTy9KUGR3V1NEVzgyeTRJNlN3Y0hYWW8zcVZNZWFmekVxR3hoU2cwWlhLeC9rdUtVMldZcG95ZndJTytsdGJaL3A2UzNSbUREdTkzZ3VjV3dzUnIzRk5XRHZOdGtjeEtNMkNCYVV6RStyZnVjcmhHZW5scXorY0tmWWZhSTlsNCtpUE9heUYrdmpDQUU1SE5CWkFjSlVjcVNadjlWSlFTbUQ1TW1CWFUzZEZFWG5raHQrYVR0RmEiLCJtYWMiOiI4MzI5OTI1OGEzNGE3NmE4NmExMGQzY2M5YTYwOWM0NjljYWI2MmYwMGE2YmY3YmE0YjY2OWJiMWYyNDc4YmYwIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-473111890\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-837713984 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">VloaUQCBjpDKrel7nHeAzB1sTLdTMwteSos1MmAN</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-837713984\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-703638582 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 17 Jun 2025 13:53:02 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6ImpCaWNDQkhFaEt0MlkyY0tSQXllV3c9PSIsInZhbHVlIjoibEM5dWw5WlNNTWVhckRqT0UzSVRhMlVuVUx0NDNQcTd2Z2w5UWoyNVJDcXN4VE1jRVI2SGp5TW0vUjloQktIU2tWY0xSOGVkcDBiU3lRZGFETmtyRHh3ZEEyZ0hVd21LbUt4SElGNmwydEkzZFJpd1hxSEM4UDlSWHJpQzdBRzBQbzQydWFVS0VMYUlEc1NPNDQrWXVQcStjbWJlUGVYK3RBeDRxK1M1VmxBOVlZS0pWZ0J2SFgzY3hWTTBIelRIWmZxSlZEQ2VkZmZYbmtWb3ZKV280d0krUXNuNGQwUnhpRmhXaVdPZW5mOEIzbjV3Sm9ndGRpUnY1UXFNM1plZVNJMUxqaHJoMXpiY3RXY0k5VVBzNDBoenY0WDZXRnNDK1BCanFmcENxV2JEQUdUdmt2dVJEa1lzMnhRV2dUU1R2OFJCTU82U2c3SzZ6WWpOR2ZpMWRUejUxajdSejM5S3BubndDdTBSQXVybTVCb2tBTGFCZlhWNXhsZ2tYb3hRbEZYeUJrdzVuOFZhWmRGNW1BTUliRktLVzFuekNvRFErWFlNYVB4clBwSXIwQmR6NkdVTWFwTEJqZ2M3dlhzY0pQMC9iSC9uRmx6Wk1IZHh1NUxnTDF3a0piM3pZUWV4QnRGOWpjQllneWtFaVJxbjNqUElwYXNjcEczZHIyU3EiLCJtYWMiOiJjNDc2N2RmMGM3ODhhZDg2NTkwMjFhMGNjNWFkNmExYzc2OWE3YTc3Mjc2ZjAwYmJiOWExZGE3MWQ1ZmYyODdlIiwidGFnIjoiIn0%3D; expires=Tue, 17 Jun 2025 15:53:02 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6ImEzWGl4VU0wYi9YNjNPMEFBbzBabFE9PSIsInZhbHVlIjoiV2l6YXlLa3c5K3NvdTlNZnNyWmg4dk9MMkNrbldSZndhRTdwL2JjY0pQUXBwcGkxbzFCZ1d4ZWlvSmdCZlI5M0hrN3h5NGJ4OFJnUGJTSnhJTVNUS1czVmtZVUFLOFZFUVNuMm4rNmFMQVF6QXVSL1AvUjRvRXdkOU5Eb1ZOdVJIbFNIMXZuNk80aHJBTnovL2MwZ3VqMUtzM3ZJMVA0UE0vSmdlUWVTYmgyWkpQN1pnK0wvb1lmUUNVMDF6dnd4NmVqVGF1OFZhMnV6MkQzaTgzblhKZFZiUHNBNjQxdjVYMTdSTU9JTzJrRWZxNXlwTmgvNTBRMmpsWTJPWDZVdmdab21qUzF5N1NoS3VyeWVhc2tmZ3FZd3B5WkJVWkpnYTZpbmMwYjRDYzhxYTM2cXhIQ0NpWjFCd1hydi9BSWlsRGJDWDRLRjZkKzJIZ21SdFNhMXRnSVdRSldVcWs2SkVxZlViVG42YUh3em9wMDY3NnowWklvTjhtYzJlTWRGbGhNWCtJUUFRTkVTR21EUXVNbWNaT1A3YVk4SDFBT2srRm82emt0ak5GSXYvVFVlaElYTk9qSHZVWHUxNnllWTZWS2YzcFRJVzY0RzJFL2V2YnVqVTgwVXM4eTQzYjR2SVUrUnBaUEFyMlJjcVF2VGFONlZhMFlPcVBMaUp1cnQiLCJtYWMiOiJkNTFmMWFlYTdiYjgwYzZiYjVmOTljNjVkZmIyNmM1ZTQ0OWE3MmVhYzE2Nzg3OTI2MjI2Y2U0MzRlMzcxMTcxIiwidGFnIjoiIn0%3D; expires=Tue, 17 Jun 2025 15:53:02 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6ImpCaWNDQkhFaEt0MlkyY0tSQXllV3c9PSIsInZhbHVlIjoibEM5dWw5WlNNTWVhckRqT0UzSVRhMlVuVUx0NDNQcTd2Z2w5UWoyNVJDcXN4VE1jRVI2SGp5TW0vUjloQktIU2tWY0xSOGVkcDBiU3lRZGFETmtyRHh3ZEEyZ0hVd21LbUt4SElGNmwydEkzZFJpd1hxSEM4UDlSWHJpQzdBRzBQbzQydWFVS0VMYUlEc1NPNDQrWXVQcStjbWJlUGVYK3RBeDRxK1M1VmxBOVlZS0pWZ0J2SFgzY3hWTTBIelRIWmZxSlZEQ2VkZmZYbmtWb3ZKV280d0krUXNuNGQwUnhpRmhXaVdPZW5mOEIzbjV3Sm9ndGRpUnY1UXFNM1plZVNJMUxqaHJoMXpiY3RXY0k5VVBzNDBoenY0WDZXRnNDK1BCanFmcENxV2JEQUdUdmt2dVJEa1lzMnhRV2dUU1R2OFJCTU82U2c3SzZ6WWpOR2ZpMWRUejUxajdSejM5S3BubndDdTBSQXVybTVCb2tBTGFCZlhWNXhsZ2tYb3hRbEZYeUJrdzVuOFZhWmRGNW1BTUliRktLVzFuekNvRFErWFlNYVB4clBwSXIwQmR6NkdVTWFwTEJqZ2M3dlhzY0pQMC9iSC9uRmx6Wk1IZHh1NUxnTDF3a0piM3pZUWV4QnRGOWpjQllneWtFaVJxbjNqUElwYXNjcEczZHIyU3EiLCJtYWMiOiJjNDc2N2RmMGM3ODhhZDg2NTkwMjFhMGNjNWFkNmExYzc2OWE3YTc3Mjc2ZjAwYmJiOWExZGE3MWQ1ZmYyODdlIiwidGFnIjoiIn0%3D; expires=Tue, 17-Jun-2025 15:53:02 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6ImEzWGl4VU0wYi9YNjNPMEFBbzBabFE9PSIsInZhbHVlIjoiV2l6YXlLa3c5K3NvdTlNZnNyWmg4dk9MMkNrbldSZndhRTdwL2JjY0pQUXBwcGkxbzFCZ1d4ZWlvSmdCZlI5M0hrN3h5NGJ4OFJnUGJTSnhJTVNUS1czVmtZVUFLOFZFUVNuMm4rNmFMQVF6QXVSL1AvUjRvRXdkOU5Eb1ZOdVJIbFNIMXZuNk80aHJBTnovL2MwZ3VqMUtzM3ZJMVA0UE0vSmdlUWVTYmgyWkpQN1pnK0wvb1lmUUNVMDF6dnd4NmVqVGF1OFZhMnV6MkQzaTgzblhKZFZiUHNBNjQxdjVYMTdSTU9JTzJrRWZxNXlwTmgvNTBRMmpsWTJPWDZVdmdab21qUzF5N1NoS3VyeWVhc2tmZ3FZd3B5WkJVWkpnYTZpbmMwYjRDYzhxYTM2cXhIQ0NpWjFCd1hydi9BSWlsRGJDWDRLRjZkKzJIZ21SdFNhMXRnSVdRSldVcWs2SkVxZlViVG42YUh3em9wMDY3NnowWklvTjhtYzJlTWRGbGhNWCtJUUFRTkVTR21EUXVNbWNaT1A3YVk4SDFBT2srRm82emt0ak5GSXYvVFVlaElYTk9qSHZVWHUxNnllWTZWS2YzcFRJVzY0RzJFL2V2YnVqVTgwVXM4eTQzYjR2SVUrUnBaUEFyMlJjcVF2VGFONlZhMFlPcVBMaUp1cnQiLCJtYWMiOiJkNTFmMWFlYTdiYjgwYzZiYjVmOTljNjVkZmIyNmM1ZTQ0OWE3MmVhYzE2Nzg3OTI2MjI2Y2U0MzRlMzcxMTcxIiwidGFnIjoiIn0%3D; expires=Tue, 17-Jun-2025 15:53:02 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-703638582\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1486257650 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>16</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"30 characters\">http://localhost/hrm-dashboard</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1486257650\", {\"maxDepth\":0})</script>\n"}}