{"__meta": {"id": "X96eb4c2efea428b963166df307bd6afd", "datetime": "2025-06-17 13:53:00", "utime": **********.784156, "method": "POST", "uri": "/chats/favorites", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.123791, "end": **********.784186, "duration": 0.6603949069976807, "duration_str": "660ms", "measures": [{"label": "Booting", "start": **********.123791, "relative_start": 0, "end": **********.671731, "relative_end": **********.671731, "duration": 0.5479400157928467, "duration_str": "548ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.671749, "relative_start": 0.5479581356048584, "end": **********.784189, "relative_end": 3.0994415283203125e-06, "duration": 0.11243987083435059, "duration_str": "112ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 46117960, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/favorites", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getFavorites", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "favorites", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=454\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:454-477</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.02895, "accumulated_duration_str": "28.95ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 16 limit 1", "type": "query", "params": [], "bindings": ["16"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.7141871, "duration": 0.02649, "duration_str": "26.49ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 91.503}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.756644, "duration": 0.00102, "duration_str": "1.02ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 91.503, "width_percent": 3.523}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (16) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.768792, "duration": 0.0014399999999999999, "duration_str": "1.44ms", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 95.026, "width_percent": 4.974}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "16", "_previous": "array:1 [\n  \"url\" => \"http://localhost/hrm-dashboard\"\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/favorites", "status_code": "<pre class=sf-dump id=sf-dump-1553698271 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1553698271\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-426085433 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-426085433\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-814578193 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-814578193\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1198317413 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"30 characters\">http://localhost/hrm-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1936 characters\">_clck=2gyfy%7C2%7Cfwu%7C0%7C1994; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clsk=1ti4td2%7C1750168377489%7C4%7C1%7Cl.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IjlGZkQzMlJGdzcxc3ZlSCtFRUlRSHc9PSIsInZhbHVlIjoiMWVRc21sSDczdTZaZlgyOVA3eXRVcFBrTHB1MVlNeXR1VUhpYmt4Q3JycDZNY0Q5L1ZkMVRNcjU4M3dHZ2xuN0V3NEFFbWVMb1ZqVjJyM0JTRmFUejZieGJBVk9rZHNTVHV0M0s2VDBadmVEN2FBVWtLOExmYTdjZ1lMVDQrU21SMnhONDZuY0haZnVtUHVGVkdaTElxUkcyVDRlc2dYWTZZSzJRdGNmdSszWFpVWUFYY2cvRWZFdnluMWRqa0hjdWM2SVFwRWZTaER2SG1ObEUzbDdsUkNCYXB4NUJGV2xHWTMraGRCQ2VpNGYxRFAzU2lLa1dsZ21oNzZteHdVdjkvTVZKTC9nemNZcSt5SXprSmNRSlhFOVBHQnhGNVhLUG0wZHRmMGNVVGZJbG9LS2N4THVqYnp2OGJOMzhRMjBtRTJkVTBPYzhlcFJ4bHVITUEyR1lPRm1sd2kwVHk1amJCeHdIK3d6c2FBMzZyZ1ltc2lxV2ZQVENrQldhN0RPdFNLd3dOSnJtZ01VcHlzZVc1UU55M2w0MFlSWDVXdEdKNGFFN2c0QjNZaTdPdVRMVWNDUVdxNjM3Yy9VMmhOQlhEeElMNHpEcHU2WHBLcE93NVJvdDV0WWZ0aWlseGZtSEkyaVNiMkNLQ3lWWHU5blZXMzF4Uk9ONWVzMkc0ZEsiLCJtYWMiOiIyODIxNTdmNmNmMWQzZjMzODUwMjg3Yjc5MTVkMDZmODgwOTBiMGZjMzY0ZTFkYjAzYjVjZTQ4OTdkZjcyZDA0IiwidGFnIjoiIn0%3D; test_session=eyJpdiI6InpkeTdTUzJGYWZZVUdTRU9DdDlHcGc9PSIsInZhbHVlIjoiLzFTZzZZV0RxMXFlRFRmMks3dXpNRHJwWXQ5WVlYcVpiVTRDdXR0RTBsNHN1eitwTlFDaHNMTmJUejhlbDVwVVVTWTJFTU96Y09PY2o0NEowaVJtVzYwVk5RWmU5TVN0TmJKY2VvcmpaN3lwYzlBZ2JpdUtFWk1BWlFPRmpVa3RrUHJzMTJFYzdvTmhtM0pzb0ovVWtWVWxMamx1T1hrMkJTUlN5RWt2bDdwcVVpR2dZVWdIYkJjNkRKaTNtMnhTa0VnamxyV2E2dWFVNE5qaFhBbkdxc1RBTWorbDBLaVhQVjVndnVieDQ0cHJyOUgzdENITlRKdGtobktScmY4RWZjb3RJV1FFUXY3d1BlTU5QL254YUJiS0FNY3pqT0Rnekx2TXR4anlqakk0ME4yQWJvcmkzbzZ0czBmZldHSkMyQzlJd05kSmdQa3BLdEZBb1VGM0F2REVUcDVMQ1N5WEpKTFFOMHZXNUxySnVZYUphLzd3ek9MbnpJdVhtbEVIRDI3a290TU9rbzc4aXFycDZoaVpPR01LRFBTRzVNcmp5WTlCeC83eW5scXVybEgrSFVVN0h5N0FxWTI1d3pLT3JzRGh2cXA5ZkovbU5oV0hnQ29nM0JYWjhoNkxBcm9vQjVSbitGa21BTG9kbTNUR1FTa1dTRC9DSW56Tm5RYmYiLCJtYWMiOiJiMDJmMmE0MjI1ZjQ5ZWU3YzVjNmFhYzIzMWUwZjBjM2M5MmZlMDdlMzQ1YmZjOWE5MGQ2MzI3ZTg0OGY1OWM0IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1198317413\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1514150946 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">VloaUQCBjpDKrel7nHeAzB1sTLdTMwteSos1MmAN</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1514150946\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1522606192 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 17 Jun 2025 13:53:00 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Ik9uY254c1NHdEZldWovZjRRcDJ4Tnc9PSIsInZhbHVlIjoiNHYrNFluQjE4TVRRQ2ZvaUhsL1NMcC9UVDhNZEYvZjU4TWRGeXlaWkxJbmF3SGJkV01tVnpYMHd1YUdVQUk4dnlDa0QzWXl5azFXcWxBb1FISnBKM3lRVHVJa2NOcTBiSGNWL3lqV1kzYndWM0xnSTZFNjkwc2JqWWFDZEdtbzFFcmZVdEZCc1RiU0NDTzdVNlZ1UmdqU3JUakdud3RoUU5xQTBvUW9ib0hnbnkyQkpKLzV4TFpOSTNVRldTc0ZhRGI2a0JJVXdQbzR1WFNqcFN4M0hIRUZyK3c3UEtOVkxBYmdKaWVkN1FkbEV0dFB3RnViTjJxNEFVYkNvdXF5ZXN1ZWNoUUpWRnZlK3pSL0JvOHJVakpNMDE0M1cyZDBkUHpHZ1ZuREVVYnpBN3piQ0N6emVYMktNK3FRcXdSZnlCTGdRM2VOdysyVys1NkdIenUzN1l5OXV2bnhZV1d1Nm1lVzdMYVliSGV1dEQ1c3JhdHo2S1BxYTc2QXQrRWpvNW15OGFnVEh2SEFWWEd5Z3FFRlpIWlhuNVRWQjRWRW1CWUlXTzN3bmY3VzhNSDhQL2J5WkdkY0FBRlZyV2IwUjg5NW1kOWdIYXNweEFWb0lFVERSZDFvUFlhNGtBYkJlTUt3enBLRDZZTERGUml4MS9XbUd1K0JOdXliZFRNdXEiLCJtYWMiOiI4Y2IzMDg1MTk1YTVkODllYzljOGViOTI1Mzc4ZDNhMjE1OTQ5MWUyOGI2MmY0OGQ4YWQwNTI3ZjRkYzFjZjI0IiwidGFnIjoiIn0%3D; expires=Tue, 17 Jun 2025 15:53:00 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6IlljVUE5YzNnTitrcGszbzJMb0p4SEE9PSIsInZhbHVlIjoiemc3VWZFN3l6T0liblU2VGpmSVdsZGZtcFpMMUlnQmdPeFFNRzhLRVkvUC8vNWtSUnc5R05Qb1pYSmhyV3JsdTRVaUJoUjhpSHYzQnVrSERUYjlBK240MFlHbDZFbGpINDZ5RU5KRG83QnhxWmZNQmFFNTJMeXZJVUQ0UDJUVzB0YVlWcEJ3YXFydFNicWdNd1FJcWg1TFZ2TjNTaGNiN0VmZG8xZTk1cmJ4bElpNHJja1NpY0dpS3ErRlllV3BvUm96dllkY1M3Q0tjZHFncTA5dHFUS1FxLzlOaWF0NWt3NllCS0NEbk5rcjFHUnpDUDE4ekZwemVESEd2bnpyOXBQd0lmUll0WWVma0NVVjI4TFdmWWh4TTlRZXA5V3lPR0JlMEhOd2h3ekk1V29pR2dOM1duZit5VVY2TnJBeE0yK3ZKYkV4R3dnRDV6RDh2MnJaZWc0d2NWdmtxNFFLOUN2czFqbE1SeGlXbVNFU1NOL29aWnJUeldVMy9sRnhNeS9qUUk4Y3Z0NUZCN1N6ZWlZWk4wK0txcVJVNTZHbEFLL24xRkk5M2ZZR1lLMlRyY0RXNHMrcEhSUTh1YVNZNTR3cEVOY3lqNXdILy81enJYcXpQeGxxOFdjTUJ1MnZDQ3Z3cDZhd2RiU3RjZVZYai9qTzJQRHNlaXJTSkI2cjkiLCJtYWMiOiI1MzFhNGYxY2ZlNjExOTI0N2ExMjFmMjQ4Yzc1ZTMwOTlkYWEyMjIxOTkzZThhOGVhMTdhYTA4MjhjNDI2ZmY3IiwidGFnIjoiIn0%3D; expires=Tue, 17 Jun 2025 15:53:00 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Ik9uY254c1NHdEZldWovZjRRcDJ4Tnc9PSIsInZhbHVlIjoiNHYrNFluQjE4TVRRQ2ZvaUhsL1NMcC9UVDhNZEYvZjU4TWRGeXlaWkxJbmF3SGJkV01tVnpYMHd1YUdVQUk4dnlDa0QzWXl5azFXcWxBb1FISnBKM3lRVHVJa2NOcTBiSGNWL3lqV1kzYndWM0xnSTZFNjkwc2JqWWFDZEdtbzFFcmZVdEZCc1RiU0NDTzdVNlZ1UmdqU3JUakdud3RoUU5xQTBvUW9ib0hnbnkyQkpKLzV4TFpOSTNVRldTc0ZhRGI2a0JJVXdQbzR1WFNqcFN4M0hIRUZyK3c3UEtOVkxBYmdKaWVkN1FkbEV0dFB3RnViTjJxNEFVYkNvdXF5ZXN1ZWNoUUpWRnZlK3pSL0JvOHJVakpNMDE0M1cyZDBkUHpHZ1ZuREVVYnpBN3piQ0N6emVYMktNK3FRcXdSZnlCTGdRM2VOdysyVys1NkdIenUzN1l5OXV2bnhZV1d1Nm1lVzdMYVliSGV1dEQ1c3JhdHo2S1BxYTc2QXQrRWpvNW15OGFnVEh2SEFWWEd5Z3FFRlpIWlhuNVRWQjRWRW1CWUlXTzN3bmY3VzhNSDhQL2J5WkdkY0FBRlZyV2IwUjg5NW1kOWdIYXNweEFWb0lFVERSZDFvUFlhNGtBYkJlTUt3enBLRDZZTERGUml4MS9XbUd1K0JOdXliZFRNdXEiLCJtYWMiOiI4Y2IzMDg1MTk1YTVkODllYzljOGViOTI1Mzc4ZDNhMjE1OTQ5MWUyOGI2MmY0OGQ4YWQwNTI3ZjRkYzFjZjI0IiwidGFnIjoiIn0%3D; expires=Tue, 17-Jun-2025 15:53:00 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6IlljVUE5YzNnTitrcGszbzJMb0p4SEE9PSIsInZhbHVlIjoiemc3VWZFN3l6T0liblU2VGpmSVdsZGZtcFpMMUlnQmdPeFFNRzhLRVkvUC8vNWtSUnc5R05Qb1pYSmhyV3JsdTRVaUJoUjhpSHYzQnVrSERUYjlBK240MFlHbDZFbGpINDZ5RU5KRG83QnhxWmZNQmFFNTJMeXZJVUQ0UDJUVzB0YVlWcEJ3YXFydFNicWdNd1FJcWg1TFZ2TjNTaGNiN0VmZG8xZTk1cmJ4bElpNHJja1NpY0dpS3ErRlllV3BvUm96dllkY1M3Q0tjZHFncTA5dHFUS1FxLzlOaWF0NWt3NllCS0NEbk5rcjFHUnpDUDE4ekZwemVESEd2bnpyOXBQd0lmUll0WWVma0NVVjI4TFdmWWh4TTlRZXA5V3lPR0JlMEhOd2h3ekk1V29pR2dOM1duZit5VVY2TnJBeE0yK3ZKYkV4R3dnRDV6RDh2MnJaZWc0d2NWdmtxNFFLOUN2czFqbE1SeGlXbVNFU1NOL29aWnJUeldVMy9sRnhNeS9qUUk4Y3Z0NUZCN1N6ZWlZWk4wK0txcVJVNTZHbEFLL24xRkk5M2ZZR1lLMlRyY0RXNHMrcEhSUTh1YVNZNTR3cEVOY3lqNXdILy81enJYcXpQeGxxOFdjTUJ1MnZDQ3Z3cDZhd2RiU3RjZVZYai9qTzJQRHNlaXJTSkI2cjkiLCJtYWMiOiI1MzFhNGYxY2ZlNjExOTI0N2ExMjFmMjQ4Yzc1ZTMwOTlkYWEyMjIxOTkzZThhOGVhMTdhYTA4MjhjNDI2ZmY3IiwidGFnIjoiIn0%3D; expires=Tue, 17-Jun-2025 15:53:00 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1522606192\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-2091071686 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>16</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"30 characters\">http://localhost/hrm-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2091071686\", {\"maxDepth\":0})</script>\n"}}