{"__meta": {"id": "X9d2f164512d446d0b64c2c864c6c860b", "datetime": "2025-06-17 13:28:57", "utime": **********.641482, "method": "GET", "uri": "/product-categories", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1750166936.885575, "end": **********.641504, "duration": 0.7559289932250977, "duration_str": "756ms", "measures": [{"label": "Booting", "start": 1750166936.885575, "relative_start": 0, "end": **********.506288, "relative_end": **********.506288, "duration": 0.6207129955291748, "duration_str": "621ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.506302, "relative_start": 0.6207270622253418, "end": **********.641507, "relative_end": 2.86102294921875e-06, "duration": 0.13520479202270508, "duration_str": "135ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 49310352, "peak_usage_str": "47MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET product-categories", "middleware": "web, verified, auth, XSS, category.access", "controller": "App\\Http\\Controllers\\ProductServiceCategoryController@getProductCategories", "namespace": null, "prefix": "", "where": [], "as": "product.categories", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FHttp%2FControllers%2FProductServiceCategoryController.php&line=203\" onclick=\"\">app/Http/Controllers/ProductServiceCategoryController.php:203-229</a>"}, "queries": {"nb_statements": 6, "nb_failed_statements": 0, "accumulated_duration": 0.021869999999999997, "accumulated_duration_str": "21.87ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 16 limit 1", "type": "query", "params": [], "bindings": ["16"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.557137, "duration": 0.01298, "duration_str": "12.98ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 59.351}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.583009, "duration": 0.00146, "duration_str": "1.46ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 59.351, "width_percent": 6.676}, {"sql": "select `permissions`.*, `model_has_permissions`.`model_id` as `pivot_model_id`, `model_has_permissions`.`permission_id` as `pivot_permission_id`, `model_has_permissions`.`model_type` as `pivot_model_type` from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `model_has_permissions`.`model_id` = 16 and `model_has_permissions`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["16", "App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 326}, {"index": 21, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 566}], "start": **********.607985, "duration": 0.00069, "duration_str": "690μs", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:326", "source": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php:326", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=326", "ajax": false, "filename": "HasPermissions.php", "line": "326"}, "connection": "kdmkjkqknb", "start_percent": 66.027, "width_percent": 3.155}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (16) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 312}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 26, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}], "start": **********.611777, "duration": 0.00067, "duration_str": "670μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 69.182, "width_percent": 3.064}, {"sql": "select `product_service_categories`.*, COUNT(pu.category_id) product_services from `product_service_categories` left join `product_services` as `pu` on `product_service_categories`.`id` = `pu`.`category_id` where `product_service_categories`.`created_by` = 15 and `product_service_categories`.`type` = 'product & service' group by `product_service_categories`.`id` order by `product_service_categories`.`id` desc", "type": "query", "params": [], "bindings": ["15", "product &amp; service"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/ProductServiceCategory.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Models\\ProductServiceCategory.php", "line": 116}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/ProductServiceCategoryController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Http\\Controllers\\ProductServiceCategoryController.php", "line": 206}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.619329, "duration": 0.00392, "duration_str": "3.92ms", "memory": 0, "memory_str": null, "filename": "ProductServiceCategory.php:116", "source": "app/Models/ProductServiceCategory.php:116", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FModels%2FProductServiceCategory.php&line=116", "ajax": false, "filename": "ProductServiceCategory.php", "line": "116"}, "connection": "kdmkjkqknb", "start_percent": 72.245, "width_percent": 17.924}, {"sql": "select count(*) as aggregate from `product_services` left join `product_service_categories` as `c` on `c`.`id` = `product_services`.`category_id` where `product_services`.`type` = 'product' and `product_services`.`created_by` = 15", "type": "query", "params": [], "bindings": ["product", "15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/ProductServiceCategoryController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Http\\Controllers\\ProductServiceCategoryController.php", "line": 209}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.627681, "duration": 0.00215, "duration_str": "2.15ms", "memory": 0, "memory_str": null, "filename": "ProductServiceCategoryController.php:209", "source": "app/Http/Controllers/ProductServiceCategoryController.php:209", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FHttp%2FControllers%2FProductServiceCategoryController.php&line=209", "ajax": false, "filename": "ProductServiceCategoryController.php", "line": "209"}, "connection": "kdmkjkqknb", "start_percent": 90.169, "width_percent": 9.831}]}, "models": {"data": {"App\\Models\\ProductServiceCategory": {"value": 26, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FModels%2FProductServiceCategory.php&line=1", "ajax": false, "filename": "ProductServiceCategory.php", "line": "?"}}, "App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 28, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 1, "messages": [{"message": "[ability => create purchase, result => true, user => 16, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1222141612 data-indent-pad=\"  \"><span class=sf-dump-note>create purchase</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"15 characters\">create purchase</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>16</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1222141612\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.617642, "xdebug_link": null}]}, "session": {"_token": "ixL6BN3l9tyyUscqj0hv99yKIy0daqhD43fAAWcL", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "16"}, "request": {"path_info": "/product-categories", "status_code": "<pre class=sf-dump id=sf-dump-1106875166 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1106875166\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-1363136518 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1363136518\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1429802038 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1429802038\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1855236527 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">ixL6BN3l9tyyUscqj0hv99yKIy0daqhD43fAAWcL</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1937 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=1pubyvl%7C2%7Cfwu%7C0%7C1993; _clsk=25xk9k%7C1750165707106%7C8%7C1%7Cl.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IkJySzk1VFFvckNqdnlvcHh0MVpJTkE9PSIsInZhbHVlIjoiWjRFNWhBaHlOa05SZ1UxU25FUnh0dk9kbE5GVVhyUFpaWnFHNzJZdkZEcXdnSXR2dVZqWXBhVlpYZUdhdXBic0U1NXU3bkd4SnZvNUM1S2IzeDZ3S2sxQURrbkgxT3g2b3N0dDVLcnVoeEJ2NHJyYUZjRXdIUzRHTnlvTFZoZjFjbVNER1ZOdTVWZSszTGRyTjQzWS9TdGpTa0IxQ3IxMVlTS0tXTnZQMERRV09lcGk1alBKTXVBcWNQV2RzQlhuaFY5UHV5cUlFVVlNUXZSbTczYTd0QmxaTVFqUjEvajVudXQyaUg5R2tsWXdZQlJ5MDN6Z0Z6SDV6RnlVeUZEVU9SQllzaGtTS2ZVenVZNGJjT05Fd2QzN0YwVmNkVHpRdElXVG4zSFR6LyttUVVVVFMxbDdqT1FvV09yUHZkWlhtcThJVGVwRG4wZWc5aEc3dVBoVVZYRTlKTWdjVUNGaXNtSFl3QTIzUXlNclJuTTRKSFhwL1JSUVVYYnRMN1pKcmlUMGFFTlFNMkhFVXJPNTF4VVhLUmlMSHlSd1hLQzl1d3pRT0tkSDZhY29nUzl0MW1RWGxIQ3Fkc2l1TkdyYjFmMXNQSmwwNnQydE1OczFiRlU1OURnNFFtWi9YeXNmckNEWVk0dWdLRy9nZTY5S2VHVjlGem44VWJXMW9sMmsiLCJtYWMiOiJiNTlmMGU1OTUxYWNjZjNiN2Q0ODFjNTQyYTkxYjdlYzUxNTE2MzMwMmQ2NTUzYTNiOWVlODlhZDNlOTI5MDMxIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6InlzTjlJdytTVmJCN2pDWEtMNEtocXc9PSIsInZhbHVlIjoiTE1hLy9EaytvYTdpeUpwZmlQeCtoLzBNaiszTzBBRjVlbnlwZThZVGs0dElYbFRoT3BvanYrZ05IR3VhaTVPRG8xNUJSQzVPUTcxVmRKRHFST3RTaXhrUDYwZ3Jnc0xqaWZrS0ZVTnYyVWZWWTdxeWM4a0ZLWVhkeHNrQ2dQREdTWG9LdnVvWGI3VXVQT29pQTZOTG5aSUhESFBFOWxGVlNidE9uclIxQ2VyeTMrNWlyRG9BTmtRK0hMdDlsbUQ3bnUxbUhxNTl4WkdhRmFkeE04b0U5UzZOMnhUcXcvN0wwNmdTNXhkTURTVmJwMVBTY2t1TCtTbXpBVzhoREk0Tnpta0NNMzhzR0dXZSsvaE10Qmcwdzlrd2VCSTV6aGJWZitDa2oxaEN6Rm5FV0RzVExuK08zM21DWHpxRDQ2QTF6RlhXeFJPbm9TOEdXRTA5cDNLKyswZ3Backo5eXJ4RFI2eUhUYXkxOU5nVmJWbUxXWXlGWFJ0K2w5QmRwY1RYa2VZbFg3RlFvSHpvN3NxdGtxd0RIOUZ0N2VMWTV5ekJ3L0lRQmxydExoQjFITzdJZG1XNXorZFNkS1JUZVUvMTcwZVFyMFAzUW9oTGZVaHlGQjhQVy93OEc4WFBQdE1ZNHdUTldUMTNNV01RdlBWdCswSDlWVzQzQ3FGL0JvZ1IiLCJtYWMiOiI2ZTczMWNkYTlmNzZjY2ViM2Y0MzJjMzg1NTllYWY3OTdiYmQ1NzRiNjRjMTQ3ZmUzNDc5MDQ3MTkxM2I4OGQyIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1855236527\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1224898923 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">ixL6BN3l9tyyUscqj0hv99yKIy0daqhD43fAAWcL</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">coJ97Z8YyNCeFgF4h0jUL1PrpYBjauguIcJbJbm8</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1224898923\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-960538674 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 17 Jun 2025 13:28:57 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IkRSdWZDMHN0b09LSTRjUUwyK1hYK2c9PSIsInZhbHVlIjoiMWFsY1pucWtqUEN5cHU2cFkxMG5rcjhWNGwvaGlQdkN3RThqWVJVK2Y5Mm9ZR3dZUHNRdzc3SjFKck4vYmk2THVpdVFVZ2NyY2ZNNGJmWk1Qd2lRSEpJK2VpMlltbk5ydUxSMTF4bkNvU0tJbGN4TWRPUHNzWkF1aVZHelRCOTJBOU1EQ3FNc05XZm5OWk5qRUZDSjl4K3QwdEUvc2FTcFdkOTREM01lWDkwY2o3QmhLYTlZYjVtZE05QWdIUFlwYUY2VE9zakxhWEIwWW9GWXBnSjl0czJaaG9IVzhxRnNPa3BhWE5va0twcTc0bkpla0lCbzRUZHhURGNJQmJMdEs4emF0aWhnVHlXVklYeVUvTVRzTXcxZFRpM2pYeURxZUtNS0hka01XREM3NVdyNm9lY0F3bENYY1VaZVRaaUxJUDNLbWRIbkZZdjM4U0pXTGZneWY3VDdhYkNveVIrcXlrZ0tWM3lVa2gvM2FEZm1PRm1ndXRGbVhjYTRVb0Voa0F0Y1N6bUxZL0ZrWDdsKzFjYTlTb1RnL1lSZlNnczJrVGtxNGRVM3Q1VlRyS3prTVBmK3NKWEZ1cnZFbTFiSnhsYk5CQ29yL1A1UWFpdUwvWXFTOXJDT1FKdmtSeXpsN3BKWmZSWU8vZFphQjhIditlY3VUU3NwV2NYRDFwaDYiLCJtYWMiOiIzOWQxMDAxZDU4ZjMzMGJmMDdjNzk2MDIxZjcxN2MwY2VkYzU3MDQ2MDUyZjhmZGY3MDViMjIxNGU3MzhlMmI3IiwidGFnIjoiIn0%3D; expires=Tue, 17 Jun 2025 15:28:57 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6IjdvOThqQ21uSWxXWjZKaVhYNXZ6aFE9PSIsInZhbHVlIjoia09PVDNnZm9ETzE3MGpzZjBqcHRJUDZNUUlpeWY3OGFOSWwvK0VMcUsxaFRtTjRNbHVaME1RdkR5U2RLd3Z0UTd1ZlBvNFdNdVlXNHlORTMva21vUG10ZUl0Q1pDNU5jaWcremV5a3ZDdk12MnlEajBYWjFLekt3NytIRytXRkc3djNzTkNqQjM3VmVWcWFKMDAxOEhYZlhFZkNJTjRDTEJnbU1YMXU4bEpoY29XVE0vS1NoZXgzSHZidmJ1RXNZK2FZM2hveFV2d1U2YkNzaGdJZ0kwYi9OcnVmUlViZitiTVAwOFV3ZnZOc05YaEhXVzFVRnhRZ0hnenpNNHZGSWwxK0RaaVd2eTY0NkNNM2tvU0FpZG9od005VXVONVF4bjhiS2g0dTNjNVVmU3VYQUlMMUMvSFNaNDNZZTRRdUJnNkJWV2lpMHM1QzVMbFQ5Y3MzS1I3YlZtb2RnNnd6Y2UvNkx2eVcrUnZVdmVUR3dMNzd3SDJDOTEvaUk1bzlJVThLcDJLZHBZY1RMa0d0bXNTdW9kZGVwbGJXWVRMM2NLbnN1cmFWcFUzSW51cFZSc1VpM0cvdmJiUjgrQ2hmOGpZaWNHRzNMOXRpeENpZzJWUHk3WkRGdXY1dEg3Q3dnTWdkL0JPV1ltY28vZW9oSWRNSm9TRG9oYUF5c1lQL00iLCJtYWMiOiIwYTZhZmM3YzAyYjRhNDNmNDY0YmY5Yzk2ZTNhYjA5MzQyNmRiOGEzOTZmMDc2Njk2ZGEzOGVkZjQ0NTA1N2UxIiwidGFnIjoiIn0%3D; expires=Tue, 17 Jun 2025 15:28:57 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IkRSdWZDMHN0b09LSTRjUUwyK1hYK2c9PSIsInZhbHVlIjoiMWFsY1pucWtqUEN5cHU2cFkxMG5rcjhWNGwvaGlQdkN3RThqWVJVK2Y5Mm9ZR3dZUHNRdzc3SjFKck4vYmk2THVpdVFVZ2NyY2ZNNGJmWk1Qd2lRSEpJK2VpMlltbk5ydUxSMTF4bkNvU0tJbGN4TWRPUHNzWkF1aVZHelRCOTJBOU1EQ3FNc05XZm5OWk5qRUZDSjl4K3QwdEUvc2FTcFdkOTREM01lWDkwY2o3QmhLYTlZYjVtZE05QWdIUFlwYUY2VE9zakxhWEIwWW9GWXBnSjl0czJaaG9IVzhxRnNPa3BhWE5va0twcTc0bkpla0lCbzRUZHhURGNJQmJMdEs4emF0aWhnVHlXVklYeVUvTVRzTXcxZFRpM2pYeURxZUtNS0hka01XREM3NVdyNm9lY0F3bENYY1VaZVRaaUxJUDNLbWRIbkZZdjM4U0pXTGZneWY3VDdhYkNveVIrcXlrZ0tWM3lVa2gvM2FEZm1PRm1ndXRGbVhjYTRVb0Voa0F0Y1N6bUxZL0ZrWDdsKzFjYTlTb1RnL1lSZlNnczJrVGtxNGRVM3Q1VlRyS3prTVBmK3NKWEZ1cnZFbTFiSnhsYk5CQ29yL1A1UWFpdUwvWXFTOXJDT1FKdmtSeXpsN3BKWmZSWU8vZFphQjhIditlY3VUU3NwV2NYRDFwaDYiLCJtYWMiOiIzOWQxMDAxZDU4ZjMzMGJmMDdjNzk2MDIxZjcxN2MwY2VkYzU3MDQ2MDUyZjhmZGY3MDViMjIxNGU3MzhlMmI3IiwidGFnIjoiIn0%3D; expires=Tue, 17-Jun-2025 15:28:57 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6IjdvOThqQ21uSWxXWjZKaVhYNXZ6aFE9PSIsInZhbHVlIjoia09PVDNnZm9ETzE3MGpzZjBqcHRJUDZNUUlpeWY3OGFOSWwvK0VMcUsxaFRtTjRNbHVaME1RdkR5U2RLd3Z0UTd1ZlBvNFdNdVlXNHlORTMva21vUG10ZUl0Q1pDNU5jaWcremV5a3ZDdk12MnlEajBYWjFLekt3NytIRytXRkc3djNzTkNqQjM3VmVWcWFKMDAxOEhYZlhFZkNJTjRDTEJnbU1YMXU4bEpoY29XVE0vS1NoZXgzSHZidmJ1RXNZK2FZM2hveFV2d1U2YkNzaGdJZ0kwYi9OcnVmUlViZitiTVAwOFV3ZnZOc05YaEhXVzFVRnhRZ0hnenpNNHZGSWwxK0RaaVd2eTY0NkNNM2tvU0FpZG9od005VXVONVF4bjhiS2g0dTNjNVVmU3VYQUlMMUMvSFNaNDNZZTRRdUJnNkJWV2lpMHM1QzVMbFQ5Y3MzS1I3YlZtb2RnNnd6Y2UvNkx2eVcrUnZVdmVUR3dMNzd3SDJDOTEvaUk1bzlJVThLcDJLZHBZY1RMa0d0bXNTdW9kZGVwbGJXWVRMM2NLbnN1cmFWcFUzSW51cFZSc1VpM0cvdmJiUjgrQ2hmOGpZaWNHRzNMOXRpeENpZzJWUHk3WkRGdXY1dEg3Q3dnTWdkL0JPV1ltY28vZW9oSWRNSm9TRG9oYUF5c1lQL00iLCJtYWMiOiIwYTZhZmM3YzAyYjRhNDNmNDY0YmY5Yzk2ZTNhYjA5MzQyNmRiOGEzOTZmMDc2Njk2ZGEzOGVkZjQ0NTA1N2UxIiwidGFnIjoiIn0%3D; expires=Tue, 17-Jun-2025 15:28:57 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-960538674\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1251521260 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">ixL6BN3l9tyyUscqj0hv99yKIy0daqhD43fAAWcL</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>16</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1251521260\", {\"maxDepth\":0})</script>\n"}}