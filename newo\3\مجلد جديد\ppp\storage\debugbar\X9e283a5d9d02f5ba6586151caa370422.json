{"__meta": {"id": "X9e283a5d9d02f5ba6586151caa370422", "datetime": "2025-06-17 13:27:43", "utime": **********.188951, "method": "GET", "uri": "/pos-payment-type?vc_name=10&user_id=&warehouse_name=8&quotation_id=0", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1750166862.550153, "end": **********.188972, "duration": 0.6388189792633057, "duration_str": "639ms", "measures": [{"label": "Booting", "start": 1750166862.550153, "relative_start": 0, "end": **********.058624, "relative_end": **********.058624, "duration": 0.5084710121154785, "duration_str": "508ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.058636, "relative_start": 0.5084829330444336, "end": **********.188975, "relative_end": 3.0994415283203125e-06, "duration": 0.1303391456604004, "duration_str": "130ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 49454224, "peak_usage_str": "47MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET pos-payment-type", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\PosController@posBillType", "namespace": null, "prefix": "", "where": [], "as": "pos.billtype", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FHttp%2FControllers%2FPosController.php&line=1458\" onclick=\"\">app/Http/Controllers/PosController.php:1458-1566</a>"}, "queries": {"nb_statements": 4, "nb_failed_statements": 0, "accumulated_duration": 0.016470000000000002, "accumulated_duration_str": "16.47ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 16 limit 1", "type": "query", "params": [], "bindings": ["16"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.113665, "duration": 0.01439, "duration_str": "14.39ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 87.371}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.14187, "duration": 0.00074, "duration_str": "740μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 87.371, "width_percent": 4.493}, {"sql": "select `permissions`.*, `model_has_permissions`.`model_id` as `pivot_model_id`, `model_has_permissions`.`permission_id` as `pivot_permission_id`, `model_has_permissions`.`model_type` as `pivot_model_type` from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `model_has_permissions`.`model_id` = 16 and `model_has_permissions`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["16", "App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 326}, {"index": 21, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 566}], "start": **********.169091, "duration": 0.00069, "duration_str": "690μs", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:326", "source": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php:326", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=326", "ajax": false, "filename": "HasPermissions.php", "line": "326"}, "connection": "kdmkjkqknb", "start_percent": 91.864, "width_percent": 4.189}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (16) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 312}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 26, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}], "start": **********.17296, "duration": 0.00065, "duration_str": "650μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 96.053, "width_percent": 3.947}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 1, "messages": [{"message": "[ability => manage pos, result => true, user => 16, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-113674356 data-indent-pad=\"  \"><span class=sf-dump-note>manage pos</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"10 characters\">manage pos</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>16</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-113674356\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.179025, "xdebug_link": null}]}, "session": {"_token": "ixL6BN3l9tyyUscqj0hv99yKIy0daqhD43fAAWcL", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "16"}, "request": {"path_info": "/pos-payment-type", "status_code": "<pre class=sf-dump id=sf-dump-546688671 data-indent-pad=\"  \"><span class=sf-dump-num>404</span>\n</pre><script>Sfdump(\"sf-dump-546688671\", {\"maxDepth\":0})</script>\n", "status_text": "Not Found", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-2040718408 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>vc_name</span>\" => \"<span class=sf-dump-str title=\"2 characters\">10</span>\"\n  \"<span class=sf-dump-key>user_id</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>warehouse_name</span>\" => \"<span class=sf-dump-str>8</span>\"\n  \"<span class=sf-dump-key>quotation_id</span>\" => \"<span class=sf-dump-str>0</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2040718408\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-2000374824 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-2000374824\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1502057203 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">ixL6BN3l9tyyUscqj0hv99yKIy0daqhD43fAAWcL</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1937 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=1pubyvl%7C2%7Cfwu%7C0%7C1993; _clsk=25xk9k%7C1750165707106%7C8%7C1%7Cl.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6Ik9qNjlLZ2M4bCtzODBNWjhaMm1Vd1E9PSIsInZhbHVlIjoiWEpUeTVRSTFtamd0ZjFySmtERUlBdmVNOVpQL0tyTHFvbzkzUTd1NkN0YTIrWEQ4VlppVTQ5Rzhab2dobmJRNnJvc1h4b3BvamFjV0MxSHZWLzJCMXY0c2VKaUticzdQb2IwbXR3WEdFdDNwL3BUQ2FlR0VMSkJyWGx5Y29GelhkNy96enJjUG05SC9pd2FtazhVdktjRFpOeEF6QzlvZjZkaXVVMW1nWWdDNzU4dU9reXd5SW4xOVJOZjBPcHZlQVd6aXRjV1pVTlRuVUkrWjlDeTZqOWhUUk5CYW9FOXlQb0VUSmI0RmxFeG9Jd0xRamQyWDFYYURwVDQ2c3hkd3N6TkJ6b1hBRUlTRmxGc0Z1NlpvV1JzNVkxQWQ2YUNkU2lHVkdlYVRhQ2JlQm1DRm11RDBCU1UrelpEbnBHQUVzK2V2REhDNlp4LzVsZmxXUVltejE2UHpDZDFsTUxDaHk0OEVlbUdxcm1ta2U1T3ZjZWRaOXcvamEyblZKY2dsUFIvZ2pGK2t4djJwYXhGNGdVVG1wWXdsczZ2dVFOVStVcThtd3JuVEQ5YnNhS3hjbTZ2dldlNG1aMTh1azFXWjhNVVVkcDR3T0l6V29uZ0pueEJTTDlmSXdJRFVYMDJCNy90V1h2Q0l5UlFvTlc4MUc4UHpnOU52dUFrVnVTRGUiLCJtYWMiOiJmNzBkMzYwYjhiMjY3NDc2ZGNlMDU4NTIxMThjZjNiYWRhZjViMDNkMDgxZWFhZDA1YTA5NGE4NTg5YmFmMDM1IiwidGFnIjoiIn0%3D; test_session=eyJpdiI6IkxWR2pxVlBvSTM1cHM0dDMvdkVkMXc9PSIsInZhbHVlIjoibXBtUGt5b2FIVnhVa2wwREpKNi9zWnY4L205WEVLbUxxMm9oYTUrSkJuRjZxbEJKbTlYeEhCbGZPbysvb3BwbStKWHFXM0IxbE5wT3RZb3Z4S3hLQXF0SEdBQWt6amQreVgyeU5iOVIrKzEwaTVtVnlNdU9mMFpyVXl3TlJGaGVKdzJCcVZHMkIwOW1MTzUvbTNmaHE1aTFpcTF5TUZ3ckFkTThaZC9rNjFObldKSkxHckNmN3dvRnpaN2N5SDdkQlBrQ014NnU3UjZqMmhwWnlHYkh0dlo2Z3p0NGo2MUVkS2RoWktvMFRSMm9WUjVhSzZlMHg4OTN1V253YVpUR082OEw4d3FnUk8wUGt4SkxxSUFCS3ZnOGFERnRpY2ZPaUF1N2RkS204SFgzTTNaOTg5aE0wdW4waW5VWGd1a2Q0b0MrQVhsd0lWQmZVc080VU9Lanl0NW1sNDZJck41TE1uMEp6c1pBTXV5Q1FOYVpMbHhValN5bW5qV2xuZCtIZWpMWGZIQWQ4UmtsVmMwS0s1YU5mSDNBT1ZzMUxpeVlUYmk4L0FsNzdiYytiQU56eExFL3NRaWViNHpvV0JKaXRURXEyV3IzenR3OTltc2RsOCsvOERKdkNrNnRPdG5ZdnVVNFNQb3pWczRhOHh5eTRncHhrdUJ6aHFhMGJ5bGciLCJtYWMiOiI3NjIxMjg0MTk0NGY0YTNmMWI1NzU0NDgyZGE5YjllYmNkYmUzYzBlNjg2NDUzNTc4NzhlNWVhZTkyZGRjZTE4IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1502057203\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1859299580 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">ixL6BN3l9tyyUscqj0hv99yKIy0daqhD43fAAWcL</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">coJ97Z8YyNCeFgF4h0jUL1PrpYBjauguIcJbJbm8</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1859299580\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-2028409595 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 17 Jun 2025 13:27:43 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6ImN1eFlibzZBRm9wckhlM2ZOZmdlVHc9PSIsInZhbHVlIjoiWmkvVWhYOFpQS2o2YWJqekNDVCt1R1NNVkdXRTV4a3ExSkpJK2tYTTc2RXB0Wi9tZG4yVnpkZ2RWTlo0MHQ5R1A0Z2N2LzBTT3Yrbk1Gc3FSa2trZzNYalRIT3BvVEM2QjhLanR3UGJqQnZBYjRZWmlLQUJQQnNyanY3Rm5jeVdja3Vyd09MMDNrNUpZeVYxM0Vxa0JWSWFUUFgyVTZnM0htZmJmNVBpR1g3WVlNMUhHSHlYZUhJSm55NkkxZnJTM09YSENPaTBoNW1obW9wdm1wNTFwbjFPN3A0bnBzT01qTlI0LzFUaFo3ckFQcjYxK1JzeDJkb3pkNG9leld0bSs2WjdMMFlzZ2ZkU1ZqeGdtUXFZWUVwNVlJOFhZUEJTUHlHbTBxcGpISFZ4cnZSZTFJaEdsSUJ2a0dOem92ZllzTTVmNG9SRXpvbEQrV2o5NFpHb3dLY1R3UUt4NktqYmt5WTIzdXplQmYrMTRlQms0cnNJajkvYk92d0RaZWJvK3dTRlRqeUFETW9XeFVNMEF0STIxdTZSdS9NWHRPVEROcG0zRjJ3VzRIRmlqaS93dEorNGFNZkF1ek45S1BRZ2F5K1N2aW81MGNxRm9aSklNYmIxMnh0d3NUWVBCdERiTjNvZHF0Tk0xZzlJSS9nWklXTlY5M1JQL2VLRWtYRngiLCJtYWMiOiJjZjZiYzJmMjUyYjA4ZGRkN2FhMDIxMmMxNmI2M2M4MjA5NmVlMGU2ZGMzYTlhNDFhZjYwNTk0YzI4NjllZDcxIiwidGFnIjoiIn0%3D; expires=Tue, 17 Jun 2025 15:27:43 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6IncyMkNaYU92MGtDbDdGQnp4NWx2Smc9PSIsInZhbHVlIjoiWGhCOHhvZFBDZWY4Vlp2alA0bmoxbStOajRFM2pVbGM2WThiakNUSFBIZi9SQXphRnJ2ZWQxVXM0a0c2TUpzc1pRbklDaTc0RWJRYjd6eFI5SG9YNmdxV25jWjhPeVR0VER3RlF0Mit1L3VOTjNaRERvWEZyMlpySlZWclUyVUdKYlUzM3JLQzJHRDAweEpUUGhlb2xnbC9EckNDMlRuTFZNV0Fla1pKUDYwRHhRNDh1QjBQck01TkhBUkRpSFdwRFdZNFVwaDUwdGUzRUorMkJEbDFDWWxCbzNxbUpxNUJhMko5T1k0ZFo3NC9EdlRtWWhXb0JZRGxSRnN5RUNMbmJEeExUTVoyTDlkaFdMVW5CbitNQm5QVVZTdHRwUWQ0RUIxWjZjRVNzdjJlRjJnaGMwTkVvcGgzMzRrdlFsU0pFZTR0bytsNGJqWFlkU0ppTUpOZzJ1TlNRR3FXQ1MxalZwU2VUaUZjVDAvNGM3RGxBYUJOQWpGQTdENTBnZmRqZloxMWg3TXFHVWY5SVkxaW5NNjVzTVdsMWt5bnFzTC9MYXNOaGpjOFRud3FFL21PTTNSWGlsOGxKVW94ZHYzbm0yVjVXOXk5U2NVMzczWHp6UHRtbWpkZFRKbHdBbEtoTmo3ZEJOald1VGVvVTVaSXltckR1VG1jOXBqcUF6TzUiLCJtYWMiOiIxMzEzNTNlMzMzYjIwNTQ2ODU2YzEyMmNkYjUyMzgxZGNkZGYwODhkODBiODFkMzNkMjRmNDAzNzM2ZmM5N2M2IiwidGFnIjoiIn0%3D; expires=Tue, 17 Jun 2025 15:27:43 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6ImN1eFlibzZBRm9wckhlM2ZOZmdlVHc9PSIsInZhbHVlIjoiWmkvVWhYOFpQS2o2YWJqekNDVCt1R1NNVkdXRTV4a3ExSkpJK2tYTTc2RXB0Wi9tZG4yVnpkZ2RWTlo0MHQ5R1A0Z2N2LzBTT3Yrbk1Gc3FSa2trZzNYalRIT3BvVEM2QjhLanR3UGJqQnZBYjRZWmlLQUJQQnNyanY3Rm5jeVdja3Vyd09MMDNrNUpZeVYxM0Vxa0JWSWFUUFgyVTZnM0htZmJmNVBpR1g3WVlNMUhHSHlYZUhJSm55NkkxZnJTM09YSENPaTBoNW1obW9wdm1wNTFwbjFPN3A0bnBzT01qTlI0LzFUaFo3ckFQcjYxK1JzeDJkb3pkNG9leld0bSs2WjdMMFlzZ2ZkU1ZqeGdtUXFZWUVwNVlJOFhZUEJTUHlHbTBxcGpISFZ4cnZSZTFJaEdsSUJ2a0dOem92ZllzTTVmNG9SRXpvbEQrV2o5NFpHb3dLY1R3UUt4NktqYmt5WTIzdXplQmYrMTRlQms0cnNJajkvYk92d0RaZWJvK3dTRlRqeUFETW9XeFVNMEF0STIxdTZSdS9NWHRPVEROcG0zRjJ3VzRIRmlqaS93dEorNGFNZkF1ek45S1BRZ2F5K1N2aW81MGNxRm9aSklNYmIxMnh0d3NUWVBCdERiTjNvZHF0Tk0xZzlJSS9nWklXTlY5M1JQL2VLRWtYRngiLCJtYWMiOiJjZjZiYzJmMjUyYjA4ZGRkN2FhMDIxMmMxNmI2M2M4MjA5NmVlMGU2ZGMzYTlhNDFhZjYwNTk0YzI4NjllZDcxIiwidGFnIjoiIn0%3D; expires=Tue, 17-Jun-2025 15:27:43 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6IncyMkNaYU92MGtDbDdGQnp4NWx2Smc9PSIsInZhbHVlIjoiWGhCOHhvZFBDZWY4Vlp2alA0bmoxbStOajRFM2pVbGM2WThiakNUSFBIZi9SQXphRnJ2ZWQxVXM0a0c2TUpzc1pRbklDaTc0RWJRYjd6eFI5SG9YNmdxV25jWjhPeVR0VER3RlF0Mit1L3VOTjNaRERvWEZyMlpySlZWclUyVUdKYlUzM3JLQzJHRDAweEpUUGhlb2xnbC9EckNDMlRuTFZNV0Fla1pKUDYwRHhRNDh1QjBQck01TkhBUkRpSFdwRFdZNFVwaDUwdGUzRUorMkJEbDFDWWxCbzNxbUpxNUJhMko5T1k0ZFo3NC9EdlRtWWhXb0JZRGxSRnN5RUNMbmJEeExUTVoyTDlkaFdMVW5CbitNQm5QVVZTdHRwUWQ0RUIxWjZjRVNzdjJlRjJnaGMwTkVvcGgzMzRrdlFsU0pFZTR0bytsNGJqWFlkU0ppTUpOZzJ1TlNRR3FXQ1MxalZwU2VUaUZjVDAvNGM3RGxBYUJOQWpGQTdENTBnZmRqZloxMWg3TXFHVWY5SVkxaW5NNjVzTVdsMWt5bnFzTC9MYXNOaGpjOFRud3FFL21PTTNSWGlsOGxKVW94ZHYzbm0yVjVXOXk5U2NVMzczWHp6UHRtbWpkZFRKbHdBbEtoTmo3ZEJOald1VGVvVTVaSXltckR1VG1jOXBqcUF6TzUiLCJtYWMiOiIxMzEzNTNlMzMzYjIwNTQ2ODU2YzEyMmNkYjUyMzgxZGNkZGYwODhkODBiODFkMzNkMjRmNDAzNzM2ZmM5N2M2IiwidGFnIjoiIn0%3D; expires=Tue, 17-Jun-2025 15:27:43 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2028409595\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-478211212 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">ixL6BN3l9tyyUscqj0hv99yKIy0daqhD43fAAWcL</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>16</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-478211212\", {\"maxDepth\":0})</script>\n"}}