{"__meta": {"id": "Xa95f84c9bb53e310d8fa5eb5f560cbc8", "datetime": "2025-06-17 13:48:41", "utime": **********.265421, "method": "POST", "uri": "/warehouse-empty-cart", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1750168120.584365, "end": **********.265463, "duration": 0.681098222732544, "duration_str": "681ms", "measures": [{"label": "Booting", "start": 1750168120.584365, "relative_start": 0, "end": **********.165691, "relative_end": **********.165691, "duration": 0.5813260078430176, "duration_str": "581ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.165715, "relative_start": 0.5813500881195068, "end": **********.265468, "relative_end": 4.76837158203125e-06, "duration": 0.09975290298461914, "duration_str": "99.75ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 46326408, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST warehouse-empty-cart", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\ProductServiceController@warehouseemptyCart", "namespace": null, "prefix": "", "where": [], "as": "warehouse-empty-cart", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FHttp%2FControllers%2FProductServiceController.php&line=1663\" onclick=\"\">app/Http/Controllers/ProductServiceController.php:1663-1673</a>"}, "queries": {"nb_statements": 2, "nb_failed_statements": 0, "accumulated_duration": 0.01766, "accumulated_duration_str": "17.66ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 16 limit 1", "type": "query", "params": [], "bindings": ["16"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.2182462, "duration": 0.01695, "duration_str": "16.95ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 95.98}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.250298, "duration": 0.0007099999999999999, "duration_str": "710μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 95.98, "width_percent": 4.02}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "16", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]"}, "request": {"path_info": "/warehouse-empty-cart", "status_code": "<pre class=sf-dump id=sf-dump-260792723 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-260792723\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-1187680057 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1187680057\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1961379211 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>session_key</span>\" => \"<span class=sf-dump-str title=\"3 characters\">pos</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1961379211\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-23688429 data-indent-pad=\"  \"><span class=sf-dump-note>array:19</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">15</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1936 characters\">_clck=2gyfy%7C2%7Cfwu%7C0%7C1994; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clsk=1ti4td2%7C1750167218299%7C2%7C1%7Cl.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IjJ2blY2TkFCSGRKZzFCR01YaE9QWGc9PSIsInZhbHVlIjoicVplK2FaZ1Vxait1Mk5QREg3L3pNOHZaQkVzNW5nWUtRUmVvWXZLU3JWR0hlL2VzV1BZLzlsSVF2bmhRVFJoVWVEL2FibkxUU2dRRUpPZFF3Z0ZDanc5SUIra05FR3FJY0gxemJCN1p5aitEOGkzOGtLek1LZVpYYUQwb1BmYW1ZM3RFR2I1anRnckhXNm9TU3JTT2lwUHlKOVZQMnZRNG9veTJIcGJiY3Rzb2dpc0l4dkpTdHRhUko1emdqQ1RCZVoxYnRpd1VURUJsNDNSM1hqREFDVUFoNEo1QlVRSHFwNlRNRW0xN3JuZmV1cVorb0dSWGtTTzNoR0FvcVZPdlp5Y1VCTXBHcnRaY29GSnFub0dHWGNCWkp3V1FweGJpelh4ajdoMm95dksrYXBmU21rREsrbVJGR0RKWU1JRWI2VWJuTm83S3phanFyYVQ1UXNzcHZ3YzlPb3dXTldxcWhMMHlUSXltR0lELzk2SzJiMFczV21YbVJLbHFEK3NqWWNCcmpCNDdyM3RJZ3BjYTZDWHZZc1h5NUxvV1hDT0wvTm5tQXdJSEw5UTJWTm1sTWFWZk9NNUpFQ1lqbFpuc0ZtQTJCL2lNZFc1UHg0cmVHQlVuRGxyOXRlaU5yQWdydTRyNjFJMm8vMG53ZDkyMkpCZXA1SUpzcnN5dE92MEEiLCJtYWMiOiJhNmQwZjQyMjg0ZTVmY2ExZWVmMTE5MWI0NzIyOWMxZDc0ZDY4MWYxMDQ3NWZlNTk4Yjg5ZmE0MjUxOWQ3MmM1IiwidGFnIjoiIn0%3D; test_session=eyJpdiI6Ijdzd0VheWtuZ2Ryck1EaGo0akdibGc9PSIsInZhbHVlIjoiaTNUZmdiK2dYbmNpbHFaTnpTM3lnbUhoTHlsZE91RTRvQ0ZQaE5RdFRKWmt2ZmxJOUxNTkpuT1k0MW8xLzJ1dEtNbE52WU9xUWcyd0Y3TnNSdFFoZUVrSmhmbDJRai8zbFczVjllRXVyTFlEemREdFdzZkFCRUVNVmwycDQ1V0g0Uzlvb3NrK0ttUXZUeUVtQTRFSklmZHhSUlh1ZFFHYXJRV1FXNUFKYzZkdW1Da3NGUXBvREptM1UxMHFxdFZwd2hRZmZrcDNYRHE0SHg4Z2Q3cjlyL2IxY0Nidk1PMlU5ZFhNSnlwSS83YlBLTHJ2TFNnOGpWWkd6Y3M1MnYzZVE5enl3L3hPQnRSVjFxV2l0NnNTTTlCYjBTSEF2SGxmRS9BLzdidGZsM0hwdVczQlltRlhvaEVtZEdIMWhVMXBSZ29LNHNrekE4VjNkWmxEallqRzA4NEtjelFQd1FNUnRISkdmZWFJdW1TRTNlVERmdDA1WWtJVkRIOCs2dXptZmhjaVVzNjhkMkFsdHFlVjdKOXc5WldxVlFEQy9kY0NYNnVEdlU3VW5OMzhaQTZ1WFVhdXVQTHE2VmlNajlmYlpTb3YzT3A4Q3h0WVhYRGRNaUd3THFmc3lZOXFPSTVrUGNUZndlWmJCL29ZWVJtYVVUTzROL04vbGo2WHYwQm8iLCJtYWMiOiI4YzMxMmJjODk1Y2M4ZjY4NDM2ZTZiZDMxMzllMTVmZDdjMjNmMzYwNTFmNDcyMzdlMTA3ODViMjFlZjNhYjI0IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-23688429\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">VloaUQCBjpDKrel7nHeAzB1sTLdTMwteSos1MmAN</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-2136558492 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 17 Jun 2025 13:48:41 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IlU2b1ZWeUxVWnRTa2pFT2hGS0s2Mnc9PSIsInZhbHVlIjoiOUdGamVTdGhKUmdCSENhenVsQzlkeklJUkxpNVR5K0hiNmtvUHpJVnJQdHIwSHI2bVg4MnlnM1Nqc1RMKzhVa0krOHRFTGJISW5YcUJHMHd3UVF2dm1tWFdPZ3B3Vy9TSFk2SEZpZDR0bW9KcUlhRmc4SmRRMlpMWTNIdks5a3ZsQ2RBMnUvL1F4Rmw4dTZzcG9pVXJGWXAzTzVJSjZGaU5CYVNRRTZpZzhvQUpVTUxLUk04SWFYV21SM0t5RzJnTmNCV2dhUWNZclBETVMzSHVFbU5sR00zSTJaWHMxYzg1b0Z4NHlRS01YeXhaSW1pY2pUVmhIZlpIdU9pMWdPT0FsK1d6WVRlVlJxU3psTmcwU3pjdm1BSk1nQ2crOC9FRys0QWluV2drRHNsb3ZsZ08vQUI4bmJVd1owbmxXMlQ3S1ZHbisxcG1LV0w3N2tZTTZtcE50aThFaFNBbm9FUHI4Q2lnRzFWb3hNS2pzN2pWUUcrKzJwUHJ4OFFBQnNINVppWDJDRitkd1JxNkxERDdIWlNyOGE1M3dpSGgyZktiRmMrU0cwc3RhU1ZPSjByM3YyQzdReURtZnp2dGQvMVNtQVdPbjByMEk0d3FoeUl2OTZ4d2tqSmQxQlh3a2ZsRG5KY2NDdU85UmJRUHVKcTkzNE05SlRDNEZqY0pJVEwiLCJtYWMiOiI4YmYyNjAzOTk5MGFkZGJhZGYxZTRlNGU2NzM1YjU0NzNkM2NhNTRlNTMxOTgwYTFlYjg2NzhkMjcyMjNlNjk0IiwidGFnIjoiIn0%3D; expires=Tue, 17 Jun 2025 15:48:41 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6InJybWI5d1RDVnVpUGE4RG40dXpBSXc9PSIsInZhbHVlIjoibzgvRk9HK01INHpSdmZmQzNuRmF3ZTdoZkkrZ0txZ3dxdHA0K2pGMWhObDllMVlQbGYvc2xIaTNaVDVPVUdWOGYwQjZmUm5nTDNSN2l3Wmozc1hYOUZOa0RWVHpRSitONmlFUkttcER6RDRReUx5VUF1bUhRMXN1REtNckU0SDQ2em9HeXVVc0JMMDZvZ2cwbktnOEFGWTZxbGkzMVBPSXkrS09QWll4NjgxNjlqTU9UZjZsNGprRTJqb1RXTHRMRXd6SEVKQ0s3amNrMHdUczc2aDkxQWl6cUpCMWlFMndhaFFpQ1FSNXFjdzdYb1p5VzM0eVNMYnQrRXR1cVdIQWExNHNZLzRmOXhmdm5wdlBML0Q2WVg2THU1MjF4OUdncVNaTXh4TzZxZ1I4MEZORkNHb2pFSFJBalZyTVA4MzNvc2RLdmZEQ3F3OGJlb3U5Z1ZoT3VpbDdzdTg1bVNZRzFISytQb1M2eGhDZ0wzUXFlTnJTc0RmZE5SZGtXQXgwUk9mZ0ZadjRGZDhtVkpHb0EyWmwxeWZ3Z2VRalJYYzBMWXdrU1BYVEZXb1JOcVlHVndoY3N1YTBIZWJBYnA2QzFwTmE2aTRaeFl3L3JTdnRkYTFPTmZIN2ROVGhiaXAwL1k2OERweDZvNzRZb3BBWHJzREpLWFZNTm5yNERXWjYiLCJtYWMiOiJmZmVmZTEwMzY0NzRiYzA0NDliNTIyZWQ3MTk0MDM3NTM2YjI2NzI2NDhhZTM2YjViYTgzMThjNjk4YjRkYzNkIiwidGFnIjoiIn0%3D; expires=Tue, 17 Jun 2025 15:48:41 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IlU2b1ZWeUxVWnRTa2pFT2hGS0s2Mnc9PSIsInZhbHVlIjoiOUdGamVTdGhKUmdCSENhenVsQzlkeklJUkxpNVR5K0hiNmtvUHpJVnJQdHIwSHI2bVg4MnlnM1Nqc1RMKzhVa0krOHRFTGJISW5YcUJHMHd3UVF2dm1tWFdPZ3B3Vy9TSFk2SEZpZDR0bW9KcUlhRmc4SmRRMlpMWTNIdks5a3ZsQ2RBMnUvL1F4Rmw4dTZzcG9pVXJGWXAzTzVJSjZGaU5CYVNRRTZpZzhvQUpVTUxLUk04SWFYV21SM0t5RzJnTmNCV2dhUWNZclBETVMzSHVFbU5sR00zSTJaWHMxYzg1b0Z4NHlRS01YeXhaSW1pY2pUVmhIZlpIdU9pMWdPT0FsK1d6WVRlVlJxU3psTmcwU3pjdm1BSk1nQ2crOC9FRys0QWluV2drRHNsb3ZsZ08vQUI4bmJVd1owbmxXMlQ3S1ZHbisxcG1LV0w3N2tZTTZtcE50aThFaFNBbm9FUHI4Q2lnRzFWb3hNS2pzN2pWUUcrKzJwUHJ4OFFBQnNINVppWDJDRitkd1JxNkxERDdIWlNyOGE1M3dpSGgyZktiRmMrU0cwc3RhU1ZPSjByM3YyQzdReURtZnp2dGQvMVNtQVdPbjByMEk0d3FoeUl2OTZ4d2tqSmQxQlh3a2ZsRG5KY2NDdU85UmJRUHVKcTkzNE05SlRDNEZqY0pJVEwiLCJtYWMiOiI4YmYyNjAzOTk5MGFkZGJhZGYxZTRlNGU2NzM1YjU0NzNkM2NhNTRlNTMxOTgwYTFlYjg2NzhkMjcyMjNlNjk0IiwidGFnIjoiIn0%3D; expires=Tue, 17-Jun-2025 15:48:41 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6InJybWI5d1RDVnVpUGE4RG40dXpBSXc9PSIsInZhbHVlIjoibzgvRk9HK01INHpSdmZmQzNuRmF3ZTdoZkkrZ0txZ3dxdHA0K2pGMWhObDllMVlQbGYvc2xIaTNaVDVPVUdWOGYwQjZmUm5nTDNSN2l3Wmozc1hYOUZOa0RWVHpRSitONmlFUkttcER6RDRReUx5VUF1bUhRMXN1REtNckU0SDQ2em9HeXVVc0JMMDZvZ2cwbktnOEFGWTZxbGkzMVBPSXkrS09QWll4NjgxNjlqTU9UZjZsNGprRTJqb1RXTHRMRXd6SEVKQ0s3amNrMHdUczc2aDkxQWl6cUpCMWlFMndhaFFpQ1FSNXFjdzdYb1p5VzM0eVNMYnQrRXR1cVdIQWExNHNZLzRmOXhmdm5wdlBML0Q2WVg2THU1MjF4OUdncVNaTXh4TzZxZ1I4MEZORkNHb2pFSFJBalZyTVA4MzNvc2RLdmZEQ3F3OGJlb3U5Z1ZoT3VpbDdzdTg1bVNZRzFISytQb1M2eGhDZ0wzUXFlTnJTc0RmZE5SZGtXQXgwUk9mZ0ZadjRGZDhtVkpHb0EyWmwxeWZ3Z2VRalJYYzBMWXdrU1BYVEZXb1JOcVlHVndoY3N1YTBIZWJBYnA2QzFwTmE2aTRaeFl3L3JTdnRkYTFPTmZIN2ROVGhiaXAwL1k2OERweDZvNzRZb3BBWHJzREpLWFZNTm5yNERXWjYiLCJtYWMiOiJmZmVmZTEwMzY0NzRiYzA0NDliNTIyZWQ3MTk0MDM3NTM2YjI2NzI2NDhhZTM2YjViYTgzMThjNjk4YjRkYzNkIiwidGFnIjoiIn0%3D; expires=Tue, 17-Jun-2025 15:48:41 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2136558492\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1773001842 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>16</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1773001842\", {\"maxDepth\":0})</script>\n"}}