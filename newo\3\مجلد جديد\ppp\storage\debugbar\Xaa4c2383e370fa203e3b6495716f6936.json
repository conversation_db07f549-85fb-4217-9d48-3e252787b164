{"__meta": {"id": "Xaa4c2383e370fa203e3b6495716f6936", "datetime": "2025-06-17 13:33:40", "utime": **********.424444, "method": "GET", "uri": "/product-categories", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1750167219.696723, "end": **********.42447, "duration": 0.7277469635009766, "duration_str": "728ms", "measures": [{"label": "Booting", "start": 1750167219.696723, "relative_start": 0, "end": **********.290537, "relative_end": **********.290537, "duration": 0.5938141345977783, "duration_str": "594ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.290554, "relative_start": 0.5938310623168945, "end": **********.424473, "relative_end": 3.0994415283203125e-06, "duration": 0.13391900062561035, "duration_str": "134ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 49310352, "peak_usage_str": "47MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET product-categories", "middleware": "web, verified, auth, XSS, category.access", "controller": "App\\Http\\Controllers\\ProductServiceCategoryController@getProductCategories", "namespace": null, "prefix": "", "where": [], "as": "product.categories", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FHttp%2FControllers%2FProductServiceCategoryController.php&line=203\" onclick=\"\">app/Http/Controllers/ProductServiceCategoryController.php:203-229</a>"}, "queries": {"nb_statements": 6, "nb_failed_statements": 0, "accumulated_duration": 0.0279, "accumulated_duration_str": "27.9ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 16 limit 1", "type": "query", "params": [], "bindings": ["16"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.3365052, "duration": 0.01961, "duration_str": "19.61ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 70.287}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.367965, "duration": 0.00074, "duration_str": "740μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 70.287, "width_percent": 2.652}, {"sql": "select `permissions`.*, `model_has_permissions`.`model_id` as `pivot_model_id`, `model_has_permissions`.`permission_id` as `pivot_permission_id`, `model_has_permissions`.`model_type` as `pivot_model_type` from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `model_has_permissions`.`model_id` = 16 and `model_has_permissions`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["16", "App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 326}, {"index": 21, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 566}], "start": **********.3919022, "duration": 0.0008100000000000001, "duration_str": "810μs", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:326", "source": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php:326", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=326", "ajax": false, "filename": "HasPermissions.php", "line": "326"}, "connection": "kdmkjkqknb", "start_percent": 72.939, "width_percent": 2.903}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (16) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 312}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 26, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}], "start": **********.3957481, "duration": 0.00065, "duration_str": "650μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 75.842, "width_percent": 2.33}, {"sql": "select `product_service_categories`.*, COUNT(pu.category_id) product_services from `product_service_categories` left join `product_services` as `pu` on `product_service_categories`.`id` = `pu`.`category_id` where `product_service_categories`.`created_by` = 15 and `product_service_categories`.`type` = 'product & service' group by `product_service_categories`.`id` order by `product_service_categories`.`id` desc", "type": "query", "params": [], "bindings": ["15", "product &amp; service"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/ProductServiceCategory.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Models\\ProductServiceCategory.php", "line": 116}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/ProductServiceCategoryController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Http\\Controllers\\ProductServiceCategoryController.php", "line": 206}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.403013, "duration": 0.00396, "duration_str": "3.96ms", "memory": 0, "memory_str": null, "filename": "ProductServiceCategory.php:116", "source": "app/Models/ProductServiceCategory.php:116", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FModels%2FProductServiceCategory.php&line=116", "ajax": false, "filename": "ProductServiceCategory.php", "line": "116"}, "connection": "kdmkjkqknb", "start_percent": 78.172, "width_percent": 14.194}, {"sql": "select count(*) as aggregate from `product_services` left join `product_service_categories` as `c` on `c`.`id` = `product_services`.`category_id` where `product_services`.`type` = 'product' and `product_services`.`created_by` = 15", "type": "query", "params": [], "bindings": ["product", "15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/ProductServiceCategoryController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Http\\Controllers\\ProductServiceCategoryController.php", "line": 209}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.411629, "duration": 0.00213, "duration_str": "2.13ms", "memory": 0, "memory_str": null, "filename": "ProductServiceCategoryController.php:209", "source": "app/Http/Controllers/ProductServiceCategoryController.php:209", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FHttp%2FControllers%2FProductServiceCategoryController.php&line=209", "ajax": false, "filename": "ProductServiceCategoryController.php", "line": "209"}, "connection": "kdmkjkqknb", "start_percent": 92.366, "width_percent": 7.634}]}, "models": {"data": {"App\\Models\\ProductServiceCategory": {"value": 26, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FModels%2FProductServiceCategory.php&line=1", "ajax": false, "filename": "ProductServiceCategory.php", "line": "?"}}, "App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 28, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 1, "messages": [{"message": "[ability => create purchase, result => true, user => 16, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1203073669 data-indent-pad=\"  \"><span class=sf-dump-note>create purchase</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"15 characters\">create purchase</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>16</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1203073669\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.401378, "xdebug_link": null}]}, "session": {"_token": "PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "16", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]"}, "request": {"path_info": "/product-categories", "status_code": "<pre class=sf-dump id=sf-dump-776078742 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-776078742\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-1860392986 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1860392986\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-105489880 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-105489880\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-703692397 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1936 characters\">_clck=2gyfy%7C2%7Cfwu%7C0%7C1994; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clsk=1ti4td2%7C1750167218299%7C2%7C1%7Cl.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6InlQZWdrRHVpc2ZHelE0Q1dhYktpOEE9PSIsInZhbHVlIjoiYkN0bzJWYWZiSmxacjZlVUxRYWY3ZGcxbG5jTmtCc3daVU1vT0oyZ1JFVUdNcndjWFBqbDJjYWNNWko0OFpTdEJYdVpFWGxiTGRNeTRiN2dVV1ZMUEQrb0hnbUt4a1dxTVpaeG9iMWU1d3M4d0tnZHdsZVh1aWs4cWlZVGZIMnVUNlB1aUhvajRhVDFvQ3RDVkF3Rm9TL0RwdFpaaWx6VVp4RFkwRm5IWnJMZjZsV0JBR3UrdEhZSkcrZW8vbXlJbkxZMzBpcTE2bENYaDBxa2JlQ0x0M2RWVXE3NDFGWTgySk1HeVEra1UyblFnRnE2cGRXMVB6dnhHWnhjaGR6TEtoblh5eElZR05DdnhIeGJKZ0FRdTY4Si8rR0RhUnR6QzhVMkNnMFdzc2lSVnhYa1MwdmIyM24vSW1mc3NMd2k5c1pubVlRaDBOSWJPVzQ1Q2hEM3ZPV2pPUFhZb3hhcVYyVTZmNEZoUUp5UncwZ1BpTHZCOCtVZFM1L09DVXRsZU5mYjAwMko4WS9jL0R4TG0rcEw4T1h1STczUExIeHFxbFR5c3pwYVc2bHE0TkNNelBCbkI1QUFtSmF1cHZ1MXd1RUNJa09HYjlLNHNIb2NTZ05pKyt6Y2dhd0VSd2oyVnFtYnhIZlQ1Ry9hbGVDSFAxd1lSQUhpdjhFY240MW8iLCJtYWMiOiI4OTI0MDMwZjA4ODE4ZTM2NGQxMTViYWQ4Y2EzMTM1Mjk0NTZhZjcyMGNkYmIxYmZjYzQxMjI4ZGM2ODZhNmQ2IiwidGFnIjoiIn0%3D; test_session=eyJpdiI6IkYzU2tXZTZyNUdxSnY2NVM2bFpOMkE9PSIsInZhbHVlIjoia3ljblh3RkV3R2xlanRmeXZTQTRFOHpxK0g1azYvdURLbHR4b09TZ0hKOGYwcEJWMXBGcEk4YzJXMmdlRFBXb05PWGdxU2QyaVphakRPaWlRTERCeFFNZ21kZ0F4aVhzam4weFlxWkVoU3FWL1EyejJ3ZjZCZzBwa0VCcjAySnZEaEZIc3EzcEN3Z2hYMWJzOE80cGtNNjFuM1ZVYzZLMlIvUHlKRW4remVIL1NkR0FGaFlrRkowSzhzcEJPS3IxU3M2SlZIaDZnSTNoMzZ2S2RUYS9pK1h2a2ZhelI4VzlIMkI5c01MMUZtWGc1aXhGRHpGTm5FQksvT3ZDL0xKYnBKMDAwVTdIRkpWcE0vTFdoSlNEQkV3bFgvSnpMUlF1RURIdXFxaUlPVE1EcWJZQndEbXRqWU1nTlE1c3hXR1dCSTJkdlZHckhPSXh2YzMzUTViZWxkazg3cE5WWG9SSkNZVlh5SzB6Q3AyS0d5Y1hqd213UTNXWi8zTE0vcFJBUldxK2hKRmhCYXhMd3ZEaTZxMVVtT1lLcHNON3AyajlrWjFhU3hRUVJrRHJCL3VjOURkMzhOK3l3UE4zYjQ4ZklBTU40eFBTUm1SYzlrbWhmWGFoRk14TDJOUUpXdEJwQzVGN0RqeTZYdWtOTlJHWFhKR3hQZkR4QnMwSW9BTE4iLCJtYWMiOiI0NTFiYzZjOTJlOThhMTRlYWZkZGIxMTMzNzQyYzkzZThjOTNlYzJhNjk4NzlkYmJkYmRkZWIwZTlmZTc1YmQwIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-703692397\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-196747489 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">VloaUQCBjpDKrel7nHeAzB1sTLdTMwteSos1MmAN</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-196747489\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-32484694 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 17 Jun 2025 13:33:40 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IjM0YWw0OW5ybGgvbUJUNERCd2FhekE9PSIsInZhbHVlIjoidHA0LzRJWk5FakpJZEh5VG9EdTZJSFBBK0JZRm9BNytRQ25Ba1dJUjdkQTRNb3B1V2ZiQ01ZTXJKYUdMc0xtZ2dMdUNYTlB1ODNwYnd1bFQ1WXZzNDRGVzBkMUFQRGg4K1ZYRytjb2hzRVN4RTltbHFlZ3Buajl2akZkWGpxVEQwQndiNXhkQjlOODZsS1I3UThiSytVRTh4TUg3WDVWWEpIL2lHQmpjaW1vYThrRTZPdXZCMjVMbVk2RDZqZS9YWmJKSWFxN2JUR0JjQk1Cd2lwVzJQczlIM1I3SEp2RG9zREZMb2JFaytVVW1VNWQ3ZTY4WThabE5LVHpoVkNBWVNOSVFvT1dQeFlucWhybDQweHJmOEg3N1Yxbkw5ZkR4dXNKaUpLV1UzLzFxaVVuZUZLV0lpa09ZVzNBcXhmQjZ3ejNSS3RRWmt1TDlXaUkzSmNoekVLZTBxZ3pndkVhdEY2OG1KNllYak53QndIUHRmMXpPR0l4bnkwcGdFVkkrTDFOWWJNdWtoWjIySWEwMHk4T0wwbkpqWHBBekdha0hnc2VFcDNkaXR3RnhUVGcyZmtTWXhiNW8rMlBmRjlwcndhL2pDVktXZ3ZkM0lIbFJaZ1h1L3RNbHVwdFJtVDFPRFQ0MXlpN1hBVUF4MEZkU3ZQZUZVbnpFZU01WkR2UFYiLCJtYWMiOiIwOTQ3Y2RmNzMxMzI5ZmFlNTFjMDk0MTdhZjQ5ZGRmNTEwZTUxOTczMmFiNDhmNjg4MWQ0NzBlYTRmOTM4NGFiIiwidGFnIjoiIn0%3D; expires=Tue, 17 Jun 2025 15:33:40 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6ImxSUllWT0R6L2VXRWoybmJmb1R1WkE9PSIsInZhbHVlIjoibmMvNjBVbVJnazE4TW1XOXNOaUNLYlZxcUdiNGtXcnNhMmpPVDhpMEpGM3VTMDJrZ3pzYU5MU2JWajFMbHlXMXBrNUV0M2VyY0htMWJ2eXVYMGRIRkVoeDFtb085VkNqQjJXYjloaGI5T2xTaks5alRhQk90OG5YcVRRL1BXMW1ianlqbDBMYit2REMzVWtESnAvWkNNb2pCdlA4UFE3cjJNajNmTEZXUllib3NCUHlyZGVTdHZSUk84SkovNnFSSjZuN3dkOEY4cVRHd3VhTjhPVCsrbTMva0ZndkI1b1ZnNythZkIrUklqbnR6K0ROUWlmcVU0MmxMQWFVNWJiMjA3bFRtSTJBaHhpQXNhSitPWjlDdWo5QWlQSlJGeHhtSEVpMWVoa0lWN2poV2ZsQ0NlUUlCRWtuSytYQkFhTEpNZlcvSnRkcDdUOVBSUk9VTzlVWTNjbDJ6TDRHay8vRC9RVGs1ekdRS3BiSWoxdUVSNjNyZWhCRENOR2NhdUFnVDZrdlpDL3dCTVV1N0ZDcGtYUDVqak1XSHJwKzg2OHFXQlNmZVJ1ZWlvWDA3NmFGUUhIK0QxN2VRM0xqanNGZ2tJOUZYTEkrQWdtSUNWTjRtM2V1MlpHbzAzS0xpTHpZbHBQUnpIOUlsUGpxbWJuS3VjVG9XSC9oSUZ3c0J2cU0iLCJtYWMiOiI0YjM2NTEzNDYyNzUyNTgyYTU0NGFjNzg3MTkwNTgyNmUwZjg1OWE3ODdhZDdiYTA1NzFmOTgxNzgxNmVkMDBlIiwidGFnIjoiIn0%3D; expires=Tue, 17 Jun 2025 15:33:40 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IjM0YWw0OW5ybGgvbUJUNERCd2FhekE9PSIsInZhbHVlIjoidHA0LzRJWk5FakpJZEh5VG9EdTZJSFBBK0JZRm9BNytRQ25Ba1dJUjdkQTRNb3B1V2ZiQ01ZTXJKYUdMc0xtZ2dMdUNYTlB1ODNwYnd1bFQ1WXZzNDRGVzBkMUFQRGg4K1ZYRytjb2hzRVN4RTltbHFlZ3Buajl2akZkWGpxVEQwQndiNXhkQjlOODZsS1I3UThiSytVRTh4TUg3WDVWWEpIL2lHQmpjaW1vYThrRTZPdXZCMjVMbVk2RDZqZS9YWmJKSWFxN2JUR0JjQk1Cd2lwVzJQczlIM1I3SEp2RG9zREZMb2JFaytVVW1VNWQ3ZTY4WThabE5LVHpoVkNBWVNOSVFvT1dQeFlucWhybDQweHJmOEg3N1Yxbkw5ZkR4dXNKaUpLV1UzLzFxaVVuZUZLV0lpa09ZVzNBcXhmQjZ3ejNSS3RRWmt1TDlXaUkzSmNoekVLZTBxZ3pndkVhdEY2OG1KNllYak53QndIUHRmMXpPR0l4bnkwcGdFVkkrTDFOWWJNdWtoWjIySWEwMHk4T0wwbkpqWHBBekdha0hnc2VFcDNkaXR3RnhUVGcyZmtTWXhiNW8rMlBmRjlwcndhL2pDVktXZ3ZkM0lIbFJaZ1h1L3RNbHVwdFJtVDFPRFQ0MXlpN1hBVUF4MEZkU3ZQZUZVbnpFZU01WkR2UFYiLCJtYWMiOiIwOTQ3Y2RmNzMxMzI5ZmFlNTFjMDk0MTdhZjQ5ZGRmNTEwZTUxOTczMmFiNDhmNjg4MWQ0NzBlYTRmOTM4NGFiIiwidGFnIjoiIn0%3D; expires=Tue, 17-Jun-2025 15:33:40 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6ImxSUllWT0R6L2VXRWoybmJmb1R1WkE9PSIsInZhbHVlIjoibmMvNjBVbVJnazE4TW1XOXNOaUNLYlZxcUdiNGtXcnNhMmpPVDhpMEpGM3VTMDJrZ3pzYU5MU2JWajFMbHlXMXBrNUV0M2VyY0htMWJ2eXVYMGRIRkVoeDFtb085VkNqQjJXYjloaGI5T2xTaks5alRhQk90OG5YcVRRL1BXMW1ianlqbDBMYit2REMzVWtESnAvWkNNb2pCdlA4UFE3cjJNajNmTEZXUllib3NCUHlyZGVTdHZSUk84SkovNnFSSjZuN3dkOEY4cVRHd3VhTjhPVCsrbTMva0ZndkI1b1ZnNythZkIrUklqbnR6K0ROUWlmcVU0MmxMQWFVNWJiMjA3bFRtSTJBaHhpQXNhSitPWjlDdWo5QWlQSlJGeHhtSEVpMWVoa0lWN2poV2ZsQ0NlUUlCRWtuSytYQkFhTEpNZlcvSnRkcDdUOVBSUk9VTzlVWTNjbDJ6TDRHay8vRC9RVGs1ekdRS3BiSWoxdUVSNjNyZWhCRENOR2NhdUFnVDZrdlpDL3dCTVV1N0ZDcGtYUDVqak1XSHJwKzg2OHFXQlNmZVJ1ZWlvWDA3NmFGUUhIK0QxN2VRM0xqanNGZ2tJOUZYTEkrQWdtSUNWTjRtM2V1MlpHbzAzS0xpTHpZbHBQUnpIOUlsUGpxbWJuS3VjVG9XSC9oSUZ3c0J2cU0iLCJtYWMiOiI0YjM2NTEzNDYyNzUyNTgyYTU0NGFjNzg3MTkwNTgyNmUwZjg1OWE3ODdhZDdiYTA1NzFmOTgxNzgxNmVkMDBlIiwidGFnIjoiIn0%3D; expires=Tue, 17-Jun-2025 15:33:40 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-32484694\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>16</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n"}}