{"__meta": {"id": "Xaf5eed22ccc27600dac6c9caa0c9cd8d", "datetime": "2025-06-17 13:51:55", "utime": **********.086643, "method": "POST", "uri": "/event/get_event_data", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1750168313.85068, "end": **********.086679, "duration": 1.2359988689422607, "duration_str": "1.24s", "measures": [{"label": "Booting", "start": 1750168313.85068, "relative_start": 0, "end": 1750168314.97087, "relative_end": 1750168314.97087, "duration": 1.120189905166626, "duration_str": "1.12s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": 1750168314.970888, "relative_start": 1.1202077865600586, "end": **********.086683, "relative_end": 4.0531158447265625e-06, "duration": 0.11579513549804688, "duration_str": "116ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 46458104, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET event/get_event_data", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\EventController@get_event_data", "namespace": null, "prefix": "", "where": [], "as": "event.get_event_data", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FHttp%2FControllers%2FEventController.php&line=317\" onclick=\"\">app/Http/Controllers/EventController.php:317-345</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.018019999999999998, "accumulated_duration_str": "18.02ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 16 limit 1", "type": "query", "params": [], "bindings": ["16"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.0332599, "duration": 0.015359999999999999, "duration_str": "15.36ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 85.239}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.064328, "duration": 0.0010500000000000002, "duration_str": "1.05ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 85.239, "width_percent": 5.827}, {"sql": "select * from `events` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/EventController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Http\\Controllers\\EventController.php", "line": 327}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.071034, "duration": 0.00161, "duration_str": "1.61ms", "memory": 0, "memory_str": null, "filename": "EventController.php:327", "source": "app/Http/Controllers/EventController.php:327", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FHttp%2FControllers%2FEventController.php&line=327", "ajax": false, "filename": "EventController.php", "line": "327"}, "connection": "kdmkjkqknb", "start_percent": 91.065, "width_percent": 8.935}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "16", "_previous": "array:1 [\n  \"url\" => \"http://localhost/account-dashboard\"\n]"}, "request": {"path_info": "/event/get_event_data", "status_code": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1936 characters\">_clck=2gyfy%7C2%7Cfwu%7C0%7C1994; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clsk=1ti4td2%7C1750167218299%7C2%7C1%7Cl.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6Ijk5eE5PcThPRE8wY3hlYVR5NkZ6eGc9PSIsInZhbHVlIjoiOG1OU0w0QXRtMlphTTVybzU5Q0hRS3BjRFY2S2ViTmJjOGlFZTJnejErSlRWNU43UnA5MWp4MitUcDRPSFhjSTlGYTN5aDZKamduS1MrK3QvRlh5ZUhRdnltWlhKUnIyTTF3YVBxdWo2OEY5aEY2bWwrUGsybFAxNXFRemd4K3lyaS9jS3NOSFhVYlJvRVNqOUtvTUkxaVRGeXVmdllSV043SUtNakJxaVNrTVRHUHNPTkUzMWhYd1dBb1hTWTFPRDFvTGwrTktPNG9uRHRpeEFuS3pDdFFOT21DQjcxemcyVDQvWHVxVDNUdkNFZS9xQTg1YlB2K0EzaXRRT0NZcWdDUlZXcjIwbzg1QzZ6ZjBvanJLVnJ6WWFNcWxZSjBDOFlPTmJuWG9DOG84RFdPSXlOKzNncW40VDV0UXFFS0dkWFNaYlNvbE1uQ1VqcDFlNzUyOFVHVjg2QWhuL0JqQ2ozTEQvajZaVzYvcFY4YlhtSnRPeUViSkxZYzhWUUxKMXZ1MUlndWpjMXhJVXZ4dHNkaFY5WUVWOWtURndiNXlJeGk2TXJNL3R5b0k2L1BsV1gwMG55OW1jT0daV1lINXFFMDR5c0ltWlpjb1R5b1ZDdkZkR0dxblZiWFY1Qi9NMmVhOGhpUHZCRkFGWXlaeTJBNDZ1S3E0VXQ5OHllak0iLCJtYWMiOiJlN2FiYzY4Y2VlNTliNDllZTc4YTZmMjliZTVlMTgzM2Y3OTIxZDI0YWNhZjEyYzhiZGY5MjdmNzMwNGI2MmNmIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6Ik5sQWx2TUQ5c01GVWJTd05sNnRyV2c9PSIsInZhbHVlIjoiNW5mTk5VcXp0SWZOdk9UbUtsRTl3NExUYmR4SC9lTzB5cUpRd2lHKzZ6Ylorb0xEQ2prY2pYR3dzMkc5aEl0YVA5dXI4RnJSeTF6am1VaU9veTBkSVFqZEtZVzZnN21xL3hUMUg3VFFuc3ZBZDY3ZEJLaDl0RUx6U3UrMkVIYUtKRE0wRDFuU1ZGOUdGZ2dTUC9uYVFQUXRGY1pkbVU2Rm9lL0dYcnkvSThTQjNhWUsyL0FiUmNaRjVXNUpucFVvMzg4RzI0WmVvRHYxQkRVYW1qYlFBcXVadnk1MWR3SUg1QmNNZ1dLdEtNMHVkaHlpL3V6NE0rbmVGb296T29uRk1adzZLVTV0SlJqOXR2bnJaZGRYNHRSOWgwZEozYW5kaXV3OFYyV2NqY0ZmOStYTGFMMU9yR2NkQktHUHdPNVdGZmg2d08vc1UrdStYRDFheFA4Snk3VkVwZ3A3cFJINm5nWXZvUjN0Mm1Bc2Z3cDRRWWZuYkk4QW9nVm54akg1T0JZWVRQR25JSnptNzdBZkVsSitHNXA0dHNIMHE2cFRENURobTZ3S2duWCtvU1k4d2xyMkhMa295Zm5NZnRTOEw0dzJ3MUM1dGw5SHR2SmtvTnVwTUtlNnQzdEU1TkUxZm9mREVDT21SQnNiZ1pObTNRUC90STFYUFViZUgrZ2kiLCJtYWMiOiI0NGQyNTUwZmYxYmE5ZmM5MGE4ZWM0MGU4OGQyYWU0YTcwMTRkOWE3MjQ5ODNkZmMwZDVjYWI2OTZmOWNjNzYxIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1145222813 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">VloaUQCBjpDKrel7nHeAzB1sTLdTMwteSos1MmAN</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1145222813\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1914763986 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 17 Jun 2025 13:51:55 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IkxyeEhSVWEwVEZLcWhsV1NzUHdLU3c9PSIsInZhbHVlIjoiWHZxQjNuNTV3WmdjTXRWdnpNMXp4WExHdlhyYStaMTliVnlQN05ubkVrdkVBMEJkMXRiS0JvSTBxSEhBZzBtcWM0UFFlZGJhZFBxdVdLSVg5RVVEZUdmMmlZdjQzd2xpNHlxRUVpK0Z2aWdHN3k3a2JIM1hzdlBoUjFOc0V6Y2NneWVoZW5GWUNwbm1IUnRTTkhnY1loYkxPOW5tTUV4amN1VTNPTW5tVzYwSm5wRXIyWDU3YU9USXJJeGpxeTdZbjZzRmh5azlyUjZlZEhPajZ0cXgzQndXKzBCQzFEdnd6cWdYK2treXVBcjFVSklyM1M0OWFmdmpnbm9IRUpGTUlnanh4NTRwZ1dIMDF1NlcwWGJIQVF0VVkrbE1YQVFwUkdZd2hvQUU1UzZMMG5DRndsSDIxZkZuK3htWWI5cCtxZXFtTHpTWEU4RlhZcHJZR2hyd0hHY1VraWtiZ3lqWDZlOWRKbFFTVGRwT2dqWWlIWm9iZFA0RWtFQzVmTm1TNlpLUFQ1T0JvVFV5ZThidHQrOGNjZjZDTUtyWTNlWUgwWTR1QklEdXkvUHNEMHlZT0ZnVjh6YzBVZWF2c2dleXlFUm9wUE5nWmVNOUtmbzJ4Y2RKWlpjMjFucGg0K082Ni9zUWUyUUNvNkVheDZLR2wzQWJHNXBMWUFpS3dBS2EiLCJtYWMiOiIyZTMwOGVlOGRjOTk0YWUwZDY5OGZlZGRjZDcxNTk4ZjQ5MDI1NmRhMzc5NzFkYzY0NjY2ZjRkZTY5OTViNzRjIiwidGFnIjoiIn0%3D; expires=Tue, 17 Jun 2025 15:51:55 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6InVPZ1BJMTBodTBCZXc1UWtGNDRza2c9PSIsInZhbHVlIjoid3dJZEcvcG53TjBtaE1mTUtQVUJEZStsTEp4U3BtNWc3aFJZeDRTc1REamZlRkZZc1ZlbDNuTFBlNy9JNUpXM3RHbmFtZkhILzlId3UxZ2psQnpqRHBJRlR5WE82d2ZmZmkvNDAveTFrQmJBelpRRXU3UElwN1FjMHNvU3Jia1BNVCttRU4wbTl4UDdVT3dpeWpReEdpQnd1OFVUOUFsc0xxTVpIT2ZYRlMwL1JlSGszQlhuN3RpVFhKMXIyY2hGYmdqSlhKWDk1Smk5SUdHSmpNM0J1dEo4ZnRLZW9CbFR6c2loTmJrVEQ4bldoTDBSMUIzc0NNYk1CK2xJWG1OT1NXYllPVWl4dmhYMy9raGxqbjM2eXk1czh3RWs1OElxZmlOYTllTXZ3OWIrMUhKQ3M5RHNXc1Y1N0Q0dy9Yc2wrUUQ1RDdXSUJOemoveTJnMllHakx6WTFQSmFxMlFhdHpKOWJrZU1DdzNqQ0hGV3NXR3BWQzFrK1ZWU2hIOEFKZUtrVkdvaGRkM3R1RE5ScjBKMW9pd3N0RkswcDdFeE1LU1Z4bDFzM2tGSUpEM2U4ZFdPUFlNblVqalQzTllNOGZMR3p5YW1ZTzlsYmVhbjNHcmI2enJ3VC9KSnE4MGVDbVZJOEw0QjAwK2s3RlQwYnV6dHk1VDRiYU9PRGRHK3MiLCJtYWMiOiIwZTMyNmMwMjAxZmI1NTIwY2Q3NTMyYWRjNTQyYzc1ZGYyNDVlZjg0OGIzZmZiNzg5N2Y3NTE0NTI3ODQ2NjM0IiwidGFnIjoiIn0%3D; expires=Tue, 17 Jun 2025 15:51:55 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IkxyeEhSVWEwVEZLcWhsV1NzUHdLU3c9PSIsInZhbHVlIjoiWHZxQjNuNTV3WmdjTXRWdnpNMXp4WExHdlhyYStaMTliVnlQN05ubkVrdkVBMEJkMXRiS0JvSTBxSEhBZzBtcWM0UFFlZGJhZFBxdVdLSVg5RVVEZUdmMmlZdjQzd2xpNHlxRUVpK0Z2aWdHN3k3a2JIM1hzdlBoUjFOc0V6Y2NneWVoZW5GWUNwbm1IUnRTTkhnY1loYkxPOW5tTUV4amN1VTNPTW5tVzYwSm5wRXIyWDU3YU9USXJJeGpxeTdZbjZzRmh5azlyUjZlZEhPajZ0cXgzQndXKzBCQzFEdnd6cWdYK2treXVBcjFVSklyM1M0OWFmdmpnbm9IRUpGTUlnanh4NTRwZ1dIMDF1NlcwWGJIQVF0VVkrbE1YQVFwUkdZd2hvQUU1UzZMMG5DRndsSDIxZkZuK3htWWI5cCtxZXFtTHpTWEU4RlhZcHJZR2hyd0hHY1VraWtiZ3lqWDZlOWRKbFFTVGRwT2dqWWlIWm9iZFA0RWtFQzVmTm1TNlpLUFQ1T0JvVFV5ZThidHQrOGNjZjZDTUtyWTNlWUgwWTR1QklEdXkvUHNEMHlZT0ZnVjh6YzBVZWF2c2dleXlFUm9wUE5nWmVNOUtmbzJ4Y2RKWlpjMjFucGg0K082Ni9zUWUyUUNvNkVheDZLR2wzQWJHNXBMWUFpS3dBS2EiLCJtYWMiOiIyZTMwOGVlOGRjOTk0YWUwZDY5OGZlZGRjZDcxNTk4ZjQ5MDI1NmRhMzc5NzFkYzY0NjY2ZjRkZTY5OTViNzRjIiwidGFnIjoiIn0%3D; expires=Tue, 17-Jun-2025 15:51:55 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6InVPZ1BJMTBodTBCZXc1UWtGNDRza2c9PSIsInZhbHVlIjoid3dJZEcvcG53TjBtaE1mTUtQVUJEZStsTEp4U3BtNWc3aFJZeDRTc1REamZlRkZZc1ZlbDNuTFBlNy9JNUpXM3RHbmFtZkhILzlId3UxZ2psQnpqRHBJRlR5WE82d2ZmZmkvNDAveTFrQmJBelpRRXU3UElwN1FjMHNvU3Jia1BNVCttRU4wbTl4UDdVT3dpeWpReEdpQnd1OFVUOUFsc0xxTVpIT2ZYRlMwL1JlSGszQlhuN3RpVFhKMXIyY2hGYmdqSlhKWDk1Smk5SUdHSmpNM0J1dEo4ZnRLZW9CbFR6c2loTmJrVEQ4bldoTDBSMUIzc0NNYk1CK2xJWG1OT1NXYllPVWl4dmhYMy9raGxqbjM2eXk1czh3RWs1OElxZmlOYTllTXZ3OWIrMUhKQ3M5RHNXc1Y1N0Q0dy9Yc2wrUUQ1RDdXSUJOemoveTJnMllHakx6WTFQSmFxMlFhdHpKOWJrZU1DdzNqQ0hGV3NXR3BWQzFrK1ZWU2hIOEFKZUtrVkdvaGRkM3R1RE5ScjBKMW9pd3N0RkswcDdFeE1LU1Z4bDFzM2tGSUpEM2U4ZFdPUFlNblVqalQzTllNOGZMR3p5YW1ZTzlsYmVhbjNHcmI2enJ3VC9KSnE4MGVDbVZJOEw0QjAwK2s3RlQwYnV6dHk1VDRiYU9PRGRHK3MiLCJtYWMiOiIwZTMyNmMwMjAxZmI1NTIwY2Q3NTMyYWRjNTQyYzc1ZGYyNDVlZjg0OGIzZmZiNzg5N2Y3NTE0NTI3ODQ2NjM0IiwidGFnIjoiIn0%3D; expires=Tue, 17-Jun-2025 15:51:55 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1914763986\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>16</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n"}}