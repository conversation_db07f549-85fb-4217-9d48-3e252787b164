{"__meta": {"id": "Xb4a618900271b85055ff6a4ce2cca9f0", "datetime": "2025-06-17 13:55:02", "utime": **********.217363, "method": "POST", "uri": "/chats/favorites", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1750168501.522795, "end": **********.217396, "duration": 0.6946010589599609, "duration_str": "695ms", "measures": [{"label": "Booting", "start": 1750168501.522795, "relative_start": 0, "end": **********.105362, "relative_end": **********.105362, "duration": 0.5825669765472412, "duration_str": "583ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.105379, "relative_start": 0.5825841426849365, "end": **********.217399, "relative_end": 2.86102294921875e-06, "duration": 0.11201977729797363, "duration_str": "112ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 46117960, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/favorites", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getFavorites", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "favorites", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=454\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:454-477</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.017929999999999998, "accumulated_duration_str": "17.93ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 16 limit 1", "type": "query", "params": [], "bindings": ["16"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.1588929, "duration": 0.0157, "duration_str": "15.7ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 87.563}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.189933, "duration": 0.00109, "duration_str": "1.09ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 87.563, "width_percent": 6.079}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (16) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.200677, "duration": 0.00114, "duration_str": "1.14ms", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 93.642, "width_percent": 6.358}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "16", "_previous": "array:1 [\n  \"url\" => \"http://localhost/hrm-dashboard\"\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/favorites", "status_code": "<pre class=sf-dump id=sf-dump-188258922 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-188258922\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-933094760 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-933094760\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1604580480 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1604580480\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-488460464 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"30 characters\">http://localhost/hrm-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1936 characters\">_clck=2gyfy%7C2%7Cfwu%7C0%7C1994; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clsk=1ti4td2%7C1750168386913%7C7%7C1%7Cl.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6ImRRMHJZUjdOYnphbG8ydEVKNmxrVUE9PSIsInZhbHVlIjoiWm8wSkdxTVZwbjRFWVBGeEpLajROYWJ6RURHMzNWRFNKMTFrbFdYZE9QZjF3NEJyN2p5Y3phcHhuQ3JzaG5WQnJiWUdOVXNqdjNyVk5RRzh2dnIxUmxYazFoT2p4YlQ5WVFtS1VpZVlqMnRjbFhVNGRlbW5QM1lSY3kzOGZXN1hKaENuR3JHSFl6VHczclNIT1E5Nmt6ZXV2L2paQ1hYVXMwZlR0V1JBWldOS1NNTnpidnR2SEY2UVNTZlAzd1B3Mk9nQUx2R29ZWjNOTDdWRzRFbWlZUXhZNHpwWlVmdzl0dWZvMDBXWGIzVkp2N3grQXZGM2FsczA4MHlJUGFFWUZKYzl3cG5oMXJwV2tBendsaGZXaUplQjYwOTdPUDJReWpWZGE0MVlDV0RnMHpreW1NVERUS0ZWdWU0QXVNWW9lVjc2WDduV2VpQmtpVEJtOW93cGxGVUx1c24zZGUxVWtzdkc4MktBRkMxczdyQWxZY0lSMVpGWm9oeVVZTC9iUktnWjZFajR4UlkwdHdaMGdnaElTS3pwZTR0WmtwNUxRdVpZWk1xaFZaSytXSnZxcklGWndheGJhcGJaL2FoMlZMTnRJU0REQnE1ZHhxVVhMYnFRRFBnWWk5SXhIVTZKMm55Znlic2hXbVgwclVEcUp6S1BlN1hQVTZla3BGd2UiLCJtYWMiOiIxOWJkN2I3MGQwZTBlM2I4YzYzMzI3NzE4YmE3OGEyMjc5NzMwYjE5NGNhZGEwZTQ2YTRlNzk3YmJmN2RjODc3IiwidGFnIjoiIn0%3D; test_session=eyJpdiI6IkdiZjJQVlNPdVQvTlBob1BZWFdMZWc9PSIsInZhbHVlIjoib0RlOEZlTFA0enZ5Tm9zNTNDbklCbk9sSCsvY3lSUHZJUEtBMVFtbFd1bHQ2VldsVGNnUEFFZFdWdWlGamRYa2MxcmY3ZytCM01yaEhhaFExM3kxQWQzcDFoc2pIelB5ZUx3WGhTb1NJY3ovRUR6OU8zM0ttSkxQamVBQ1FMNnZ1aXFVTElPYjBoQUpYdDlmZlFtdG9zNXE3aVpIYk5RTmd3dmh3ZENsWVFYa0JPY2tEYTFhVjhIeW5DcjVsRWFXanMzcTRIN2pyUmZBSUd5aU1yWU5xRFFIbFhkTFBHeXhORGJoZURRZDJXeXJnZTYwWjVWOVVIMlk5Nlh5YklQYk5kejllSDlxODh5YVhZRzlUYWQzZVZBRGVyK2F0dFVNMXduOWRaa1duSWZWVnRTUGRZNERsdUZtZmx5RGV5cVp0eGpFdzhnT3F2ektHcm5rbjBITHhzVHRqVU9jdU14QUQ0QWR0cFFraCsrRmJTdmpBOW03VmNvRUV2L29QWW1nOW1kS3dVd0ExTHFHcjhNMk1YdWdNaUFKQUtTRHhtaDRvaUF2Y3dsbHVCSjZLVVBhcW9HR3BQN3dpbytKRVpEcHpuS3d2Q2s2K0M1ajFlVitVaUtOeGZ1NHVUcW9GaWdzTlVFRkNsMElSUm00M29abDdvWW5mQXN0ajVmVVRydlUiLCJtYWMiOiIwMDUyZTU3NDFhMjgzYzYyYjhjNWY5MTY5NjgyZDBkMTQ1MWQ2OTcxZDBjM2Q0ZDU4NjhiOGFmNzk1MmUzZjM5IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-488460464\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1512622371 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">VloaUQCBjpDKrel7nHeAzB1sTLdTMwteSos1MmAN</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1512622371\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1335176068 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 17 Jun 2025 13:55:02 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IlN1WHR6dHFLRWRGaTE5Z2VjMlVkd0E9PSIsInZhbHVlIjoidU40cTBwVzEvSXRWUVFuS3FCRk5uN1hZZ0Z6REJuYjRzVVl5cHAyaWtQTTY4NVRyb2NTQzM3RmpLajdiaUd6TE16VVh2MTdtaHNYTWptRytYWWY1aEV1dTNQbUsvaEI5SjRUaGxMN1BPQjJBU29jMTF5MUoyQzN0a3pRdlllQzc4T0h6Sm10QkxicksvNTFjUHY5anhkaTJ4VCtZYmJJR2JEWVR0MFZpSWNaUEFFbzU2eGRsT1llS3NEWFN2Smcrd1ZSTm4rTzN5L2JwV0dJcE5iVWtRZlpWTk5QeFJmeHp0MmQrSHhqc0JJdnhoOTNnNjJEb1lROUErdzFRMnhwS0lSVnlnSEtYSld0OHkyaTh0SHorZWxsczdodm05TkZQUHBRYkVKekNsZmhDTnNDVUN2Z2xUMlFDbDVmWTdZc1JDMXJyam0yVWhzSFlHZFlESEphRzV2eWJhenBlZkxrQ1liSXd6M2lqc1kvV1ZGcWtvTjlXeFdOQTF1VEpxM3U1UCtOUStaSE00YW1FMmRtajYvblNESG9sYU42ZGp0dkpia0dwZTZrWmtnSkxLOFNuMVR3a3NIcTFyNk1hYjBtbGtNeFRscGVTeU1KUkoybXlSM3ZLU2tRWHFMb3pSODd2MlFtMVRFT3FaeDkwZktsaGlNRDFWRDQrcGV4VHFOejEiLCJtYWMiOiI5OWE2MzhhZjkxNDE3MDMwNzI4OWI5ODBkZmU0Mzc1YTVjZDZkYWM0NDBhMDRiYmY2N2U1ODIzMjYxNDYxZWVjIiwidGFnIjoiIn0%3D; expires=Tue, 17 Jun 2025 15:55:02 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6IkdXOFhJajhGYUtTUlVSeUFZbFZYWXc9PSIsInZhbHVlIjoiNnptWTVOSEhhKzFGMXpTZDVlNEl2TWt0WW9TWWJVTDNoLzJ5RlJ3dEJZczgvc2x0RFJtWDk0a2dLbk9zNmpGQWt2QzdlMmpzN3VLbG1yOHZTMlI1cWxIWnFYUE9hczVlSjBMRkhHN2tFcXQzRDZpM3BYZGJ0bzN1Z2dKdXlFeklmN0NXMVZOaXY4a0p1UFJ4aGVUdmp2K1JMNGZZTWxqZ3BYR1lUVlUxMTVmWG5YMUFONjRoY1FQTVNYOFZPS1VoMi9Nc3A5U21rQVhFNk4ycnBBUTNvUDdBVmw0TWdiRGxJcjhsT1JLRkxzU3BCem9GcHJibFRJN3ZaWmNna2ozVWIwTnhia2p4cm00ME9ZYXFIVDNDa01yVVZiZTJlaGQwZkdxYTdpMVNIemxHWXJZT1l0M3EzR1h2dDJVWGRHN3VkdDF1ZDJRTnBLcU13c08yaWQycER2bEpMbjZyQnhyb2tFOVBCVGZSMVR1dzVqQVZnd0IwMktlUy92QUoxVXRyNWdRd3FRNHpMd0YyNitQZkoyODQzQk5tZitFaFpEckE4NGh6ZisvcE5UQmZkem90Vm0zaFo4eGxoTitaeFJEQ3RzT0l3dlQveEZISnJFbnpzME1yZW5ZczlVeE1ER2UzakdZSHVLQ0psaUNUK3QvQWZsaHhFSGU2QXdrOXV6UlUiLCJtYWMiOiI1YTk1MTEzOGQ5YjExOTRjMTFmYzZjZDEzYmMzOWIxMTk5YmJhZDA1ODBmYWUxZDcwZjY5ZTliMjBhMmM5MjFjIiwidGFnIjoiIn0%3D; expires=Tue, 17 Jun 2025 15:55:02 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IlN1WHR6dHFLRWRGaTE5Z2VjMlVkd0E9PSIsInZhbHVlIjoidU40cTBwVzEvSXRWUVFuS3FCRk5uN1hZZ0Z6REJuYjRzVVl5cHAyaWtQTTY4NVRyb2NTQzM3RmpLajdiaUd6TE16VVh2MTdtaHNYTWptRytYWWY1aEV1dTNQbUsvaEI5SjRUaGxMN1BPQjJBU29jMTF5MUoyQzN0a3pRdlllQzc4T0h6Sm10QkxicksvNTFjUHY5anhkaTJ4VCtZYmJJR2JEWVR0MFZpSWNaUEFFbzU2eGRsT1llS3NEWFN2Smcrd1ZSTm4rTzN5L2JwV0dJcE5iVWtRZlpWTk5QeFJmeHp0MmQrSHhqc0JJdnhoOTNnNjJEb1lROUErdzFRMnhwS0lSVnlnSEtYSld0OHkyaTh0SHorZWxsczdodm05TkZQUHBRYkVKekNsZmhDTnNDVUN2Z2xUMlFDbDVmWTdZc1JDMXJyam0yVWhzSFlHZFlESEphRzV2eWJhenBlZkxrQ1liSXd6M2lqc1kvV1ZGcWtvTjlXeFdOQTF1VEpxM3U1UCtOUStaSE00YW1FMmRtajYvblNESG9sYU42ZGp0dkpia0dwZTZrWmtnSkxLOFNuMVR3a3NIcTFyNk1hYjBtbGtNeFRscGVTeU1KUkoybXlSM3ZLU2tRWHFMb3pSODd2MlFtMVRFT3FaeDkwZktsaGlNRDFWRDQrcGV4VHFOejEiLCJtYWMiOiI5OWE2MzhhZjkxNDE3MDMwNzI4OWI5ODBkZmU0Mzc1YTVjZDZkYWM0NDBhMDRiYmY2N2U1ODIzMjYxNDYxZWVjIiwidGFnIjoiIn0%3D; expires=Tue, 17-Jun-2025 15:55:02 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6IkdXOFhJajhGYUtTUlVSeUFZbFZYWXc9PSIsInZhbHVlIjoiNnptWTVOSEhhKzFGMXpTZDVlNEl2TWt0WW9TWWJVTDNoLzJ5RlJ3dEJZczgvc2x0RFJtWDk0a2dLbk9zNmpGQWt2QzdlMmpzN3VLbG1yOHZTMlI1cWxIWnFYUE9hczVlSjBMRkhHN2tFcXQzRDZpM3BYZGJ0bzN1Z2dKdXlFeklmN0NXMVZOaXY4a0p1UFJ4aGVUdmp2K1JMNGZZTWxqZ3BYR1lUVlUxMTVmWG5YMUFONjRoY1FQTVNYOFZPS1VoMi9Nc3A5U21rQVhFNk4ycnBBUTNvUDdBVmw0TWdiRGxJcjhsT1JLRkxzU3BCem9GcHJibFRJN3ZaWmNna2ozVWIwTnhia2p4cm00ME9ZYXFIVDNDa01yVVZiZTJlaGQwZkdxYTdpMVNIemxHWXJZT1l0M3EzR1h2dDJVWGRHN3VkdDF1ZDJRTnBLcU13c08yaWQycER2bEpMbjZyQnhyb2tFOVBCVGZSMVR1dzVqQVZnd0IwMktlUy92QUoxVXRyNWdRd3FRNHpMd0YyNitQZkoyODQzQk5tZitFaFpEckE4NGh6ZisvcE5UQmZkem90Vm0zaFo4eGxoTitaeFJEQ3RzT0l3dlQveEZISnJFbnpzME1yZW5ZczlVeE1ER2UzakdZSHVLQ0psaUNUK3QvQWZsaHhFSGU2QXdrOXV6UlUiLCJtYWMiOiI1YTk1MTEzOGQ5YjExOTRjMTFmYzZjZDEzYmMzOWIxMTk5YmJhZDA1ODBmYWUxZDcwZjY5ZTliMjBhMmM5MjFjIiwidGFnIjoiIn0%3D; expires=Tue, 17-Jun-2025 15:55:02 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1335176068\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1370705961 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>16</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"30 characters\">http://localhost/hrm-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1370705961\", {\"maxDepth\":0})</script>\n"}}