{"__meta": {"id": "Xb7331c44bbb4a3ce9f077b0148af5ce5", "datetime": "2025-06-17 13:53:00", "utime": **********.775879, "method": "POST", "uri": "/event/get_event_data", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.120054, "end": **********.7759, "duration": 0.6558458805084229, "duration_str": "656ms", "measures": [{"label": "Booting", "start": **********.120054, "relative_start": 0, "end": **********.67543, "relative_end": **********.67543, "duration": 0.5553760528564453, "duration_str": "555ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.675447, "relative_start": 0.5553929805755615, "end": **********.775903, "relative_end": 3.0994415283203125e-06, "duration": 0.10045599937438965, "duration_str": "100ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 46443248, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET event/get_event_data", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\EventController@get_event_data", "namespace": null, "prefix": "", "where": [], "as": "event.get_event_data", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FHttp%2FControllers%2FEventController.php&line=317\" onclick=\"\">app/Http/Controllers/EventController.php:317-345</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.019860000000000003, "accumulated_duration_str": "19.86ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 16 limit 1", "type": "query", "params": [], "bindings": ["16"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.7226782, "duration": 0.01785, "duration_str": "17.85ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 89.879}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.756794, "duration": 0.00086, "duration_str": "860μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 89.879, "width_percent": 4.33}, {"sql": "select * from `events` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/EventController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Http\\Controllers\\EventController.php", "line": 327}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.764238, "duration": 0.00115, "duration_str": "1.15ms", "memory": 0, "memory_str": null, "filename": "EventController.php:327", "source": "app/Http/Controllers/EventController.php:327", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FHttp%2FControllers%2FEventController.php&line=327", "ajax": false, "filename": "EventController.php", "line": "327"}, "connection": "kdmkjkqknb", "start_percent": 94.209, "width_percent": 5.791}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "16", "_previous": "array:1 [\n  \"url\" => \"http://localhost/hrm-dashboard\"\n]"}, "request": {"path_info": "/event/get_event_data", "status_code": "<pre class=sf-dump id=sf-dump-203775531 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-203775531\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-849212782 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-849212782\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1248483791 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1248483791\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1235987258 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"30 characters\">http://localhost/hrm-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1936 characters\">_clck=2gyfy%7C2%7Cfwu%7C0%7C1994; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clsk=1ti4td2%7C1750168377489%7C4%7C1%7Cl.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IjlGZkQzMlJGdzcxc3ZlSCtFRUlRSHc9PSIsInZhbHVlIjoiMWVRc21sSDczdTZaZlgyOVA3eXRVcFBrTHB1MVlNeXR1VUhpYmt4Q3JycDZNY0Q5L1ZkMVRNcjU4M3dHZ2xuN0V3NEFFbWVMb1ZqVjJyM0JTRmFUejZieGJBVk9rZHNTVHV0M0s2VDBadmVEN2FBVWtLOExmYTdjZ1lMVDQrU21SMnhONDZuY0haZnVtUHVGVkdaTElxUkcyVDRlc2dYWTZZSzJRdGNmdSszWFpVWUFYY2cvRWZFdnluMWRqa0hjdWM2SVFwRWZTaER2SG1ObEUzbDdsUkNCYXB4NUJGV2xHWTMraGRCQ2VpNGYxRFAzU2lLa1dsZ21oNzZteHdVdjkvTVZKTC9nemNZcSt5SXprSmNRSlhFOVBHQnhGNVhLUG0wZHRmMGNVVGZJbG9LS2N4THVqYnp2OGJOMzhRMjBtRTJkVTBPYzhlcFJ4bHVITUEyR1lPRm1sd2kwVHk1amJCeHdIK3d6c2FBMzZyZ1ltc2lxV2ZQVENrQldhN0RPdFNLd3dOSnJtZ01VcHlzZVc1UU55M2w0MFlSWDVXdEdKNGFFN2c0QjNZaTdPdVRMVWNDUVdxNjM3Yy9VMmhOQlhEeElMNHpEcHU2WHBLcE93NVJvdDV0WWZ0aWlseGZtSEkyaVNiMkNLQ3lWWHU5blZXMzF4Uk9ONWVzMkc0ZEsiLCJtYWMiOiIyODIxNTdmNmNmMWQzZjMzODUwMjg3Yjc5MTVkMDZmODgwOTBiMGZjMzY0ZTFkYjAzYjVjZTQ4OTdkZjcyZDA0IiwidGFnIjoiIn0%3D; test_session=eyJpdiI6InpkeTdTUzJGYWZZVUdTRU9DdDlHcGc9PSIsInZhbHVlIjoiLzFTZzZZV0RxMXFlRFRmMks3dXpNRHJwWXQ5WVlYcVpiVTRDdXR0RTBsNHN1eitwTlFDaHNMTmJUejhlbDVwVVVTWTJFTU96Y09PY2o0NEowaVJtVzYwVk5RWmU5TVN0TmJKY2VvcmpaN3lwYzlBZ2JpdUtFWk1BWlFPRmpVa3RrUHJzMTJFYzdvTmhtM0pzb0ovVWtWVWxMamx1T1hrMkJTUlN5RWt2bDdwcVVpR2dZVWdIYkJjNkRKaTNtMnhTa0VnamxyV2E2dWFVNE5qaFhBbkdxc1RBTWorbDBLaVhQVjVndnVieDQ0cHJyOUgzdENITlRKdGtobktScmY4RWZjb3RJV1FFUXY3d1BlTU5QL254YUJiS0FNY3pqT0Rnekx2TXR4anlqakk0ME4yQWJvcmkzbzZ0czBmZldHSkMyQzlJd05kSmdQa3BLdEZBb1VGM0F2REVUcDVMQ1N5WEpKTFFOMHZXNUxySnVZYUphLzd3ek9MbnpJdVhtbEVIRDI3a290TU9rbzc4aXFycDZoaVpPR01LRFBTRzVNcmp5WTlCeC83eW5scXVybEgrSFVVN0h5N0FxWTI1d3pLT3JzRGh2cXA5ZkovbU5oV0hnQ29nM0JYWjhoNkxBcm9vQjVSbitGa21BTG9kbTNUR1FTa1dTRC9DSW56Tm5RYmYiLCJtYWMiOiJiMDJmMmE0MjI1ZjQ5ZWU3YzVjNmFhYzIzMWUwZjBjM2M5MmZlMDdlMzQ1YmZjOWE5MGQ2MzI3ZTg0OGY1OWM0IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1235987258\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1117578291 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">VloaUQCBjpDKrel7nHeAzB1sTLdTMwteSos1MmAN</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1117578291\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-440223267 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 17 Jun 2025 13:53:00 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IjlZYnN0bm5jODhlOFBwWW9rMFUzUUE9PSIsInZhbHVlIjoiTDFsdGxLSys4RVdUaWp4RVBBMUFwSkRMd2d0SXNZcDVXaGp0N1R3amtaZFlvSnV3OFNTeWp3THhORW1iWUlBcXZiNFRNMmFNOXlPMkhOMWdrY0JEQ1RPVE5xREcxekErWXovaVEzanZqQnNpQzlGS3hIakl4WDFrei9neFZaRk1kQnlkb3MzZ1dnS1hJWm5TSU12Q1VhNVg2Zk9sRUZBZ0tiRytoa3k3cC9ZN3ZwWUVBWTZlNWNkZTdZTmNkcllsdmtMTGhHc3BaMFdwSTF5YnU0Z2gxaFFUWWhCUnp5QzR0djZqYmdGWlVhbVhmc2ZiczBwWXl1OG8xZk50OEFxR3NydTJDMHp2dWlEYWRRamY3Z3JtMXZRWGFzLzJLNitxRU50NnN6aGhRS2JYQlJOeU9Wb1gvaEwxNzZtWlNaK211TUI1MHVkODhMUDF1QnlqTjdMSEN3M0Z4YjZEMExUYmwyQ3FxRHlpL0VqZzZmVlgvSDlrSGxZbjJaczRFMDFpYUxaTGZQb3VFb2s2ZGdtN3JaK0htZ3JJMXBBUCtIU1prdlZaUDhrRlNPcit2RkVmZXpzVFhVdXRBMnI4d3FJaGVaOWVYZFQyU3V1ZFdEbDlpaG9adW11NVRRdjREaWpqRUJDY1l1Vm5sM29rUU0yemR0TUszQTlvQVRydVR1UkEiLCJtYWMiOiJkZDcxZjBkMTQyMmEzMjYxNTllNWQ1ZDcyZWIyZWRjMTNhZjdhNmE4MjNiMTdhYzI2ZmQzYzg2ZmZiOTg1YzkxIiwidGFnIjoiIn0%3D; expires=Tue, 17 Jun 2025 15:53:00 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6ImpsVzNIaFRqZlVXOHhzK01ZcXROZUE9PSIsInZhbHVlIjoiQkR0L2JRQ3R3NlFqTFk3NDBoUXczZ281cWdORDBSV2w4WHVMSGFlbUJONWZaanp0dkhsc1FEeW5jSkJUNzVOVVE1b2c2VEpoeDR6bDdyOEViWjZLMTBqWmlCN2xaU1BvLzN5dzlFcEJFVDF3YjArNFRsQklWbkhlaU1XWkNMTk11aW85TXZKdnp3RGdmYjdXU3U2RkdtS2JKOXl5dEpDaVZVbnZxWkhPcVVOdzlhZVJ0Yjd1MmIwMnNYZkFNWEN6U2FCRGZOZ2kwNGJXT0hZK09zWnU1aXFmOGdsb2w4QjNjaWxXM2x2WkcwWDZWa25nQVpPOEVPNm4wMzRNYkpSbzAwelJFaFhkWHlENU1OVk52SFhVRU5jT1VOdFZpVDlwdFE3WHFXYWFpZnRkSGlQTC9teWlSR3ZaT0kvamVUQnh1YXNNMmpISkw2cThTMzhKNXZ2NFlaZTluMi94T3lENUN6eXhyOTl6ZzNzZ0k1dXhmVzc2cTExeitncFlDNjN2bWo4ZGY2bkZ6TGRHNThXTU1qUjJjeDZyN0JPdzl2QkdmcUhIN1FFaGlkVmwzY0UvcW5TSkhBTmpCSE5aM0xCRXRMeG5aS2VSWDU1NnpCRVlFanJGREI3YnFENkJqMDNTU09zY3NCbkRkZmpxcmw4b3Q0T05tTjlCNFVTeVlhNVciLCJtYWMiOiJkNjgzZmMwYWViNjcyZjg2NjM1NDQ5ZWZjMmIyMjI3NDk2NGZjN2VkNWY5ZWQxOGMwYjNmMWY5MmRiMjVjMjQ2IiwidGFnIjoiIn0%3D; expires=Tue, 17 Jun 2025 15:53:00 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IjlZYnN0bm5jODhlOFBwWW9rMFUzUUE9PSIsInZhbHVlIjoiTDFsdGxLSys4RVdUaWp4RVBBMUFwSkRMd2d0SXNZcDVXaGp0N1R3amtaZFlvSnV3OFNTeWp3THhORW1iWUlBcXZiNFRNMmFNOXlPMkhOMWdrY0JEQ1RPVE5xREcxekErWXovaVEzanZqQnNpQzlGS3hIakl4WDFrei9neFZaRk1kQnlkb3MzZ1dnS1hJWm5TSU12Q1VhNVg2Zk9sRUZBZ0tiRytoa3k3cC9ZN3ZwWUVBWTZlNWNkZTdZTmNkcllsdmtMTGhHc3BaMFdwSTF5YnU0Z2gxaFFUWWhCUnp5QzR0djZqYmdGWlVhbVhmc2ZiczBwWXl1OG8xZk50OEFxR3NydTJDMHp2dWlEYWRRamY3Z3JtMXZRWGFzLzJLNitxRU50NnN6aGhRS2JYQlJOeU9Wb1gvaEwxNzZtWlNaK211TUI1MHVkODhMUDF1QnlqTjdMSEN3M0Z4YjZEMExUYmwyQ3FxRHlpL0VqZzZmVlgvSDlrSGxZbjJaczRFMDFpYUxaTGZQb3VFb2s2ZGdtN3JaK0htZ3JJMXBBUCtIU1prdlZaUDhrRlNPcit2RkVmZXpzVFhVdXRBMnI4d3FJaGVaOWVYZFQyU3V1ZFdEbDlpaG9adW11NVRRdjREaWpqRUJDY1l1Vm5sM29rUU0yemR0TUszQTlvQVRydVR1UkEiLCJtYWMiOiJkZDcxZjBkMTQyMmEzMjYxNTllNWQ1ZDcyZWIyZWRjMTNhZjdhNmE4MjNiMTdhYzI2ZmQzYzg2ZmZiOTg1YzkxIiwidGFnIjoiIn0%3D; expires=Tue, 17-Jun-2025 15:53:00 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6ImpsVzNIaFRqZlVXOHhzK01ZcXROZUE9PSIsInZhbHVlIjoiQkR0L2JRQ3R3NlFqTFk3NDBoUXczZ281cWdORDBSV2w4WHVMSGFlbUJONWZaanp0dkhsc1FEeW5jSkJUNzVOVVE1b2c2VEpoeDR6bDdyOEViWjZLMTBqWmlCN2xaU1BvLzN5dzlFcEJFVDF3YjArNFRsQklWbkhlaU1XWkNMTk11aW85TXZKdnp3RGdmYjdXU3U2RkdtS2JKOXl5dEpDaVZVbnZxWkhPcVVOdzlhZVJ0Yjd1MmIwMnNYZkFNWEN6U2FCRGZOZ2kwNGJXT0hZK09zWnU1aXFmOGdsb2w4QjNjaWxXM2x2WkcwWDZWa25nQVpPOEVPNm4wMzRNYkpSbzAwelJFaFhkWHlENU1OVk52SFhVRU5jT1VOdFZpVDlwdFE3WHFXYWFpZnRkSGlQTC9teWlSR3ZaT0kvamVUQnh1YXNNMmpISkw2cThTMzhKNXZ2NFlaZTluMi94T3lENUN6eXhyOTl6ZzNzZ0k1dXhmVzc2cTExeitncFlDNjN2bWo4ZGY2bkZ6TGRHNThXTU1qUjJjeDZyN0JPdzl2QkdmcUhIN1FFaGlkVmwzY0UvcW5TSkhBTmpCSE5aM0xCRXRMeG5aS2VSWDU1NnpCRVlFanJGREI3YnFENkJqMDNTU09zY3NCbkRkZmpxcmw4b3Q0T05tTjlCNFVTeVlhNVciLCJtYWMiOiJkNjgzZmMwYWViNjcyZjg2NjM1NDQ5ZWZjMmIyMjI3NDk2NGZjN2VkNWY5ZWQxOGMwYjNmMWY5MmRiMjVjMjQ2IiwidGFnIjoiIn0%3D; expires=Tue, 17-Jun-2025 15:53:00 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-440223267\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1100727391 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>16</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"30 characters\">http://localhost/hrm-dashboard</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1100727391\", {\"maxDepth\":0})</script>\n"}}