{"__meta": {"id": "Xc616c20ec07fc70905efbec4d598f0a3", "datetime": "2025-06-17 13:26:12", "utime": **********.203355, "method": "GET", "uri": "/customer/check/warehouse?customer_id=10&warehouse_id=8", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1750166771.549065, "end": **********.203381, "duration": 0.6543159484863281, "duration_str": "654ms", "measures": [{"label": "Booting", "start": 1750166771.549065, "relative_start": 0, "end": **********.096902, "relative_end": **********.096902, "duration": 0.5478367805480957, "duration_str": "548ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.096915, "relative_start": 0.5478498935699463, "end": **********.203384, "relative_end": 2.86102294921875e-06, "duration": 0.10646891593933105, "duration_str": "106ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 46235744, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET customer/check/warehouse", "middleware": "web, verified, auth, XSS, revalidate", "controller": "App\\Http\\Controllers\\CustomerController@checkWarehouse", "namespace": null, "prefix": "", "where": [], "as": "customer.check.warehouse", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FHttp%2FControllers%2FCustomerController.php&line=383\" onclick=\"\">app/Http/Controllers/CustomerController.php:383-397</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.030010000000000002, "accumulated_duration_str": "30.01ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 16 limit 1", "type": "query", "params": [], "bindings": ["16"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.144238, "duration": 0.02844, "duration_str": "28.44ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 94.768}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.186751, "duration": 0.00093, "duration_str": "930μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 94.768, "width_percent": 3.099}, {"sql": "select * from `customers` where `customers`.`id` = '10' limit 1", "type": "query", "params": [], "bindings": ["10"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/CustomerController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Http\\Controllers\\CustomerController.php", "line": 388}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.192629, "duration": 0.00064, "duration_str": "640μs", "memory": 0, "memory_str": null, "filename": "CustomerController.php:388", "source": "app/Http/Controllers/CustomerController.php:388", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FHttp%2FControllers%2FCustomerController.php&line=388", "ajax": false, "filename": "CustomerController.php", "line": "388"}, "connection": "kdmkjkqknb", "start_percent": 97.867, "width_percent": 2.133}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\Customer": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FModels%2FCustomer.php&line=1", "ajax": false, "filename": "Customer.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "ixL6BN3l9tyyUscqj0hv99yKIy0daqhD43fAAWcL", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "16", "pos": "array:1 [\n  2043 => array:9 [\n    \"name\" => \"zello sour\"\n    \"quantity\" => 1\n    \"price\" => \"7.00\"\n    \"id\" => \"2043\"\n    \"tax\" => 0\n    \"subtotal\" => 7.0\n    \"originalquantity\" => 19\n    \"product_tax\" => \"-\"\n    \"product_tax_id\" => 0\n  ]\n]"}, "request": {"path_info": "/customer/check/warehouse", "status_code": "<pre class=sf-dump id=sf-dump-1054200111 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1054200111\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>customer_id</span>\" => \"<span class=sf-dump-str title=\"2 characters\">10</span>\"\n  \"<span class=sf-dump-key>warehouse_id</span>\" => \"<span class=sf-dump-str>8</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1347290521 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1347290521\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1578744191 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">ixL6BN3l9tyyUscqj0hv99yKIy0daqhD43fAAWcL</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1937 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=1pubyvl%7C2%7Cfwu%7C0%7C1993; _clsk=25xk9k%7C1750165707106%7C8%7C1%7Cl.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6ImxxNFpPUHpVaGRqU1pMVFZwcFdZOEE9PSIsInZhbHVlIjoibHcyZ01zY1M4WmtlRHNObU5GWlgzR2NnbkUwUFlEWGZBYXU1NGwxL1p0MVZkRFl0dXhRQzdUYmxoc3NNaFdoQUpBWVdqTFdJTHpyRFcvZXlvUm54QTcvditpMWswWlkyN2xNUldPYlczZWZUTStxelljZmZHY0hiMXptZWV4Rlg4eTUwRlZ3U1ZEK2tJWDlUYUIrY1AwbHBlMGdYWkNneEpIbEdGR2EzQmdUdTFaNTdUZEpGVmh2dkVPTkRPbmh3VkdBWVBPdTRUOXFpQ2dvY1FMb0U4S1hTNTBVL3RMTUFDb0N6WXdpUmtTYXp2WnMxUnhPdU9jbUMyU0ZObHN0SFl4RFZjajFwbTdyK01PY3FQbGd2VTdUcGx2bUdCRUhmVXNyVDJOdTlCUWI3cjNZRWprNEhJK1NIamNEYmVKL1ZmSlN6Z0J4dkF5SlhEd1pCWjNvdS9SVE5HQ0g4WkdBY3R1ZXgvNHJLVUwzd25nZkZqRVFJc0Z6TTJjYzdoODVQTjMvZk1TWk9OTVRxbklDamdmdHVGNHZPVGk1dVRqZmhVR2xCVkttK09ocncxUVpVUTZTM2F1Ny9UU2ZtTHRDM2MvSThJVUlmaGg1N2dWcFRWeDRSOTNUMG91ZkllMGpwMm96cUFpTG0ybzRHT0dqb0lBV1NYUThWb0VtSEp5RnciLCJtYWMiOiI0NGFmZmI5ZGZiNDZlNDA3MGMwMmUyZmJiYTg3M2E0YTcwYjAzNzgyMDU4ZTIzNDJmN2RiNWRjZjEyZmFhZjc1IiwidGFnIjoiIn0%3D; test_session=eyJpdiI6IldCMHdHakJ5eS9qL3NhaGNlaWQ0ZWc9PSIsInZhbHVlIjoiSEp5Q3krLzRoWUMyYU11NHVacjI3VzNmQXM4RUVOUVI4VElQcFg3OTJNeGhlckdEVndIamsyN2pkTUZCbmNHbTNMUS90Kzh5M1pQN09kN0lSc216bmtqamxhbjJKa2ppWjdDSnU5TjA0emdNdEI0bUZBNk53TDM0R2dlcXo4ZlBHUjZXR0hsRTRLbVJuYzVESmlKS0xjTVFQV1h3Z0cyNGovck54cHVrdDRZVmJnQ3F1Q24xN2E2MEYvelBBRmE0dThvU2JCTVlWakV6RUxiRm5ORjM0OUViMnZ4c1F6R1pXeWxyOXd0dFVJTUFUd2JsMExjNkM3YjdMNzZNa1JZWE42YnVKY0ozMmtEQUhSbVUxcldvQ2ZXRGluZTBYY21OL3pjdm0yWGJNU2oyMmVjOCtYYm9MUkdQUzRvNi9UdE8vSWZjalMva0ZkOVpHMmpROXFDWlFPWFpGUW9ZSFhHUTQ0QmwxMGZVMUNmS21EUG5OTjI5SHNyUEZ3WVk2bmphWGdDRVIzODVoZVQ5QjJmTEwwTG9ZemttekJ2enU1RFEyVXZyU3lVMXNYVllUSDBkeTlNeER2MlQ2NjhHUWhJNnY5NmowSFhVSVdTWDYrelFRUkdTVERuczNETHY3V0VsRE15dW9rejF0Y2dlb0Q4dFdJMU8rcHhlQVVJUHlKc0UiLCJtYWMiOiI2ZjQ1YzEyOTBmNzNlYWQ1YjRiY2NhYzM1NTM5YWE0Zjg1YmQzZWJlYWFlMDNiZmMzYmFhZjk2NjU5YzUwNjk5IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1578744191\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-776994458 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">ixL6BN3l9tyyUscqj0hv99yKIy0daqhD43fAAWcL</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">coJ97Z8YyNCeFgF4h0jUL1PrpYBjauguIcJbJbm8</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-776994458\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-448093813 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 17 Jun 2025 13:26:12 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-methods</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">POST, GET, OPTIONS, PUT, DELETE</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-headers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"80 characters\">Content-Type, Accept, Authorization, X-Requested-With, Application, X-CSRF-TOKEN</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IkhLKy9UZnZkN0FmTTVqR2NOdmxpMGc9PSIsInZhbHVlIjoicU91cGlSOGVscU9MUWprOTBKM2ZmSGJ4REplTmE2UUJYM1JLc0ZzU3kxQkhxZnNMaXhSazJTUmRYNjZLam92S0M1YTRQNEs1N0RHc2lJOUhqejcxc2ltbW5mR1JveVQ4TFpoVGE0eFRhQm1pT3NQVUdmZEZvK2MyQlVRei9yb3V4Ukx0SHVxdldpSEVnVldQUzFkOHY2eEZrVTQ0SERDM3hBNFphNWJvOHR2NFFoWW5NMjhkZ2YrRFpZdnNDdUQwVVlUSUc5NTdzdnZaalFlN3dZK0xmdmpHdGdTcnp6c0YwamIwc1FwZ2hQeG5Sekp6UUhJdGx3M3NWMkFIOVpXZmtwYlJ5VitBZEJrVE94VUJmTW02UVNpOUtma25SU3VQOXlnMCtSUlZISnUxWFd2bXM2Z3BGSmY1T3F1U1MyZnF1QWFwenNZOWlDNnVsQmE1RlhVZVQxTFBuMEpjcGp3MUttZlVVZC9WOWN3ZXNlb1JqVFBOa0VUNVRSOWVqQWZMaXdSU3pzWUI3bDRGYUpFSXdub0s3bFJJRFk2SjJCMGVvWXpTWjBrUW9qTHFsZlprdjBraHFGalhKOUl1Wk1NSXZZNi9SZk0xZjJwQUFPU25hY1dPeXIxSjVxTXV5UjVuV3dqSldSYXdhR0VFcFdlakc2RTBUSnVTOWNvc1FIVFUiLCJtYWMiOiJkMDQ1ZTk0ZWY1MmM4YmFiOTkyNmQ1MjhlNDgyZGQ4NWQ4NjI1MWQyNzBmZDdmZDFiMWY0MzliYTAzY2JhNzA4IiwidGFnIjoiIn0%3D; expires=Tue, 17 Jun 2025 15:26:12 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6IjZDTC84SkI5V1BHRlVIZ1lxUnFiSFE9PSIsInZhbHVlIjoiWWc4K25DUWlHM3NhOExkMHBvZ29CMmQrSmhvOEFnQWhrc1EvQWlZUTRxaHZqRVRwSDNjaFRyc1o3N2EvQ2ZQOHJwS2ZEdGJudzRsUmpWR285d1BVa3lJZFJuTzlOYW54bGozMFdTa2JhcXJueUtOOFU5OTc0U3lZSVc4VEZRSzlkZGE2UWNMa3MvM0FEL1Bpais1WDJTb1ZRVUJIZWhhb1h5aWRIdlRRY1FPTXBMVjU4ckRBQzkvTTJ5SWV2TDlNelQzTUVjbkk3SjM3am5LM2RhUnREZVBpRGxrbnNMVnFmWHV3Tzgwd3J5M1lsMUN6MkhucWcyRSt1N290VDdGZlRnNDk1UHZ5RlJjVUR1ZlN4T1NXSWZ5OGlpTnRuU3Y1TUhUc05jakJvOGozLysxZDlOalNQWnN1QnRob2NjSzRXcEtScVpWU0VJOFBsd1FPckFZS3creXQ1YnZwUGdEbnA5YytDUW01L21ZUy9PbG5oNEdxSjBOWmRCelZ4VjE3NnZTdmRSUWNxdjZQa04rWU9yMnUwSEhmMEFZV056WSt0Ri9LNWNPVXJKVkNocHhwWDVuR2NSNnZQMkpvblh4ZjdTT21pR3l4d3pCTmw1TVVsbnBzMmVDWXlmRkVsd3pSZUZGTm1BNU0wcFNaYWRFVWpVa0grOEsvZFJmbHpTK0QiLCJtYWMiOiJmZGZhMjIyMGE1OTE3MWYwMjJkYWNkMGQ5YzMzYzU1ZWIzMGU1MTNkOWI2ZGU0Njc2YmVkN2M2NDI4MmEzN2IyIiwidGFnIjoiIn0%3D; expires=Tue, 17 Jun 2025 15:26:12 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IkhLKy9UZnZkN0FmTTVqR2NOdmxpMGc9PSIsInZhbHVlIjoicU91cGlSOGVscU9MUWprOTBKM2ZmSGJ4REplTmE2UUJYM1JLc0ZzU3kxQkhxZnNMaXhSazJTUmRYNjZLam92S0M1YTRQNEs1N0RHc2lJOUhqejcxc2ltbW5mR1JveVQ4TFpoVGE0eFRhQm1pT3NQVUdmZEZvK2MyQlVRei9yb3V4Ukx0SHVxdldpSEVnVldQUzFkOHY2eEZrVTQ0SERDM3hBNFphNWJvOHR2NFFoWW5NMjhkZ2YrRFpZdnNDdUQwVVlUSUc5NTdzdnZaalFlN3dZK0xmdmpHdGdTcnp6c0YwamIwc1FwZ2hQeG5Sekp6UUhJdGx3M3NWMkFIOVpXZmtwYlJ5VitBZEJrVE94VUJmTW02UVNpOUtma25SU3VQOXlnMCtSUlZISnUxWFd2bXM2Z3BGSmY1T3F1U1MyZnF1QWFwenNZOWlDNnVsQmE1RlhVZVQxTFBuMEpjcGp3MUttZlVVZC9WOWN3ZXNlb1JqVFBOa0VUNVRSOWVqQWZMaXdSU3pzWUI3bDRGYUpFSXdub0s3bFJJRFk2SjJCMGVvWXpTWjBrUW9qTHFsZlprdjBraHFGalhKOUl1Wk1NSXZZNi9SZk0xZjJwQUFPU25hY1dPeXIxSjVxTXV5UjVuV3dqSldSYXdhR0VFcFdlakc2RTBUSnVTOWNvc1FIVFUiLCJtYWMiOiJkMDQ1ZTk0ZWY1MmM4YmFiOTkyNmQ1MjhlNDgyZGQ4NWQ4NjI1MWQyNzBmZDdmZDFiMWY0MzliYTAzY2JhNzA4IiwidGFnIjoiIn0%3D; expires=Tue, 17-Jun-2025 15:26:12 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6IjZDTC84SkI5V1BHRlVIZ1lxUnFiSFE9PSIsInZhbHVlIjoiWWc4K25DUWlHM3NhOExkMHBvZ29CMmQrSmhvOEFnQWhrc1EvQWlZUTRxaHZqRVRwSDNjaFRyc1o3N2EvQ2ZQOHJwS2ZEdGJudzRsUmpWR285d1BVa3lJZFJuTzlOYW54bGozMFdTa2JhcXJueUtOOFU5OTc0U3lZSVc4VEZRSzlkZGE2UWNMa3MvM0FEL1Bpais1WDJTb1ZRVUJIZWhhb1h5aWRIdlRRY1FPTXBMVjU4ckRBQzkvTTJ5SWV2TDlNelQzTUVjbkk3SjM3am5LM2RhUnREZVBpRGxrbnNMVnFmWHV3Tzgwd3J5M1lsMUN6MkhucWcyRSt1N290VDdGZlRnNDk1UHZ5RlJjVUR1ZlN4T1NXSWZ5OGlpTnRuU3Y1TUhUc05jakJvOGozLysxZDlOalNQWnN1QnRob2NjSzRXcEtScVpWU0VJOFBsd1FPckFZS3creXQ1YnZwUGdEbnA5YytDUW01L21ZUy9PbG5oNEdxSjBOWmRCelZ4VjE3NnZTdmRSUWNxdjZQa04rWU9yMnUwSEhmMEFZV056WSt0Ri9LNWNPVXJKVkNocHhwWDVuR2NSNnZQMkpvblh4ZjdTT21pR3l4d3pCTmw1TVVsbnBzMmVDWXlmRkVsd3pSZUZGTm1BNU0wcFNaYWRFVWpVa0grOEsvZFJmbHpTK0QiLCJtYWMiOiJmZGZhMjIyMGE1OTE3MWYwMjJkYWNkMGQ5YzMzYzU1ZWIzMGU1MTNkOWI2ZGU0Njc2YmVkN2M2NDI4MmEzN2IyIiwidGFnIjoiIn0%3D; expires=Tue, 17-Jun-2025 15:26:12 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-448093813\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1782379797 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">ixL6BN3l9tyyUscqj0hv99yKIy0daqhD43fAAWcL</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>16</span>\n  \"<span class=sf-dump-key>pos</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-key>2043</span> => <span class=sf-dump-note>array:9</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"10 characters\">zello sour</span>\"\n      \"<span class=sf-dump-key>quantity</span>\" => <span class=sf-dump-num>1</span>\n      \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"4 characters\">7.00</span>\"\n      \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"4 characters\">2043</span>\"\n      \"<span class=sf-dump-key>tax</span>\" => <span class=sf-dump-num>0</span>\n      \"<span class=sf-dump-key>subtotal</span>\" => <span class=sf-dump-num>7.0</span>\n      \"<span class=sf-dump-key>originalquantity</span>\" => <span class=sf-dump-num>19</span>\n      \"<span class=sf-dump-key>product_tax</span>\" => \"<span class=sf-dump-str>-</span>\"\n      \"<span class=sf-dump-key>product_tax_id</span>\" => <span class=sf-dump-num>0</span>\n    </samp>]\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1782379797\", {\"maxDepth\":0})</script>\n"}}