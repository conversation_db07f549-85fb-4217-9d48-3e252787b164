{"__meta": {"id": "Xcb14041032f65d699660588013e9fa00", "datetime": "2025-06-17 13:48:46", "utime": **********.584953, "method": "GET", "uri": "/add-to-cart/2044/pos", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1750168125.941372, "end": **********.584979, "duration": 0.6436071395874023, "duration_str": "644ms", "measures": [{"label": "Booting", "start": 1750168125.941372, "relative_start": 0, "end": **********.421062, "relative_end": **********.421062, "duration": 0.4796900749206543, "duration_str": "480ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.421074, "relative_start": 0.4797019958496094, "end": **********.584981, "relative_end": 1.9073486328125e-06, "duration": 0.16390705108642578, "duration_str": "164ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 49788688, "peak_usage_str": "47MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET add-to-cart/{id}/{session}", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\ProductServiceController@addToCart", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FHttp%2FControllers%2FProductServiceController.php&line=1338\" onclick=\"\">app/Http/Controllers/ProductServiceController.php:1338-1571</a>"}, "queries": {"nb_statements": 7, "nb_failed_statements": 0, "accumulated_duration": 0.02989, "accumulated_duration_str": "29.89ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 16 limit 1", "type": "query", "params": [], "bindings": ["16"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.473542, "duration": 0.02085, "duration_str": "20.85ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 69.756}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.508293, "duration": 0.00106, "duration_str": "1.06ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 69.756, "width_percent": 3.546}, {"sql": "select `permissions`.*, `model_has_permissions`.`model_id` as `pivot_model_id`, `model_has_permissions`.`permission_id` as `pivot_permission_id`, `model_has_permissions`.`model_type` as `pivot_model_type` from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `model_has_permissions`.`model_id` = 16 and `model_has_permissions`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["16", "App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 326}, {"index": 21, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 566}], "start": **********.536524, "duration": 0.00083, "duration_str": "830μs", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:326", "source": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php:326", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=326", "ajax": false, "filename": "HasPermissions.php", "line": "326"}, "connection": "kdmkjkqknb", "start_percent": 73.302, "width_percent": 2.777}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (16) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 312}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 26, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}], "start": **********.542207, "duration": 0.0009599999999999999, "duration_str": "960μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 76.079, "width_percent": 3.212}, {"sql": "select * from `product_services` where `product_services`.`id` = '2044' limit 1", "type": "query", "params": [], "bindings": ["2044"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/ProductServiceController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Http\\Controllers\\ProductServiceController.php", "line": 1342}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.5523329, "duration": 0.0009, "duration_str": "900μs", "memory": 0, "memory_str": null, "filename": "ProductServiceController.php:1342", "source": "app/Http/Controllers/ProductServiceController.php:1342", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FHttp%2FControllers%2FProductServiceController.php&line=1342", "ajax": false, "filename": "ProductServiceController.php", "line": "1342"}, "connection": "kdmkjkqknb", "start_percent": 79.291, "width_percent": 3.011}, {"sql": "select sum(`quantity`) as aggregate from `warehouse_products` where `product_id` = 2044 and exists (select * from `warehouses` where `warehouse_products`.`warehouse_id` = `warehouses`.`id` and `created_by` = 15)", "type": "query", "params": [], "bindings": ["2044", "15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/ProductService.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Models\\ProductService.php", "line": 155}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/ProductServiceController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Http\\Controllers\\ProductServiceController.php", "line": 1346}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.561026, "duration": 0.00457, "duration_str": "4.57ms", "memory": 0, "memory_str": null, "filename": "ProductService.php:155", "source": "app/Models/ProductService.php:155", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FModels%2FProductService.php&line=155", "ajax": false, "filename": "ProductService.php", "line": "155"}, "connection": "kdmkjkqknb", "start_percent": 82.302, "width_percent": 15.289}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Models\\Utility.php", "line": 4725}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Models\\Utility.php", "line": 4659}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/ProductServiceController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Http\\Controllers\\ProductServiceController.php", "line": 1415}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.56876, "duration": 0.0007199999999999999, "duration_str": "720μs", "memory": 0, "memory_str": null, "filename": "Utility.php:4725", "source": "app/Models/Utility.php:4725", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FModels%2FUtility.php&line=4725", "ajax": false, "filename": "Utility.php", "line": "4725"}, "connection": "kdmkjkqknb", "start_percent": 97.591, "width_percent": 2.409}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}, "App\\Models\\ProductService": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FModels%2FProductService.php&line=1", "ajax": false, "filename": "ProductService.php", "line": "?"}}}, "count": 3, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 1, "messages": [{"message": "[\n  ability => manage product & service,\n  result => true,\n  user => 16,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-2132488062 data-indent-pad=\"  \"><span class=sf-dump-note>manage product & service</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"24 characters\">manage product &amp; service</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>16</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2132488062\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.550867, "xdebug_link": null}]}, "session": {"_token": "PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "16", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]", "pos": "array:1 [\n  2044 => array:9 [\n    \"name\" => \"تجربية\"\n    \"quantity\" => 1\n    \"price\" => \"10.00\"\n    \"id\" => \"2044\"\n    \"tax\" => 0\n    \"subtotal\" => 10.0\n    \"originalquantity\" => 1\n    \"product_tax\" => \"-\"\n    \"product_tax_id\" => 0\n  ]\n]"}, "request": {"path_info": "/add-to-cart/2044/pos", "status_code": "<pre class=sf-dump id=sf-dump-2072204407 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-2072204407\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1957702931 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1957702931\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1936 characters\">_clck=2gyfy%7C2%7Cfwu%7C0%7C1994; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clsk=1ti4td2%7C1750167218299%7C2%7C1%7Cl.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6ImZ3OEN4a0VvQnBtbUQrb2RtWlFWeHc9PSIsInZhbHVlIjoiQjd5UFd2cTNQOVZZNGtWZnBsa1ZsZmNPeXBib2RPTHhtUTdoNFIydVBMa2t0ODBZNCtaV3FCdWVxY2pNWW44bXF3TDVtNnl0Tm9BQVdoZW1paGJmT1ZxWkxZem9BcTB2TkJBNWdrbGJmSmZCak1ibFNlRGFQaUJaSW11UTJxT28xbjRiZEhrR3dzWG4yZlp0VEVtWWJsVG81UE5QZERzSmVNSkxiUmNiSGw2TWNpaTRpWHZRWG8wNGJKRi9JRG4xMXJrbll3ZldnOVpqQUpUQURqTC9Ib1pDYTZuSEwrcUJHbjVSRmJicytuY2JoaWZiZk0vc2VBQ2ZrNlhaMTRNL1ZmeXphNUwySHRGZmJBT1NCSDBlSFo2UC9sMlV2dEZlUjJScUJQQ2swZDYvM2ZnL0FVNXlBSEE4TE1FSFpaYjFxTUVWMS8yamNFTnVyaUgycVdBS085M2Q1RkFXWEx1dElZUjlIR245T0I1bUNkS3VYR2RCVmhWQTVKclM4ai9JY1hZSWZCVUFtTDFQUEExd2FqT21uRURDVythS2FUSkx2QVpXcGtWL1VZeVRwbFk4akk2VWxpZXZhbFpQbEMyNE8zT1JBWlBscXdOb3dnSGJoT2xKeVgxckF4cDAvZkpranNhZ203WEhQTkFDZmR2QTZaMC8rbkh1OEFZS2wyQTciLCJtYWMiOiIzMDJmMWRmOTU1Nzk0NmYyNGYyMTgyMmUwNjlkY2Q2NmJhOGU5ZDRiYTYxYmUzYzI2NjljNWFhMTdmMzI5ZWQ0IiwidGFnIjoiIn0%3D; test_session=eyJpdiI6InVuQVN4eHdzeFVOZUxCYkFnNFBzbHc9PSIsInZhbHVlIjoic3h0WXJFd2Z6ZW1OOUQxcWIzWS8yRkxkNGlPVFoweVgwbm5KTWs5RXVJd0I2U2ZqWFJaakNpMmxuVlUrM0VLVWR6aGNJVUFoUkU1Mmo5TW9RclIrSXFnRFZ1MHB4SENZMlFEaW4rMG5SYXBlVTJpb3RqMHpNckErUXR0MWE1QW5WT21vUDZNYlFXaXNaNm5scVRZOWFHWndNY21VWTY1YURWV2tITEZValFoMHBWOE1tRm5wV05GdnI5bGxRY3ZmWWliMWZYSmh1bVRtZXdsV0ZNcDZGeGt3VlJBS3RnbDQ1MzFMQWJwbFZmMkMwam5JaE5lMGxoR3V6MzRzbyttc2pNbWZQL2cyR3B3QmptTW81TUV1eTlLUDlja2ZCNmZWcmwvUDJBTUNhbHFid3Y0T2R1RlBiTjFkaUhDMVpaaTJQNVJyYzg1WmtaOUIxTUEwdjMweTBlZVpIYW5sMHF5cit6NHpwZTBwVG5MNEZobHc1d09hY2RMYlJobDArSWhJZzkvNkRrajZ4S2lxRHNPVFFxZDQ4SnA0M1lxUm43NXB2cmVDVkMwSklaZG9aT1FtL3EzR2pPNDVHWjlOOGZmMUNHc2xPSTQ2ZmhjY1lPUCsrMTJGVU9FSEgyZG9rdEFIMmMyM2dJT3NJWEFHVHhGTzU5Z2xGWDRSU1M5TktBRXUiLCJtYWMiOiI0N2RhYTVlYzg2OTdkMDAwYjk4NTk4NDNkNDBhY2MyZDE3ZWNmMTNiMjBlMTgyNjQzN2I2YjVmOWM1M2YwZTNhIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1109067882 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">VloaUQCBjpDKrel7nHeAzB1sTLdTMwteSos1MmAN</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1109067882\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-881496630 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 17 Jun 2025 13:48:46 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IlNUR1d0YWF1d2VyNFVCM1dVUmpzdFE9PSIsInZhbHVlIjoiQnFZazBjZzhMMFpaVE9DU1prdW1GNFBFeWlrT3pVR3k2MEJQZHEwclFvb0xjenRjOFd5Q2p1OCt2d1N5Mkc2WlcxaHZqU0NKd0J2VkFQRmU0NGxTZU9YMXZ3aDRsSTBNSUh2SGgxS2p3cXIrSjF2UTdpbkV3VWMyRDdqUFNkTHpFWHd3ZDhXV2lUcGNvY3BYL1VKbk5QRVJLSklxL0VUVWg3U3FrSTVXdW9jcHBDeUNkVkY0TkE1bzlrWlJ5VVhQNDRXVTBDcG1Qc0sxNEU4TjJyMzBubVFnMXpjN1VFTFBxR0tNMVIvMlErVWpOQkt4WVlGUUdtT3d3aGhrZ2ZadnFuaUw5dEZnc3U5aWNJM0Q0OS9zbVBHc0swb25YMEZLRm9sOTNPUnhBSE14ajRDOFdnWkpqZ1A3UzlwbStPZXdUTk9vSTBiUk5IcFpCSThuVzVINnNGR2VZUnpUa0t0OUdTbEE0WW9lNmxpcUN4MFIydWJCc01ldFhzbW1pbW9lQ3ozZUFFS01nMUtrcUFJYStQYmhrY1BXaDYvVTNQdFRPbjJjSlJTdDhzMHJNU1RFb2FiekQ2cDdoazZjczJTRzdIenNuK25ESjMxR0E1MHk3blpxamFOOGtkRUU5OFcxZkVUT1ZsQjhieWRzY1RUN2tlZXpCUXhLR3FmeFpYMnYiLCJtYWMiOiI0MGQxNmE5NDVhNDk1NzQ2MmM2OWY0OTBkMTMzODRlODhlMzllNDAwZTA3ZDY3MTFhZGEwNDgxMDljNmNhODg5IiwidGFnIjoiIn0%3D; expires=Tue, 17 Jun 2025 15:48:46 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6Ikxrd0Y2Q0N4SEZyMzQzYzV0UkIxK0E9PSIsInZhbHVlIjoiOEF2MlZBU1JGOUNBWktLUzBYT3g5d3hiTEJKRURzMkFmQUJacm9lam13aStTbFhDWklERC9SZ1dNZ1FVcjZwdURaS0VXL0FadlFhRi9teWlhcm5abDl3SkZBLzlMRHB2MlhraWliVVI2c1lzbTlaaWFQUSt2MVBCczM0YUZGNXdMTzZ3M01GZ2tZNGNWQzdxeHhjWVhFbkJVaHpCby9vSFJXaWpGZUhGVmo1NUM2bVVoK1VuQVZtVkZIWnI5QnZUaHBTbFo5dkZYZ2VRUlcxMTRVdVlxSFV1ODJBWk53VHg0czg2TThJcitpUHlaVlNLdXdkUFJBSDNSOGZGMi9GLzZHVm96V3ZrMFFnMmhENkJrYWE0eVk0b1hta3hzMGNIQUJpb1lKbDliWHJheWdEYldhZWpuZks3MmdYNFBaNm5ydzhYWjg3b2hMeEVYNXV3ZHE5OSs2L0NlZGpPMWIyOUhxRUZsUTFXakx1eUk1amtBYTBKTlJ3RUtSUmNETHczU09LdDhXZ3JLMEdqcGx6SHNRWVYxZHZJc3A3Z0ZvcWNPWHF5NUJGZVBvQVQ4NG85QTBWcDRqN0c3MUNnVlVJK2kzNGFOSUFnME4waGVqa0h5bVFSbXRrdEsvZEFCTndHYnRkdWxmTk50djdrbWt2SDVNODNDQUNKd091bm9UVk0iLCJtYWMiOiIyZWNiNTJjMDc3OGE1ZWNkMmY5NDYyZGU3MWZjZWUzOGRhNjRiN2IwYzYwNjJmMzM0MDU1MzE4NzUxYjQ0NDQzIiwidGFnIjoiIn0%3D; expires=Tue, 17 Jun 2025 15:48:46 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IlNUR1d0YWF1d2VyNFVCM1dVUmpzdFE9PSIsInZhbHVlIjoiQnFZazBjZzhMMFpaVE9DU1prdW1GNFBFeWlrT3pVR3k2MEJQZHEwclFvb0xjenRjOFd5Q2p1OCt2d1N5Mkc2WlcxaHZqU0NKd0J2VkFQRmU0NGxTZU9YMXZ3aDRsSTBNSUh2SGgxS2p3cXIrSjF2UTdpbkV3VWMyRDdqUFNkTHpFWHd3ZDhXV2lUcGNvY3BYL1VKbk5QRVJLSklxL0VUVWg3U3FrSTVXdW9jcHBDeUNkVkY0TkE1bzlrWlJ5VVhQNDRXVTBDcG1Qc0sxNEU4TjJyMzBubVFnMXpjN1VFTFBxR0tNMVIvMlErVWpOQkt4WVlGUUdtT3d3aGhrZ2ZadnFuaUw5dEZnc3U5aWNJM0Q0OS9zbVBHc0swb25YMEZLRm9sOTNPUnhBSE14ajRDOFdnWkpqZ1A3UzlwbStPZXdUTk9vSTBiUk5IcFpCSThuVzVINnNGR2VZUnpUa0t0OUdTbEE0WW9lNmxpcUN4MFIydWJCc01ldFhzbW1pbW9lQ3ozZUFFS01nMUtrcUFJYStQYmhrY1BXaDYvVTNQdFRPbjJjSlJTdDhzMHJNU1RFb2FiekQ2cDdoazZjczJTRzdIenNuK25ESjMxR0E1MHk3blpxamFOOGtkRUU5OFcxZkVUT1ZsQjhieWRzY1RUN2tlZXpCUXhLR3FmeFpYMnYiLCJtYWMiOiI0MGQxNmE5NDVhNDk1NzQ2MmM2OWY0OTBkMTMzODRlODhlMzllNDAwZTA3ZDY3MTFhZGEwNDgxMDljNmNhODg5IiwidGFnIjoiIn0%3D; expires=Tue, 17-Jun-2025 15:48:46 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6Ikxrd0Y2Q0N4SEZyMzQzYzV0UkIxK0E9PSIsInZhbHVlIjoiOEF2MlZBU1JGOUNBWktLUzBYT3g5d3hiTEJKRURzMkFmQUJacm9lam13aStTbFhDWklERC9SZ1dNZ1FVcjZwdURaS0VXL0FadlFhRi9teWlhcm5abDl3SkZBLzlMRHB2MlhraWliVVI2c1lzbTlaaWFQUSt2MVBCczM0YUZGNXdMTzZ3M01GZ2tZNGNWQzdxeHhjWVhFbkJVaHpCby9vSFJXaWpGZUhGVmo1NUM2bVVoK1VuQVZtVkZIWnI5QnZUaHBTbFo5dkZYZ2VRUlcxMTRVdVlxSFV1ODJBWk53VHg0czg2TThJcitpUHlaVlNLdXdkUFJBSDNSOGZGMi9GLzZHVm96V3ZrMFFnMmhENkJrYWE0eVk0b1hta3hzMGNIQUJpb1lKbDliWHJheWdEYldhZWpuZks3MmdYNFBaNm5ydzhYWjg3b2hMeEVYNXV3ZHE5OSs2L0NlZGpPMWIyOUhxRUZsUTFXakx1eUk1amtBYTBKTlJ3RUtSUmNETHczU09LdDhXZ3JLMEdqcGx6SHNRWVYxZHZJc3A3Z0ZvcWNPWHF5NUJGZVBvQVQ4NG85QTBWcDRqN0c3MUNnVlVJK2kzNGFOSUFnME4waGVqa0h5bVFSbXRrdEsvZEFCTndHYnRkdWxmTk50djdrbWt2SDVNODNDQUNKd091bm9UVk0iLCJtYWMiOiIyZWNiNTJjMDc3OGE1ZWNkMmY5NDYyZGU3MWZjZWUzOGRhNjRiN2IwYzYwNjJmMzM0MDU1MzE4NzUxYjQ0NDQzIiwidGFnIjoiIn0%3D; expires=Tue, 17-Jun-2025 15:48:46 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-881496630\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>16</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pos</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-key>2044</span> => <span class=sf-dump-note>array:9</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"6 characters\">&#1578;&#1580;&#1585;&#1576;&#1610;&#1577;</span>\"\n      \"<span class=sf-dump-key>quantity</span>\" => <span class=sf-dump-num>1</span>\n      \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"5 characters\">10.00</span>\"\n      \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"4 characters\">2044</span>\"\n      \"<span class=sf-dump-key>tax</span>\" => <span class=sf-dump-num>0</span>\n      \"<span class=sf-dump-key>subtotal</span>\" => <span class=sf-dump-num>10.0</span>\n      \"<span class=sf-dump-key>originalquantity</span>\" => <span class=sf-dump-num>1</span>\n      \"<span class=sf-dump-key>product_tax</span>\" => \"<span class=sf-dump-str>-</span>\"\n      \"<span class=sf-dump-key>product_tax_id</span>\" => <span class=sf-dump-num>0</span>\n    </samp>]\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n"}}