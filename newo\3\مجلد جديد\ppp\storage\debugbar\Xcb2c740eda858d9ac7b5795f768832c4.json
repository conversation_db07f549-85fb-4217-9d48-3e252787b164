{"__meta": {"id": "Xcb2c740eda858d9ac7b5795f768832c4", "datetime": "2025-06-17 13:10:06", "utime": **********.353923, "method": "POST", "uri": "/chats/favorites", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1750165805.52271, "end": **********.353966, "duration": 0.8312559127807617, "duration_str": "831ms", "measures": [{"label": "Booting", "start": 1750165805.52271, "relative_start": 0, "end": **********.192935, "relative_end": **********.192935, "duration": 0.6702249050140381, "duration_str": "670ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.192963, "relative_start": 0.670252799987793, "end": **********.35397, "relative_end": 4.0531158447265625e-06, "duration": 0.16100716590881348, "duration_str": "161ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 46118352, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/favorites", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getFavorites", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "favorites", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=454\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:454-477</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.01039, "accumulated_duration_str": "10.39ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.27898, "duration": 0.00824, "duration_str": "8.24ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 79.307}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.3062751, "duration": 0.00116, "duration_str": "1.16ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 79.307, "width_percent": 11.165}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (15) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.3290582, "duration": 0.00099, "duration_str": "990μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 90.472, "width_percent": 9.528}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "zwnmKcRLmNWGDZ856YJrjbbNuEV0Tli3iJOiUNS6", "_previous": "array:1 [\n  \"url\" => \"http://localhost/financial/productservice\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/favorites", "status_code": "<pre class=sf-dump id=sf-dump-1492839968 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1492839968\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-28513524 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-28513524\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-33191469 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">zwnmKcRLmNWGDZ856YJrjbbNuEV0Tli3iJOiUNS6</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-33191469\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">http://localhost/financial/productservice</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1994 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=a091af98-270d-406e-ab7e-33a6e610d6e2414686; _clck=nups8l%7C2%7Cfwu%7C0%7C1960; _clsk=kau4eo%7C1750165656311%7C14%7C1%7Cl.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6Imp6aEpRVC9Cc2VsTm5iZlhXd3lSb3c9PSIsInZhbHVlIjoibkZnNy9CMzhHeE4wSWxSK21tQ3VPdVI0NjhJUytSRndTdER0a1kvNmZJVGY1WFhjenBKZHc1U0pxbzYrRmhoM01zaFpidFN0VVZJUXA4Z05FYndFVVkvVHZIZ1dKajFpZjlZVHl0M3pqUjRFTGxJREpTT3lDTlJXUnQwc1piajNWUFF6aXlyUnhpSUpUN2pVbGZOalBBdUVEN1dKbVUvQ0xyVWJ1UWZCcXJKdGJLaFhGSGF0V3Z2QzdzV1hCazJZWjViQ040dlNuZmgxTDVEd3U2WVZTTU9NMUNMSVNuR0FvYmZVS1g0TWNDdE9TU3NuN0hINnMvQTdWazFtakpKcHR4QXBKcGpvOTFjam5va0IyeWJLaCs1N01kZDBJOTJmb3pQMHd6T1h5RG1LMjg4b2tNei9GMHEvS0k0ZW9xVHNqeSs0YWpvTFc1VUMwalZTV1RNZm1udWhKVGZLVndjOHdkR1hhVzBSY1dYTUU0VW5lc2ZJbjY2ay90bVNNK09VWHhlSVoxSTJldkVac0orSTZMd2RUdnZhRGlOWGZqeDVnT3NqRWRaZWZZOUEzdXd3TjNQZW5GSWUyWmRrWFkwTlJFbXpUZXo5MUM1NWJTNldDTVA0cUpNbDJ3bHRvbHpYdEFIQmo0RDZIdTFMWFo4cWZacjdYbnZkUnRwenkvUnAiLCJtYWMiOiI5MzNhYjI0MmU0ZmNiNmQ0NTM1YmMwNmJkZGM4ZjQzNDQ0M2UxYjIyYzUwNGNhYjE0NDNlNjNmZjhkZWIxNGNjIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6IlcveUdaUWYxSStTbldQcENlS2xDeHc9PSIsInZhbHVlIjoiZ25TR05xN2ZvRG1hb21Hdy9zYitYRkxuU1ZTSU5ZQW5IbjZUaHNuUEVhRGJUaXhxTHlaWWF4VW1KZW9SSXVoakU1c0dScjFtZWtYdm1mc0VyYW1WTGVsQTlQcEs5Qjl6dFdGZ0FDWDFhckxWWmc5YytadGRnd1FCYUlyM1MzUzdzVHYyR3B3RUgzYlFwYm9DaWpnNkpQQnhCb25ReGhxUDBrU0lOcnJlWmx1UDl1L1kyUEQ0eHFLb0MxV0JoaFNOWDlXOHJPY21DUlV1WVQ4ckVZK0JHNnVkQkFsNm5WNWZJdnhtRnhNYWw5WkhmbjJNTEEvem5iN3V5TlErcGttNm94eUdQN2pER2gzVmVzcldJM0FSYy9mdFVlc09zcW03YThrN0FETDBBR3p4TVJTajdNUk5KMkFwTElPNjlTdE83dEZObHcyN0phVGtaY1EzbHFTdFYyN2UvU0h6VHB5c2Rycm1DY2c2d2xnc3NaSFdadWJaVWpycE1TWU1MK2MzMVJBK2dOdTYyWlRzQkJKeTRDZ0paeHZYOTlZOTZ0UlY4VTVPd080MkZVQThhak80SmJOWXM3K3BKejgxWmhhTVBGWnFZcVkwaU9BbFIyVzltQmdpcGJwb2o2R3dpbWdxUlZWL2l6dFBQR1FIR3pxMHVSZTZTT2hrQlVoMWJkL2EiLCJtYWMiOiJkYjE3YTViZDQ1ZjJkMDUyYmQ1NWJkMGE1MDM4NmI4MjU3MTc3Yjc0Y2NkZGRjMjEyMGFkNGYzMGQwOWFlOTQ0IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-2104150824 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">zwnmKcRLmNWGDZ856YJrjbbNuEV0Tli3iJOiUNS6</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">HxnOnSuhAAPTfN2VS4q58slgJoytwtHYqaX0oLoW</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2104150824\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-2118681070 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 17 Jun 2025 13:10:06 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6ImNCbWwwZFZSeW0vNG8rajM1bmFEd1E9PSIsInZhbHVlIjoiaWdZSVVBRkVWUjg3TVh0eHV5cndVUmZ4Y2x5YXF2QmRuRFp5YTVzT1Iyb3o0VHJ1Y0VNcURWL2pIbjlRNlVaWThidWVSVWkreCszbklDU1hUY1M5aDllWmIweGNVZEgxSlpwMzhBNXlxeFkzc29RaDRGVGhWcVlCbHAxYnNsc29rM2lzZkdvYmI4WnZkZjEycVBVdUx0OHAzczJJbTNVSThyVTJaNVQ2cmZqd2ZtZmZtTllrWGJqTFcwRkNZYmZOMWJNNm9sUFVyOVFNbm0vd3VLMWdyOTBVTkw4NytkZDd4Y2lVZVZsQVdLaEFpSDlqd1lHbUp3NmZXekhzN2NOSkN0SFBtcXFxT3V2SzhvbWEyUmVBSktEYndqdnpmVlY4WW1BYWJOcVAzNkszaUdQQitya3J2VC8zK3pyMFYraUlOcmZVS1hBS1ZVOURFSnFYVlhIUmdPK04zQWVwN3VIU0JYQU0ydUI0aFdkZExMdHc3RDZRZ3JrT0Yza0hXbE5HZlNvODkxNzFnWVFWOFVKT3o3SnU3UytsNEtxMWo4SC9XK1dBTVlza1Z3bC9wdEErVG8ycERRcVVnK3ZhUzBrV2hNc0NhZThOdTlRdWd4ZFhscEI3S01HdDFxazBWQkl1ZUpUeVRvUGR0Wm1ZdUFEcFBZdTAvdldTcDcxU0NlY0kiLCJtYWMiOiIxNjgxMDRhZmM2YzQ4NTQ1ZThhYWEwYWYxOWYxNjY0M2U3NmQ5NGQ2ZWY1MzgzZWY3NzEzYWM5NWRkZjJiY2VmIiwidGFnIjoiIn0%3D; expires=Tue, 17 Jun 2025 15:10:06 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6InQ2ZkRzV2dOUWtmcTJ2R3U5d1dpdGc9PSIsInZhbHVlIjoiby9kMUN4Ly9EWkRtcGwyOTFIamM1dEovUUFpcmorRTNGR3JPTHNaekZKdXdYVWdTUHhHWjZraERmT3NaVEZYV2xXdVQvc3VYVENmelY1elg2ZC9jNWRIM0lxYUYwc2xwRDVhYmw3VjN4TVl5NDVwSkNoT20xRURzRTZxU1NYa2RqaDBwWjRUYTREcmw1UEhkTDNPRFF0QVlMZ3oxcngwWG0wNmVqai9FUWM5cVdiSkhQTnNvQmNUL2ZGdXVmUlJlTk9UZXRnOVZTU1h0cWFnZGR2Yjg5R2JPRnpKME5sZncvYnpaRFdkZ1lFcFlWOGxvL1d5ZzEwbWdyam4rVWNZd1FyWnQ1OW1kNnpURHZ4RzFLeDZCU2xuVEViQUcwdkxMSlVHNy9GVHVWU210cmJDcWFjT1pINnJqWkJwREVPemZFT2R0My9id053eGxYZkM4Rm9CVG94blplTFhKSk1QcnlnVElWVW9rNW9SVGsrM3QwQ2I0bkJKYnVzN0E2cENHbDY3aG1WZVpoeDk1V0twNzR5TVNPb3VrekIwL0FSc0F2Skd5SnJ4VVBscnNzR1Q3amllaEJrd0JoUGxmc2dkZ1Nhc2hCdGIydlcwT1g5OXFONEtwOExjT3ZRcUxkRTVHR0hOcG1WdEdqMWVwZ3hSL0IrS3dGUEZGeEcvbzAxZ1EiLCJtYWMiOiI3OTZhNWQyOTNlZmJlZDQxOWZhODZhNThkYjEzODlmODZiM2NjNzc0ZTc3MWUxM2RmZmZiZjM5ZTI2ODI2NTQ1IiwidGFnIjoiIn0%3D; expires=Tue, 17 Jun 2025 15:10:06 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6ImNCbWwwZFZSeW0vNG8rajM1bmFEd1E9PSIsInZhbHVlIjoiaWdZSVVBRkVWUjg3TVh0eHV5cndVUmZ4Y2x5YXF2QmRuRFp5YTVzT1Iyb3o0VHJ1Y0VNcURWL2pIbjlRNlVaWThidWVSVWkreCszbklDU1hUY1M5aDllWmIweGNVZEgxSlpwMzhBNXlxeFkzc29RaDRGVGhWcVlCbHAxYnNsc29rM2lzZkdvYmI4WnZkZjEycVBVdUx0OHAzczJJbTNVSThyVTJaNVQ2cmZqd2ZtZmZtTllrWGJqTFcwRkNZYmZOMWJNNm9sUFVyOVFNbm0vd3VLMWdyOTBVTkw4NytkZDd4Y2lVZVZsQVdLaEFpSDlqd1lHbUp3NmZXekhzN2NOSkN0SFBtcXFxT3V2SzhvbWEyUmVBSktEYndqdnpmVlY4WW1BYWJOcVAzNkszaUdQQitya3J2VC8zK3pyMFYraUlOcmZVS1hBS1ZVOURFSnFYVlhIUmdPK04zQWVwN3VIU0JYQU0ydUI0aFdkZExMdHc3RDZRZ3JrT0Yza0hXbE5HZlNvODkxNzFnWVFWOFVKT3o3SnU3UytsNEtxMWo4SC9XK1dBTVlza1Z3bC9wdEErVG8ycERRcVVnK3ZhUzBrV2hNc0NhZThOdTlRdWd4ZFhscEI3S01HdDFxazBWQkl1ZUpUeVRvUGR0Wm1ZdUFEcFBZdTAvdldTcDcxU0NlY0kiLCJtYWMiOiIxNjgxMDRhZmM2YzQ4NTQ1ZThhYWEwYWYxOWYxNjY0M2U3NmQ5NGQ2ZWY1MzgzZWY3NzEzYWM5NWRkZjJiY2VmIiwidGFnIjoiIn0%3D; expires=Tue, 17-Jun-2025 15:10:06 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6InQ2ZkRzV2dOUWtmcTJ2R3U5d1dpdGc9PSIsInZhbHVlIjoiby9kMUN4Ly9EWkRtcGwyOTFIamM1dEovUUFpcmorRTNGR3JPTHNaekZKdXdYVWdTUHhHWjZraERmT3NaVEZYV2xXdVQvc3VYVENmelY1elg2ZC9jNWRIM0lxYUYwc2xwRDVhYmw3VjN4TVl5NDVwSkNoT20xRURzRTZxU1NYa2RqaDBwWjRUYTREcmw1UEhkTDNPRFF0QVlMZ3oxcngwWG0wNmVqai9FUWM5cVdiSkhQTnNvQmNUL2ZGdXVmUlJlTk9UZXRnOVZTU1h0cWFnZGR2Yjg5R2JPRnpKME5sZncvYnpaRFdkZ1lFcFlWOGxvL1d5ZzEwbWdyam4rVWNZd1FyWnQ1OW1kNnpURHZ4RzFLeDZCU2xuVEViQUcwdkxMSlVHNy9GVHVWU210cmJDcWFjT1pINnJqWkJwREVPemZFT2R0My9id053eGxYZkM4Rm9CVG94blplTFhKSk1QcnlnVElWVW9rNW9SVGsrM3QwQ2I0bkJKYnVzN0E2cENHbDY3aG1WZVpoeDk1V0twNzR5TVNPb3VrekIwL0FSc0F2Skd5SnJ4VVBscnNzR1Q3amllaEJrd0JoUGxmc2dkZ1Nhc2hCdGIydlcwT1g5OXFONEtwOExjT3ZRcUxkRTVHR0hOcG1WdEdqMWVwZ3hSL0IrS3dGUEZGeEcvbzAxZ1EiLCJtYWMiOiI3OTZhNWQyOTNlZmJlZDQxOWZhODZhNThkYjEzODlmODZiM2NjNzc0ZTc3MWUxM2RmZmZiZjM5ZTI2ODI2NTQ1IiwidGFnIjoiIn0%3D; expires=Tue, 17-Jun-2025 15:10:06 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2118681070\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1667028655 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">zwnmKcRLmNWGDZ856YJrjbbNuEV0Tli3iJOiUNS6</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"41 characters\">http://localhost/financial/productservice</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1667028655\", {\"maxDepth\":0})</script>\n"}}