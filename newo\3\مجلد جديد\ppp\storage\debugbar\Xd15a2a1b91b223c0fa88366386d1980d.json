{"__meta": {"id": "Xd15a2a1b91b223c0fa88366386d1980d", "datetime": "2025-06-17 13:34:53", "utime": **********.72511, "method": "GET", "uri": "/customer/check/delivery?customer_id=10", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.158962, "end": **********.725132, "duration": 0.5661699771881104, "duration_str": "566ms", "measures": [{"label": "Booting", "start": **********.158962, "relative_start": 0, "end": **********.65244, "relative_end": **********.65244, "duration": 0.49347805976867676, "duration_str": "493ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.652452, "relative_start": 0.49348998069763184, "end": **********.725135, "relative_end": 3.0994415283203125e-06, "duration": 0.07268309593200684, "duration_str": "72.68ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 44977760, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET customer/check/delivery", "middleware": "web, verified", "controller": "App\\Http\\Controllers\\CustomerController@checkDelivery", "namespace": null, "prefix": "", "where": [], "as": "customer.check.delivery", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FHttp%2FControllers%2FCustomerController.php&line=365\" onclick=\"\">app/Http/Controllers/CustomerController.php:365-378</a>"}, "queries": {"nb_statements": 2, "nb_failed_statements": 0, "accumulated_duration": 0.014070000000000001, "accumulated_duration_str": "14.07ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 16 limit 1", "type": "query", "params": [], "bindings": ["16"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/AuthManager.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\AuthManager.php", "line": 57}, {"index": 23, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 33}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.696046, "duration": 0.01315, "duration_str": "13.15ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 93.461}, {"sql": "select * from `customers` where `customers`.`id` = '10' limit 1", "type": "query", "params": [], "bindings": ["10"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/CustomerController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Http\\Controllers\\CustomerController.php", "line": 368}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.7141619, "duration": 0.00092, "duration_str": "920μs", "memory": 0, "memory_str": null, "filename": "CustomerController.php:368", "source": "app/Http/Controllers/CustomerController.php:368", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FHttp%2FControllers%2FCustomerController.php&line=368", "ajax": false, "filename": "CustomerController.php", "line": "368"}, "connection": "kdmkjkqknb", "start_percent": 93.461, "width_percent": 6.539}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\Customer": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FModels%2FCustomer.php&line=1", "ajax": false, "filename": "Customer.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "16", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]", "pos": "array:1 [\n  2044 => array:9 [\n    \"name\" => \"تجربية\"\n    \"quantity\" => 1\n    \"price\" => \"10.00\"\n    \"id\" => \"2044\"\n    \"tax\" => 0\n    \"subtotal\" => 10.0\n    \"originalquantity\" => 1\n    \"product_tax\" => \"-\"\n    \"product_tax_id\" => 0\n  ]\n]"}, "request": {"path_info": "/customer/check/delivery", "status_code": "<pre class=sf-dump id=sf-dump-1692328176 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1692328176\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-1542623232 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>customer_id</span>\" => \"<span class=sf-dump-str title=\"2 characters\">10</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1542623232\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1641322656 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1936 characters\">_clck=2gyfy%7C2%7Cfwu%7C0%7C1994; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clsk=1ti4td2%7C1750167218299%7C2%7C1%7Cl.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6InVtYlRtaGt3TnJVaGM3dityL3p0SFE9PSIsInZhbHVlIjoiei9GMEplVm0xRVBBZ3hGb2wwV3A4ajdXVHhTdTFiYVFBMjZ4emVyTnVrb3IvazAzbk9NVFY1WFpRdE95Z01XTktlVzZYNWhyMVQ1cUh6YUJ4c3pxNjMySXVWaURoZEFrK2pUUE1NOTFTNkFVV2dSc2JYUHFLTTRkQUltL3pqc2RXSzViUUREME91WnJyYTlZTTFhdmJtOUZNdHZ4RUVhNHVpTTYvNUdaZjV0WGJ3ckl0Sks4NVlLVXVjVitoR3RUZERvZS9lQ2ZnakRpRWNrQlFzNis1cUk3YjFtYUd6NTVTTktCZXJaMkVjNnNIdUNjVHRFeXJVbENKbnNPZ3lqejRac2dWcXlkSWxONVpiYTFiUGNLWnlrUkhFV2MyV3FNUldYTTZEUThxQnIzWkxGN0NGQmlldThMbWVDazVlUWhLakl2dGd5M1Vzak5lS25ldnk1VGtSYVVYaXR3OVBCUnRWamFEeFREOHdGU2o5Zmc1VFNsdHRKNGx6blphdFVlZ3VPc3EyUWxhTDM2MmowdUpjS05VWEY3OVJFS3Y5cmdNSzJuUlRYcGFtR2pJRzVJWWRZV0dIZGF1MU5xV3hEZWZhdnk0NkViS2t0MEVWczdPSGVxK2d3MEhYS1VWb3dRNjQzM1Bxc0pROWlJK0tvQ25BZ1kvc2lRNDNQczhESnAiLCJtYWMiOiJhOGM3MmNkODkxYzkzZGQ0YWZiOTc3MzFhMTM3ZWI3OTFjZTc3ZTMyNDk4N2NmNjBhMzVmODU5NmNhNjhjMWNmIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6Imw0dlpYTExkS0tRRlN3RFVZRU5xOFE9PSIsInZhbHVlIjoiQnlweGE4TlZPNm1YYnVicnVLWkFDOUZJZ3lZYm9CQTVpeGNvWXlpMG1zdW5XMko5V29VdDRwdkpkbHNTVm1SOVJ2WUFjOXJJTnk4SzdxbkFQblRPWG9HVUozQXdVT1FibVp2LzY4MXFJSnJLbWpwT1NxTTFScXRacy90M2IwNk5QZHlobzRKKzdmUHUra0xqcUdvZFlHUi96YXFjV1E0T2xHdFNiclVRU1drbVZqQlJrZG9OYmxPQytLZmFQY2wvUnM5YzhudXpXejI0OGd1Tnl5TThydlZwRmVGK2R0TmlPZHF3VWxzWU9YZ29TeUFaN04yeVZNMDBWZ3VNZ2diU3BnRk1GWVliN09NQjkrYnlTMHp5aUcxcWpGcUlSK1B5bkplL2ltbGtjamNzMEt5Qjh4Y1pmVHpkSE1FVjZrZWFTVzArR1ptRGRHZXF4SDhjbitTa1JBczA4NTNwc2lpWVhhSmgzWFhqZ01LSVgzSGlRWll1ak8xZmFxTzAydFkrdi8wSVpvaG1XVjY5VTZ0M3dDN3ZReHlFY1AvQklVQzVlYTlnOTFoWW9zQ0VBbjVPRzJDZ0h3KzRtNTNHbWdjbWJHWlFIMlZyOW1JSnZMRkt3eEUvZkxwc3pnRlJtbWI4N2Z4WEU1QkYybDhQcGhFRmR6ajIzUndlN2JZMDNvMlQiLCJtYWMiOiJjMjUzMzRjNTA3NzEzN2Y5YWJiZWE1MWQ0MjUyYmM3NTQ5M2I4OTc1OTliZWJkZjYzNWYxYTI5NDI1N2U3NmU0IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1641322656\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1696130928 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">VloaUQCBjpDKrel7nHeAzB1sTLdTMwteSos1MmAN</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1696130928\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 17 Jun 2025 13:34:53 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Imx5N0YrQnZzT3NVVGxLcjRXSWJ6S1E9PSIsInZhbHVlIjoicFQvRnNPKzZscEFBN3VlQlI0Q0JjeW9PbXJuanMyRVlCKy9WTUpkZXVwa2cvYmpGb1F1RkZOaFFHZHhqdGVRYWZnaDAwM0NHQjJCOVZocWhaSlVVNmdFQ2ZkMktnRElsMjYwaVowQU82eWlYS0NQcmM1WUxQcmtONyttMEVjZ2tLWEpXZCsvdmRCQlNaSXVwU0ZJbHNZVXBwYjVOSk85ZDZQQlkySlBibFpXTlhPWnorSW9jTEk2YktldXBoNndVbmZGcURFUUU3LzRqQy90UnJ1Q0JrWmUvUmtUYnUrOUs5d0dERXA2WjRDOXFJc0RiRmRhaVhsMmNwS3UxZENxVkkwWktRNVpzRTdkNi9QWTBsLzh5WnY1VkVSdXBoMVVVVjNubGpIWEJiOG1CTVEzcmlxUU9WZXpFYzRyaVVHNmtLb2tyZzEydC8rbGc3M212cmdJcERhSTE4Q1hEamhXZ1hZQS9jV1RkelhtejNkL3ZTcFcvNG5OajJ2QlJQSnoyRmIraC9UYlJSZTRaaGRnVlRxWmZ2OHF2UndvM0F4YkY5ZHRJcGlGL3o2WjlCVzg4WUlZYXoxRENmazVleHRnbzVTcHFkeGFQak1mMDY2WjYrSGVBYVBCc1NmZDJRbXFHNVlVbnBTdFlJLzVDYUViQnJEcWJwR2N2c0htWXhsVkYiLCJtYWMiOiIxYjcxN2Q3YzY4OGZlMTUxNDBlMTMzYjA4Y2QwZjI0ZTQwODY1N2IyNzk4M2Y3NWMwNzI4Mjk3NjMxZTFjMzM1IiwidGFnIjoiIn0%3D; expires=Tue, 17 Jun 2025 15:34:53 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6Ijk5Q3hXVnBNV2IwUW0rQTJWWDRvRHc9PSIsInZhbHVlIjoiVWdvZUVMN243bllXOTNyQkM1VXc5UzVleFdnT2JXc2k2Q1NHSGU1RFVOT2pqSUZHY2QySDRNU3VTMVZqRlg0S3prSWxNQnRDcHdjU2NaMGFWMFg3RTBoOFdIdzdvL1ZFcmZ2eUtDRFFNd2tzeVZOV21GQXViY2FJWTRvQlZyL2JNbVl2Nyt1QjlXbUJBd0IrazIzQlBlQ1F3TnU4RGRsc0RkNEZ4dWhlRGR5bWwzMXVDYzNTNUUwSTJHaStucGp2dHZuOWozVlhYL3ZmMDlKdHRzWEpkRjRPdU94Zm83TW1TR3hPMmIyU3VBRWxyZ2xlWk9pUVZTTWo4U251NHZHTDJhZTd3djBuNXF3WHdOT3pmQi9ScS9SSkQxdjRGdmhpQmVieHFmK1Z1cjQ3OVNEL0NoQXJhM3ppbVNiaXlrMGdrSkF2eHZvYittcUQ3Mzk2UzJETiszUHBpR1J6Mk0zTUVmRlc0L0NjdXFiMk5aa3ptLzAwUGI0R3NiankwME1LNytCUnoxMWdLM2F3cFNkWCtqUERIUElQM3hiU1hIcnI2bUhpRlhEQ2lRMjM4d2VnWk93cW1Kb2xxSGpSclN4dmVNckVObkZid0pwRmFBRFFSUmQ2czFObjJRZU5tL3ZGY2Z5eG9VN0xrbTdCOU9BZkswYTdvRDlwd3NhYVBJM1ciLCJtYWMiOiJjODFlZjNjNGY0OTU3NDAwMDIxM2NlYTE0OTMzYmYxMjExMmM4NmZhODUzMDRjYjQ1MTQ4MTFkYmVhMzJjMDIyIiwidGFnIjoiIn0%3D; expires=Tue, 17 Jun 2025 15:34:53 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Imx5N0YrQnZzT3NVVGxLcjRXSWJ6S1E9PSIsInZhbHVlIjoicFQvRnNPKzZscEFBN3VlQlI0Q0JjeW9PbXJuanMyRVlCKy9WTUpkZXVwa2cvYmpGb1F1RkZOaFFHZHhqdGVRYWZnaDAwM0NHQjJCOVZocWhaSlVVNmdFQ2ZkMktnRElsMjYwaVowQU82eWlYS0NQcmM1WUxQcmtONyttMEVjZ2tLWEpXZCsvdmRCQlNaSXVwU0ZJbHNZVXBwYjVOSk85ZDZQQlkySlBibFpXTlhPWnorSW9jTEk2YktldXBoNndVbmZGcURFUUU3LzRqQy90UnJ1Q0JrWmUvUmtUYnUrOUs5d0dERXA2WjRDOXFJc0RiRmRhaVhsMmNwS3UxZENxVkkwWktRNVpzRTdkNi9QWTBsLzh5WnY1VkVSdXBoMVVVVjNubGpIWEJiOG1CTVEzcmlxUU9WZXpFYzRyaVVHNmtLb2tyZzEydC8rbGc3M212cmdJcERhSTE4Q1hEamhXZ1hZQS9jV1RkelhtejNkL3ZTcFcvNG5OajJ2QlJQSnoyRmIraC9UYlJSZTRaaGRnVlRxWmZ2OHF2UndvM0F4YkY5ZHRJcGlGL3o2WjlCVzg4WUlZYXoxRENmazVleHRnbzVTcHFkeGFQak1mMDY2WjYrSGVBYVBCc1NmZDJRbXFHNVlVbnBTdFlJLzVDYUViQnJEcWJwR2N2c0htWXhsVkYiLCJtYWMiOiIxYjcxN2Q3YzY4OGZlMTUxNDBlMTMzYjA4Y2QwZjI0ZTQwODY1N2IyNzk4M2Y3NWMwNzI4Mjk3NjMxZTFjMzM1IiwidGFnIjoiIn0%3D; expires=Tue, 17-Jun-2025 15:34:53 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6Ijk5Q3hXVnBNV2IwUW0rQTJWWDRvRHc9PSIsInZhbHVlIjoiVWdvZUVMN243bllXOTNyQkM1VXc5UzVleFdnT2JXc2k2Q1NHSGU1RFVOT2pqSUZHY2QySDRNU3VTMVZqRlg0S3prSWxNQnRDcHdjU2NaMGFWMFg3RTBoOFdIdzdvL1ZFcmZ2eUtDRFFNd2tzeVZOV21GQXViY2FJWTRvQlZyL2JNbVl2Nyt1QjlXbUJBd0IrazIzQlBlQ1F3TnU4RGRsc0RkNEZ4dWhlRGR5bWwzMXVDYzNTNUUwSTJHaStucGp2dHZuOWozVlhYL3ZmMDlKdHRzWEpkRjRPdU94Zm83TW1TR3hPMmIyU3VBRWxyZ2xlWk9pUVZTTWo4U251NHZHTDJhZTd3djBuNXF3WHdOT3pmQi9ScS9SSkQxdjRGdmhpQmVieHFmK1Z1cjQ3OVNEL0NoQXJhM3ppbVNiaXlrMGdrSkF2eHZvYittcUQ3Mzk2UzJETiszUHBpR1J6Mk0zTUVmRlc0L0NjdXFiMk5aa3ptLzAwUGI0R3NiankwME1LNytCUnoxMWdLM2F3cFNkWCtqUERIUElQM3hiU1hIcnI2bUhpRlhEQ2lRMjM4d2VnWk93cW1Kb2xxSGpSclN4dmVNckVObkZid0pwRmFBRFFSUmQ2czFObjJRZU5tL3ZGY2Z5eG9VN0xrbTdCOU9BZkswYTdvRDlwd3NhYVBJM1ciLCJtYWMiOiJjODFlZjNjNGY0OTU3NDAwMDIxM2NlYTE0OTMzYmYxMjExMmM4NmZhODUzMDRjYjQ1MTQ4MTFkYmVhMzJjMDIyIiwidGFnIjoiIn0%3D; expires=Tue, 17-Jun-2025 15:34:53 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>16</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pos</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-key>2044</span> => <span class=sf-dump-note>array:9</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"6 characters\">&#1578;&#1580;&#1585;&#1576;&#1610;&#1577;</span>\"\n      \"<span class=sf-dump-key>quantity</span>\" => <span class=sf-dump-num>1</span>\n      \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"5 characters\">10.00</span>\"\n      \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"4 characters\">2044</span>\"\n      \"<span class=sf-dump-key>tax</span>\" => <span class=sf-dump-num>0</span>\n      \"<span class=sf-dump-key>subtotal</span>\" => <span class=sf-dump-num>10.0</span>\n      \"<span class=sf-dump-key>originalquantity</span>\" => <span class=sf-dump-num>1</span>\n      \"<span class=sf-dump-key>product_tax</span>\" => \"<span class=sf-dump-str>-</span>\"\n      \"<span class=sf-dump-key>product_tax_id</span>\" => <span class=sf-dump-num>0</span>\n    </samp>]\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n"}}