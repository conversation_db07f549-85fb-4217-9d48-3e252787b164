{"__meta": {"id": "Xda47e3d26f83282b23d8e0c35ac684cb", "datetime": "2025-06-17 13:30:34", "utime": **********.67647, "method": "GET", "uri": "/product-categories", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.043419, "end": **********.676495, "duration": 0.6330761909484863, "duration_str": "633ms", "measures": [{"label": "Booting", "start": **********.043419, "relative_start": 0, "end": **********.545845, "relative_end": **********.545845, "duration": 0.5024261474609375, "duration_str": "502ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.545857, "relative_start": 0.5024380683898926, "end": **********.676498, "relative_end": 2.86102294921875e-06, "duration": 0.13064098358154297, "duration_str": "131ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 49311800, "peak_usage_str": "47MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET product-categories", "middleware": "web, verified, auth, XSS, category.access", "controller": "App\\Http\\Controllers\\ProductServiceCategoryController@getProductCategories", "namespace": null, "prefix": "", "where": [], "as": "product.categories", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FHttp%2FControllers%2FProductServiceCategoryController.php&line=203\" onclick=\"\">app/Http/Controllers/ProductServiceCategoryController.php:203-229</a>"}, "queries": {"nb_statements": 6, "nb_failed_statements": 0, "accumulated_duration": 0.027430000000000003, "accumulated_duration_str": "27.43ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 16 limit 1", "type": "query", "params": [], "bindings": ["16"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.5896711, "duration": 0.019260000000000003, "duration_str": "19.26ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 70.215}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.621375, "duration": 0.00087, "duration_str": "870μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 70.215, "width_percent": 3.172}, {"sql": "select `permissions`.*, `model_has_permissions`.`model_id` as `pivot_model_id`, `model_has_permissions`.`permission_id` as `pivot_permission_id`, `model_has_permissions`.`model_type` as `pivot_model_type` from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `model_has_permissions`.`model_id` = 16 and `model_has_permissions`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["16", "App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 326}, {"index": 21, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 566}], "start": **********.644792, "duration": 0.00082, "duration_str": "820μs", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:326", "source": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php:326", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=326", "ajax": false, "filename": "HasPermissions.php", "line": "326"}, "connection": "kdmkjkqknb", "start_percent": 73.387, "width_percent": 2.989}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (16) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 312}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 26, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}], "start": **********.648286, "duration": 0.00063, "duration_str": "630μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 76.376, "width_percent": 2.297}, {"sql": "select `product_service_categories`.*, COUNT(pu.category_id) product_services from `product_service_categories` left join `product_services` as `pu` on `product_service_categories`.`id` = `pu`.`category_id` where `product_service_categories`.`created_by` = 15 and `product_service_categories`.`type` = 'product & service' group by `product_service_categories`.`id` order by `product_service_categories`.`id` desc", "type": "query", "params": [], "bindings": ["15", "product &amp; service"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/ProductServiceCategory.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Models\\ProductServiceCategory.php", "line": 116}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/ProductServiceCategoryController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Http\\Controllers\\ProductServiceCategoryController.php", "line": 206}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.655248, "duration": 0.00372, "duration_str": "3.72ms", "memory": 0, "memory_str": null, "filename": "ProductServiceCategory.php:116", "source": "app/Models/ProductServiceCategory.php:116", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FModels%2FProductServiceCategory.php&line=116", "ajax": false, "filename": "ProductServiceCategory.php", "line": "116"}, "connection": "kdmkjkqknb", "start_percent": 78.673, "width_percent": 13.562}, {"sql": "select count(*) as aggregate from `product_services` left join `product_service_categories` as `c` on `c`.`id` = `product_services`.`category_id` where `product_services`.`type` = 'product' and `product_services`.`created_by` = 15", "type": "query", "params": [], "bindings": ["product", "15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/ProductServiceCategoryController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Http\\Controllers\\ProductServiceCategoryController.php", "line": 209}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.663234, "duration": 0.00213, "duration_str": "2.13ms", "memory": 0, "memory_str": null, "filename": "ProductServiceCategoryController.php:209", "source": "app/Http/Controllers/ProductServiceCategoryController.php:209", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FHttp%2FControllers%2FProductServiceCategoryController.php&line=209", "ajax": false, "filename": "ProductServiceCategoryController.php", "line": "209"}, "connection": "kdmkjkqknb", "start_percent": 92.235, "width_percent": 7.765}]}, "models": {"data": {"App\\Models\\ProductServiceCategory": {"value": 26, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FModels%2FProductServiceCategory.php&line=1", "ajax": false, "filename": "ProductServiceCategory.php", "line": "?"}}, "App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 28, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 1, "messages": [{"message": "[ability => create purchase, result => true, user => 16, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-876006974 data-indent-pad=\"  \"><span class=sf-dump-note>create purchase</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"15 characters\">create purchase</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>16</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-876006974\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.653856, "xdebug_link": null}]}, "session": {"_token": "ixL6BN3l9tyyUscqj0hv99yKIy0daqhD43fAAWcL", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "16", "pos": "array:1 [\n  2043 => array:9 [\n    \"name\" => \"zello sour\"\n    \"quantity\" => 1\n    \"price\" => \"7.00\"\n    \"id\" => \"2043\"\n    \"tax\" => 0\n    \"subtotal\" => 7.0\n    \"originalquantity\" => 19\n    \"product_tax\" => \"-\"\n    \"product_tax_id\" => 0\n  ]\n]"}, "request": {"path_info": "/product-categories", "status_code": "<pre class=sf-dump id=sf-dump-1544375745 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1544375745\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-1213300467 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1213300467\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-72173164 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-72173164\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1909084534 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">ixL6BN3l9tyyUscqj0hv99yKIy0daqhD43fAAWcL</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1937 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=1pubyvl%7C2%7Cfwu%7C0%7C1993; _clsk=25xk9k%7C1750165707106%7C8%7C1%7Cl.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IlhROEYzMm9waE1kaWFSTVl0QzA3dVE9PSIsInZhbHVlIjoiMUI4aEFMUzUxWS9WWThnVUduS0wwQStRWGptWjVvb0hLaGFpOVFDeDR6YlI1cElkQzRMUVJXYzBZS2ExL1ovT3N6UmorOVdpN3gxQWRON1l6bzdpNHRvT3RNSTNWc2dUMmwrK1ZUcXV2Lzc5bjdGbkZURTVHOFMxMVlmS0cwc1prYjNTczl6ZHV1KzNLSHY4OWY2MlZFdXpmeSswTyt3ZWs0Q0xJbTZ1U05GV20xRWwybDlLVFI1VUZmVHVJcHQ1S0c4VWtEMWp0cmFuWUVkODIvcGxoS2k5RXE4ZE9vb2JKanJLcERXVGZIckVTS01wOEkwSTVDVEV2aFh2eWxlVkhJTUF1VlRPUGVmaUJ5ZlpoOGh0UEpEZnRneTh5NHFETng0eVJYcUJEMjlTQnZHRW55Ykl2Q3FzSkJPTjkySmxCcytkZWRsTjVRQ2wxZVBGaWpkTTdTWXhJei9KR1IwdnJ5OHZDOVJvdy9rQU9ORTNKSEt6dlNLR1AyNWUxUE9zOHhua0JqM3ZKaHR4TDJXVzRBWHplMDBKTlBQMm5ZM2RCZ1BRcWo1NXJDYzhiQ3E1UXRhUGQ3SjNJWGptekkyNVllN3VtYytuMHY3T1NublUyT2lpRGgzV2dyUHRPS0lweUhBbjVNTGNramtkR1YwY04rRkFvNUwvNEN2ejJKQ20iLCJtYWMiOiJmNDBkNjczNzA1NjgyNmI3ZTg2OGQ1ZDY4ZTFkZmY4YWQ0NTVjN2RmZDg2ZjhkMjAwZTVjM2ZlZjNkNGRjNDQ5IiwidGFnIjoiIn0%3D; test_session=eyJpdiI6IktwenFWeEZJajJZUzJ1Tm0xQ0lucmc9PSIsInZhbHVlIjoiM2pMVjhJazRCZGdGQUJ0bzgwd3VndExEK0FzMlZpZHdzZWpEMm9pOWRXQi9EYjJYcWd1b1hJMzlwRDZTMTdGWTlnbFNFSlg4bTlTdU1nalNXblVFMUlnbFo5RG9VZ3A5eEwzYkViVHR1Nk8xeWkrVVhST3c3NzA0cmpsd09vcVlQUzMyZk15UjVENDQ5ekRucWRDNkYxUUlrQXJDK0wyL0hSZiszenFXR29XblgwUzhaVS90aDRvd1l4NnRQNS8xNWpyVTNOdFlGSk1TNkFpRkpGN1dmUUdiaEtKNWswd29yNTI5aUJwclJaQXN2ai85MHJBZFpuc0lrZHVPd0lodjB0TS9HdGdWVUhqdnk1NnNNMEwwTkwzS2NXb0NJaEJhTFRvckUrSU1POFAvT0lYTUJsMU53RzJGNXhXY3NaMTEwWEhtL2cvbUNodCtFWFdOVW0yOEkyRXF3UDd6VmtrcGtVQ09FVDZVVlJnZUs2elIwNFg5dU5WbWFYQ0EyckFGVGZ1V1hDRCtXOXRrbnBFdjR1d2h4Tm50QWk1dzZmclMrL2VETXJ0S2tvNFh4d2oyYlhQU05OUEVXNnB1eXgvS2E3VHV4VHdHd0FSVit4T3Z6RHFyZlI3RVlqMkpuUXY2VzVtVitTS2llTkhPQ3lSSlRxa003aVRzRkY0RUFiMWsiLCJtYWMiOiI2ZWViNmJmYmEyMWJmN2RkNTJjNGJhZGM3NjdiOGY4ZjY4M2QwZGMyZmU1NDkwZTJmZDg4NDY3YWIxOGJkNGUxIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1909084534\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1170623235 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">ixL6BN3l9tyyUscqj0hv99yKIy0daqhD43fAAWcL</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">coJ97Z8YyNCeFgF4h0jUL1PrpYBjauguIcJbJbm8</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1170623235\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-126645914 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 17 Jun 2025 13:30:34 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IjEwOEJraWhzVjJBOEttOHhPUjc0Ymc9PSIsInZhbHVlIjoiYlZVbFoyZE9LdkYybk1GWUtpWGhzSUxrUzJ5Zm40QzRXZlNUN2loeW1VRlpMVVZmUEhBdGwzR3hUaGgxSnZpS2VkUHh2aGpIMXNUN0orVWdGZ1VzeTg2dlA2dkoxSFladlAxTHVpVHRzSEVGYkFHVkNhQWF6TnA0NFU5STlUNjlKd1djRXc5T0ZrMFFPQzgxYlJVQmFlUmlzMCtuTHlaeFZCTk1mRERDNnVoMDJJL1BvQUNUWWRkWmk0ckRHbHQrZnBQb3VDQ0p0QWlFMHZtTzhUZnczaC9HZ3krdGdvTVZMVzFyYzNkMzRCOFlTem5xcEtnQ0tXdHNSL3lSbUorTkd2NHhwM1V1WEk3aFhCT1JGNlo2YVFaS040OFBjc2N1QmIveVlFSFpjY0duVWRQcmZ6MU4vOGw5TDBaYkFOKzVBUFRtMXpkaG1ZTkFjSExrWTQreDNZc0dSSEE1YmZYUjZBWE02RnRJbmpVMSt6a1p6TE95dkdPWnNIcWhMRjRXUmJxa1BNQzFwb2dLem8zRjZodmtaeXJkMmtVWWxsMlBLMGpUemFBYnF3TFUwTlA1aHEzMUdwNWtWdldNUHlCT0lDYXFpbk45ZXZQcUJ6cUxrMmlkZDF4SDdaS1lvNWcvQUZiUGh5S2lTbmx6N2hhZzc3MWs0cFdYeUJCQmNZUGoiLCJtYWMiOiIzM2M0NzVhMTQ2OGJkNDE2MzVmNWJjOTFlMDNmODYwODk0NmM2MzVkMGUzMTQwY2IxNGEyNzY5MTEzODEyNDY4IiwidGFnIjoiIn0%3D; expires=Tue, 17 Jun 2025 15:30:34 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6ImNNWDBXOXhJY1h1bkFoaWlPdlJtM3c9PSIsInZhbHVlIjoiaTVEQnpXNjJEb1UzeFFCVTZNYmFPWUtvN05BcXBxVW0rU3hBbjhBY2t5d2FKT0p5VTFrREMzaUZDejRlb2pxL1lTdk1aN2sybERxR3ZqVEo4ZExHNCt0VE8rOXRLQlU0bFdJcEEwZHpsM1ZscGFFdHNRUWl0eTNmLzRGSmp3MGluZXB0dXlTSWxIOUpaNndhVE01S2g3ZldUZWw3L1VRbk1QZW9GazdVUUxPaVVtUC91WXVqdUNEZndRSEN5Z3ZkOGo4Kzcvem02cWhIQnZiTGVEaTBpbk0xV09tc2RYM1BuVTRrRHpvZkcveGR4YTVoMGV5TVQ4czZsNE9oRC9rZTdJMGllUjJSMmxaYmw2VE1meHBmTkhvY0RwWVFxZHppRlhodlJHeFAvb1pjVHBraUdBeXVkRUxKNXMwUktqeTQ2QnMvOHpPSjUwR0N3S1dwdVZ4WEFaUU05S1gyTnloZTU3MXI5TlZOTzBMaVg0c0VOTlExZHhGZG12YTk2ZkVINjYvM245eWVEcGFRSG1QWHR1YlFOVTFKdlhCeWszQmt3UnBXMUJIaStQaE5rbXVGcmNaelFqNm83V2QzMVI5K2lUa0VRUG41V2lUbk1zQlNuWllNQTM1RlRYUUw1b1lXb1MvUkRPcHRZL2ttZzI0VmEraytCV1FXOFNkamxRRGciLCJtYWMiOiI3ZWIxNTI1YzY3YjViMDljZTZmZjI0MzI2NjdkNWM2NDRhNmQyMjJiZGY5ZGI2NmViN2Y0YWEzN2ZjN2MzMzJmIiwidGFnIjoiIn0%3D; expires=Tue, 17 Jun 2025 15:30:34 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IjEwOEJraWhzVjJBOEttOHhPUjc0Ymc9PSIsInZhbHVlIjoiYlZVbFoyZE9LdkYybk1GWUtpWGhzSUxrUzJ5Zm40QzRXZlNUN2loeW1VRlpMVVZmUEhBdGwzR3hUaGgxSnZpS2VkUHh2aGpIMXNUN0orVWdGZ1VzeTg2dlA2dkoxSFladlAxTHVpVHRzSEVGYkFHVkNhQWF6TnA0NFU5STlUNjlKd1djRXc5T0ZrMFFPQzgxYlJVQmFlUmlzMCtuTHlaeFZCTk1mRERDNnVoMDJJL1BvQUNUWWRkWmk0ckRHbHQrZnBQb3VDQ0p0QWlFMHZtTzhUZnczaC9HZ3krdGdvTVZMVzFyYzNkMzRCOFlTem5xcEtnQ0tXdHNSL3lSbUorTkd2NHhwM1V1WEk3aFhCT1JGNlo2YVFaS040OFBjc2N1QmIveVlFSFpjY0duVWRQcmZ6MU4vOGw5TDBaYkFOKzVBUFRtMXpkaG1ZTkFjSExrWTQreDNZc0dSSEE1YmZYUjZBWE02RnRJbmpVMSt6a1p6TE95dkdPWnNIcWhMRjRXUmJxa1BNQzFwb2dLem8zRjZodmtaeXJkMmtVWWxsMlBLMGpUemFBYnF3TFUwTlA1aHEzMUdwNWtWdldNUHlCT0lDYXFpbk45ZXZQcUJ6cUxrMmlkZDF4SDdaS1lvNWcvQUZiUGh5S2lTbmx6N2hhZzc3MWs0cFdYeUJCQmNZUGoiLCJtYWMiOiIzM2M0NzVhMTQ2OGJkNDE2MzVmNWJjOTFlMDNmODYwODk0NmM2MzVkMGUzMTQwY2IxNGEyNzY5MTEzODEyNDY4IiwidGFnIjoiIn0%3D; expires=Tue, 17-Jun-2025 15:30:34 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6ImNNWDBXOXhJY1h1bkFoaWlPdlJtM3c9PSIsInZhbHVlIjoiaTVEQnpXNjJEb1UzeFFCVTZNYmFPWUtvN05BcXBxVW0rU3hBbjhBY2t5d2FKT0p5VTFrREMzaUZDejRlb2pxL1lTdk1aN2sybERxR3ZqVEo4ZExHNCt0VE8rOXRLQlU0bFdJcEEwZHpsM1ZscGFFdHNRUWl0eTNmLzRGSmp3MGluZXB0dXlTSWxIOUpaNndhVE01S2g3ZldUZWw3L1VRbk1QZW9GazdVUUxPaVVtUC91WXVqdUNEZndRSEN5Z3ZkOGo4Kzcvem02cWhIQnZiTGVEaTBpbk0xV09tc2RYM1BuVTRrRHpvZkcveGR4YTVoMGV5TVQ4czZsNE9oRC9rZTdJMGllUjJSMmxaYmw2VE1meHBmTkhvY0RwWVFxZHppRlhodlJHeFAvb1pjVHBraUdBeXVkRUxKNXMwUktqeTQ2QnMvOHpPSjUwR0N3S1dwdVZ4WEFaUU05S1gyTnloZTU3MXI5TlZOTzBMaVg0c0VOTlExZHhGZG12YTk2ZkVINjYvM245eWVEcGFRSG1QWHR1YlFOVTFKdlhCeWszQmt3UnBXMUJIaStQaE5rbXVGcmNaelFqNm83V2QzMVI5K2lUa0VRUG41V2lUbk1zQlNuWllNQTM1RlRYUUw1b1lXb1MvUkRPcHRZL2ttZzI0VmEraytCV1FXOFNkamxRRGciLCJtYWMiOiI3ZWIxNTI1YzY3YjViMDljZTZmZjI0MzI2NjdkNWM2NDRhNmQyMjJiZGY5ZGI2NmViN2Y0YWEzN2ZjN2MzMzJmIiwidGFnIjoiIn0%3D; expires=Tue, 17-Jun-2025 15:30:34 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-126645914\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">ixL6BN3l9tyyUscqj0hv99yKIy0daqhD43fAAWcL</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>16</span>\n  \"<span class=sf-dump-key>pos</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-key>2043</span> => <span class=sf-dump-note>array:9</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"10 characters\">zello sour</span>\"\n      \"<span class=sf-dump-key>quantity</span>\" => <span class=sf-dump-num>1</span>\n      \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"4 characters\">7.00</span>\"\n      \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"4 characters\">2043</span>\"\n      \"<span class=sf-dump-key>tax</span>\" => <span class=sf-dump-num>0</span>\n      \"<span class=sf-dump-key>subtotal</span>\" => <span class=sf-dump-num>7.0</span>\n      \"<span class=sf-dump-key>originalquantity</span>\" => <span class=sf-dump-num>19</span>\n      \"<span class=sf-dump-key>product_tax</span>\" => \"<span class=sf-dump-str>-</span>\"\n      \"<span class=sf-dump-key>product_tax_id</span>\" => <span class=sf-dump-num>0</span>\n    </samp>]\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n"}}