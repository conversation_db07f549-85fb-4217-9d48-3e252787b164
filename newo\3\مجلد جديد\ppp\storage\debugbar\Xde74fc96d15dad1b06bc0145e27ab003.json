{"__meta": {"id": "Xde74fc96d15dad1b06bc0145e27ab003", "datetime": "2025-06-17 13:36:00", "utime": **********.384009, "method": "POST", "uri": "/warehouse-empty-cart", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1750167359.774857, "end": **********.384031, "duration": 0.6091740131378174, "duration_str": "609ms", "measures": [{"label": "Booting", "start": 1750167359.774857, "relative_start": 0, "end": **********.297413, "relative_end": **********.297413, "duration": 0.5225560665130615, "duration_str": "523ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.297427, "relative_start": 0.5225698947906494, "end": **********.384033, "relative_end": 1.9073486328125e-06, "duration": 0.08660602569580078, "duration_str": "86.61ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 46355056, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST warehouse-empty-cart", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\ProductServiceController@warehouseemptyCart", "namespace": null, "prefix": "", "where": [], "as": "warehouse-empty-cart", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FHttp%2FControllers%2FProductServiceController.php&line=1663\" onclick=\"\">app/Http/Controllers/ProductServiceController.php:1663-1673</a>"}, "queries": {"nb_statements": 2, "nb_failed_statements": 0, "accumulated_duration": 0.00538, "accumulated_duration_str": "5.38ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 16 limit 1", "type": "query", "params": [], "bindings": ["16"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.35218, "duration": 0.00417, "duration_str": "4.17ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 77.509}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.371027, "duration": 0.00121, "duration_str": "1.21ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 77.509, "width_percent": 22.491}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "16", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]"}, "request": {"path_info": "/warehouse-empty-cart", "status_code": "<pre class=sf-dump id=sf-dump-802004789 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-802004789\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-1948658070 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1948658070\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-359955344 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>session_key</span>\" => \"<span class=sf-dump-str title=\"3 characters\">pos</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-359955344\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1297523324 data-indent-pad=\"  \"><span class=sf-dump-note>array:19</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">15</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1936 characters\">_clck=2gyfy%7C2%7Cfwu%7C0%7C1994; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clsk=1ti4td2%7C1750167218299%7C2%7C1%7Cl.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IkxuVG5SN0VvK29vOUdCOE4wWTNEcUE9PSIsInZhbHVlIjoiQ3ZaalJyT0FFTGkwbUpua2RnOE1JQUVFZHR0YStJdkRmZi83RFBLdVJsNmxuWEI2SmJwRlM1ejJSQUtTd3NZUktHd2ZKczFDc1JKaXNRMWdOS0VSNVFkZlRKS1NyYVRGSnd6MWhGRTFZMnorRDdsdE5OeFcrWjBZUDRCNnUwM3lsa2NiRFoyeDdDM0VkSVUrUGltOWZSaGZPZ29vY3ZZNUZRTWtaQ2ZYRENJSXZSSXJWOWJzc0hVZFJ4bWNXVFRaUEVNSzZEWUE5VlFsdFpEVjFlc0lTcDFvbS9kQXpGUkFOeUZHTmd2Y3BLM3VES3ljTE13OXpKUWh2NVRQTmgrcHBYczNZZ3FFTkhxY3cyN1d3UEdpUVNpOUdHbmZmR1lyQmpYWU51TFRrL0NJK0dOMXM0UDNoU0o5NlEwcnVmRHlJSzMvMUdTckNHRDdrNzdrTEtNMzh3VmtVN2JFSkcwQ0xrS2tROFFRU056dnZTSUtqd3k3NUZjV3kyb3lsWmlzM1NaQmU5c2YwSFQ2ZlAwZUM4QytrYUQwRkYvdGNhd0NFUS9CZmIwazFrN1Q3NHRnY1BOZlZMMENxekNXZFBjenl4TTlDUlRRUGJDSGN0VXFLVkpiNUw1L3UrL2sxOHExMWpON0M2OWJWSWZ6RVdjRmt4aEZxTTJqZ2pwK2RuQlEiLCJtYWMiOiJjYmE3MjUxYjI1ZTQ0ZTk3ZWE0NzU3ZmNkNGQyMGQzZTZmOGZhNWRjODc1ZjYzMzE5Mjg3ZmIyNmM0ZTA1NjZiIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6IkRjTFNmaGZ6RjAzUVpRSld1NjdWRnc9PSIsInZhbHVlIjoiM1pCVHF4c0R4MHowYWNieUoydDZ1NEFvL0FKWTIzeXFEMHJuOWQrVytQRlFxTTV3endtZG9wR3IyTWVxUzZIMjJNTS9sM1d2R3FIYnBmNnlNZGJpRnRkSWdnM2JTMm1RQ3E5L1FsclU5MllsUzh1THdRaHhpK2J4cDR5dzJiUnA3Ui9MdXlrY1owbmRRNzRqMFp3Z29nZTE0V1B2ak1rYkY0MG13MDFrMzV2TkVsSGkraUNpVDJhdWZCK3lYMjVLai9PWGQ4Mnh2NGVwcGZxeFNHNSsrQ2J5QVpYb3RWNENjRGhvcFZFVjhvMGNnNXZmVldqZWxnWHhLZ3IrdmQ3andHU1dpUWkyWGZuYk9Qeno0MC9tempsR21NdnlNWHUra3R4bGpiRlg1MHZpOUFXVGRXeHpIRlZLeFh5cFZvakw4dzZ6Y2VlOXBzSmFYLy9NbENlWmErRmdBS3JpaG9weTlHTWlrWXlzbUxla0J3eGZzamJLYTNKR1I5anQyUm1ab3NrcS9ZZm85UHdEcWhKbktieUYvTDdkQ0R2c1EzdkJhY1RVM3kvdmkyZm93TEFYVVRnemdKYm9SNXkxYUZzSko1eHVrUmkxd3pnakNOa3BkY1VBRmQ1YmdBWFBYUlBJcTZjcFRjY0E0WXhpZy9jcFBFekpLZWJKek5BbkFUOGkiLCJtYWMiOiIyNWJiODRiOGQ1NzZhMWZkNzYwY2JjNDFlZGZiYjU3OWY2MmU2NWQ1NDQyOTQyNjNiN2ViMmE4OTEyYjZmZjgzIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1297523324\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1336367468 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">VloaUQCBjpDKrel7nHeAzB1sTLdTMwteSos1MmAN</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1336367468\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1719168626 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 17 Jun 2025 13:36:00 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IjdmQjhYTnFyNlBmQTE3ZUZNRHNtQ1E9PSIsInZhbHVlIjoibVpoU05uOG1jUERhZVVKSi9oMEZpdGdSekRFVnpPZHVRVkN0eWZCQ21ucHQ3clVhSmlMazhvM0RWd1FoODRDT1JYN2NZc0RmQ3E1dmlNOFNIOEhJdzVoTlIrYjYvcmdDZGJxU3lZZjZvRS9Edkg2dFY1MENOYzNZWGZtZGhIbnRaTlNTWjZHZ0ZVRnEzbTh3NXA4UmQzclJ6cWlVQ2VhSFZRK01oZXFOVEZBMEFBL3l5YWFCT3dkVmUwQ2lBMnlPSW1GZ2NoZndFYlJ4UHNmL2RGZ3JhMmp1M1ZHT280VWhFRHlFSzJSRWZqN21BWUJKYU5seUZKS0RnYkhISzdkbExKeWxKNWErSmlETFhmN0JhR2NmTXZZdXdHZ3NlaHNCNFV0UXRBNTV2S3hnRTM2N3FyMU5rbDlFczc0WUw5ZjRkZU05RGVJdlkveENMczcvZkt3VHdoMVpmWHJ6UTlpRGIwSUtMOUNJS0xTdzJOM0pCQmU3N0lUODc2QmEyS3JncFFHWU80QW91UDN0NEY5bDFQaHdOTW5UbElNY0lYd1dCbzU3WW5VUi9UMEQvRmJ3cXNNMUV3ZDNVZldVeXZFRWxVVGhvUzcwRjU2clo2OHRnSUZFbFBIdTVKZ2xJWDdiSVpmdWlIK1lQWTVOZFU5N1UyWkhXMFM2dlg4OHNCZTMiLCJtYWMiOiJkMTA1MjNjY2ExY2FkMWYyYjAzMTA2MzM5ZDFiZDMwOThkZmYyZWVjZTcyZGMzZDgwNjlhYmZkODEzZGZhOTk3IiwidGFnIjoiIn0%3D; expires=Tue, 17 Jun 2025 15:36:00 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6IkFmZ0NqZk5MOFprYXF3c0xmVTFGTXc9PSIsInZhbHVlIjoiYi9DZ3R2dENoY3VlajFiN1p0RmVFUEMwVlM2U1JYRnZQMGlhd1RTYzVzWTdYdVNJb3VlbHV0WThNUnl6YUlmcnQzcjlJeGw4U091ZFlhNk1SU3JuQVZicXk5VUhXaHE2NjJrdTZjMlNub0NvejQxRUNGUTBRVVBZdGFpWEhSckxRRCsvNDl0RU94VnJmK05uZGVmWEtqNWVFcXRNOGxjWlRTUzVmWkFQam5uY2FjOC9BWWZqSmc3R1ZQNkxWb0V6S3lUNlp6eGxwU2RENlllQW1TcjNMYTIwTHU1VDVUMkVSbWZmcGVZMjFtK240eG1xYXNYWHgvd0ljQUd5VDl6UTgvZjA0VVltbVNWOVBORiswcEx1UklLOHFMd1p3QTJpZk56eFo4STlJYVY5bmdwZDBvcFZqMEVPd1o4eFBuaEtRTDFkRThUb2phREFLZDdHYldBeWg3QjFlOWFQY0liSmJpS1gybEpkRk4zR0JqeDZ3cklxM052dFE5WSttREJrVnIxMzB3alZCQW5pMUpqZ0RHSTQ4Zjd5OXRodzBISHkycEFDNXgvSVE4elZ2MkZ5SHlmKzNFNFEyTnFaOTE5Z3dqV005OFpjcEgwQlBJdXpZTThVWGZoaTUvZGRHaGRsWWtid1hjdjcxQ3dCeVQ0TjRyc056c0RQTFZCZG1vcjciLCJtYWMiOiIyMzI4ZTk1ZjkwMzAzNDUwOWY3MzRlZDQ0NDBkNGRhMmY4OGM4Yjc5N2RmODQyODg3MzBkYzFkNDdlMmVjMGQ1IiwidGFnIjoiIn0%3D; expires=Tue, 17 Jun 2025 15:36:00 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IjdmQjhYTnFyNlBmQTE3ZUZNRHNtQ1E9PSIsInZhbHVlIjoibVpoU05uOG1jUERhZVVKSi9oMEZpdGdSekRFVnpPZHVRVkN0eWZCQ21ucHQ3clVhSmlMazhvM0RWd1FoODRDT1JYN2NZc0RmQ3E1dmlNOFNIOEhJdzVoTlIrYjYvcmdDZGJxU3lZZjZvRS9Edkg2dFY1MENOYzNZWGZtZGhIbnRaTlNTWjZHZ0ZVRnEzbTh3NXA4UmQzclJ6cWlVQ2VhSFZRK01oZXFOVEZBMEFBL3l5YWFCT3dkVmUwQ2lBMnlPSW1GZ2NoZndFYlJ4UHNmL2RGZ3JhMmp1M1ZHT280VWhFRHlFSzJSRWZqN21BWUJKYU5seUZKS0RnYkhISzdkbExKeWxKNWErSmlETFhmN0JhR2NmTXZZdXdHZ3NlaHNCNFV0UXRBNTV2S3hnRTM2N3FyMU5rbDlFczc0WUw5ZjRkZU05RGVJdlkveENMczcvZkt3VHdoMVpmWHJ6UTlpRGIwSUtMOUNJS0xTdzJOM0pCQmU3N0lUODc2QmEyS3JncFFHWU80QW91UDN0NEY5bDFQaHdOTW5UbElNY0lYd1dCbzU3WW5VUi9UMEQvRmJ3cXNNMUV3ZDNVZldVeXZFRWxVVGhvUzcwRjU2clo2OHRnSUZFbFBIdTVKZ2xJWDdiSVpmdWlIK1lQWTVOZFU5N1UyWkhXMFM2dlg4OHNCZTMiLCJtYWMiOiJkMTA1MjNjY2ExY2FkMWYyYjAzMTA2MzM5ZDFiZDMwOThkZmYyZWVjZTcyZGMzZDgwNjlhYmZkODEzZGZhOTk3IiwidGFnIjoiIn0%3D; expires=Tue, 17-Jun-2025 15:36:00 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6IkFmZ0NqZk5MOFprYXF3c0xmVTFGTXc9PSIsInZhbHVlIjoiYi9DZ3R2dENoY3VlajFiN1p0RmVFUEMwVlM2U1JYRnZQMGlhd1RTYzVzWTdYdVNJb3VlbHV0WThNUnl6YUlmcnQzcjlJeGw4U091ZFlhNk1SU3JuQVZicXk5VUhXaHE2NjJrdTZjMlNub0NvejQxRUNGUTBRVVBZdGFpWEhSckxRRCsvNDl0RU94VnJmK05uZGVmWEtqNWVFcXRNOGxjWlRTUzVmWkFQam5uY2FjOC9BWWZqSmc3R1ZQNkxWb0V6S3lUNlp6eGxwU2RENlllQW1TcjNMYTIwTHU1VDVUMkVSbWZmcGVZMjFtK240eG1xYXNYWHgvd0ljQUd5VDl6UTgvZjA0VVltbVNWOVBORiswcEx1UklLOHFMd1p3QTJpZk56eFo4STlJYVY5bmdwZDBvcFZqMEVPd1o4eFBuaEtRTDFkRThUb2phREFLZDdHYldBeWg3QjFlOWFQY0liSmJpS1gybEpkRk4zR0JqeDZ3cklxM052dFE5WSttREJrVnIxMzB3alZCQW5pMUpqZ0RHSTQ4Zjd5OXRodzBISHkycEFDNXgvSVE4elZ2MkZ5SHlmKzNFNFEyTnFaOTE5Z3dqV005OFpjcEgwQlBJdXpZTThVWGZoaTUvZGRHaGRsWWtid1hjdjcxQ3dCeVQ0TjRyc056c0RQTFZCZG1vcjciLCJtYWMiOiIyMzI4ZTk1ZjkwMzAzNDUwOWY3MzRlZDQ0NDBkNGRhMmY4OGM4Yjc5N2RmODQyODg3MzBkYzFkNDdlMmVjMGQ1IiwidGFnIjoiIn0%3D; expires=Tue, 17-Jun-2025 15:36:00 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1719168626\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1176560529 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>16</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1176560529\", {\"maxDepth\":0})</script>\n"}}