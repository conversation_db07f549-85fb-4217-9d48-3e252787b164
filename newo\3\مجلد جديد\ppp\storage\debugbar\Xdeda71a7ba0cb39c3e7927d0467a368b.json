{"__meta": {"id": "Xdeda71a7ba0cb39c3e7927d0467a368b", "datetime": "2025-06-17 13:33:36", "utime": **********.898793, "method": "POST", "uri": "/chats/favorites", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.140907, "end": **********.898816, "duration": 0.7579090595245361, "duration_str": "758ms", "measures": [{"label": "Booting", "start": **********.140907, "relative_start": 0, "end": **********.795561, "relative_end": **********.795561, "duration": 0.6546540260314941, "duration_str": "655ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.795573, "relative_start": 0.6546659469604492, "end": **********.898818, "relative_end": 1.9073486328125e-06, "duration": 0.10324501991271973, "duration_str": "103ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 46132792, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/favorites", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getFavorites", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "favorites", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=454\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:454-477</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.021580000000000002, "accumulated_duration_str": "21.58ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 16 limit 1", "type": "query", "params": [], "bindings": ["16"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.841448, "duration": 0.020050000000000002, "duration_str": "20.05ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 92.91}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.874576, "duration": 0.00083, "duration_str": "830μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 92.91, "width_percent": 3.846}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (16) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.886567, "duration": 0.0007, "duration_str": "700μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 96.756, "width_percent": 3.244}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "16", "_previous": "array:1 [\n  \"url\" => \"http://localhost/hrm-dashboard\"\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/favorites", "status_code": "<pre class=sf-dump id=sf-dump-714957693 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-714957693\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-512710792 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-512710792\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-769211506 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-769211506\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1320031844 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"30 characters\">http://localhost/hrm-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1936 characters\">_clck=2gyfy%7C2%7Cfwu%7C0%7C1994; _clsk=1ti4td2%7C1750167180417%7C1%7C1%7Cl.clarity.ms%2Fcollect; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; XSRF-TOKEN=eyJpdiI6IjB0YUJxeXAwUDJaSHRIdTNVWGFhTFE9PSIsInZhbHVlIjoiWjJWdzNKeVlpZTRrVjFHSU1zV0hycUtRV2lnSDRONS81Zmw5UTc5ZS9FSnphK01vZHIrMm1IQ0F6Y1cwMkdqM05VL1N0TTJKU3ZEbC85d0dYQnl3cDZpSjBVNnpTT2hsWWs1RzJhN0FIRjZpcFp4OHNuSDYzUUp6TWY5WkVqZjVlSXRjNUxsbXNVNVQ2K1NNbjhxZ3RCaHpBdytzcmcvMW9vdUw3NVZ0dlYzRVdXZEdDKzlIb01jYUh6cVpLNzl6YisxSE9GZW9KdEoyb0h3YXhrU0V4ekhOWENFSE5OU1pjbENFTEEycG1LQWxIejJ6a1IrOTFrWGdHOUp6TUlQb1pnNjduNmhXbEJzSTdOdVZ1OGtiNmFRV3U3K0RtbmhGTHFta2JWNzJST0RYRmFSYVJ1b0hId2dKakFYdkRkZW92aXF5R05xU3NMK1FiN2RhNmZoeGc4NEl1UGpOaWNJaTc3dFN0d2RLSEZ6WXRFbXpGRjdZQlc1REo3eC9WYm9yZnVSczEwamhDTzQ3b1hscmkrMDdCMXFOczlTbUwyMzFLd0hBemdjTVcxUk5kS3l2Ynh3MVlndU05eERYa212dGpOR095b2RRdndhV0R3U0ZRRlliYzkyTDY3Nmg1bjVWM0tSaFBUK29BODluTEhZZ0hpZkFhUFVEZ1VwQkQ2WEYiLCJtYWMiOiJhMmZjMWUxMTE0N2UzZDZkMDcxNjYzYjU0ZmZkNGFkYjMxZWFjMjkyNDdkZTg3MzJkMjQ5MWUzNWE3ZjRhNTdjIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6IjNWcmI5QlRtSWszaXUyaHRnUHprb3c9PSIsInZhbHVlIjoiMUtEQWVuck9xcEllaTdTWnNuRWJneG9xTW9SWWFwTTBmbDVlWGh4RlJvQ05TSlBmZTlWRnBxZTFSeC9lODBwS0pwWUxtTEhhS2ZNSkVEa2gyUlNMUEVrZnZ4cWpBZ2x2SWF6TVZOdldySzcxbmlBWGROK0dpSUtVZnBKVnY1MzYzOCtXMEQrUm1FUWNLNjREL1RHKy8rQmxCTTRONUxoajM2NkZYZ3ZCYjcwMmlacjd2aThYcTR1MGxhWjZ3Mi9KdUxKUFEveEhXSG5jYzJ1dWpkdXZqMEhrY0tranB0Q0pEdlRwdWJUTENaQXZGZWZyVVFlWE5BTGNGaEN6cFI1R004Sk94QTJMY3luVkIzVjBWUDNiZTVzcC9wRXNUWmw5ZERtSHJkUjI5d0NFMWUxK3RqeEtQUE03QzNMVkZiaUVsQjI0cnBpQzdLd0dEZ3RZYnhicmgxU09wNUJEZ1prSkdMNkRqazJMOHJiMVZOcG9rcDdnNXJtQ1VITFYvZU9kcU1lQ05HQkRkNjlNZUtVNG1hR2QzbHRCaHJXNk1oSTc2bEE2S2xYZXhtR29pVlZZd09LdjNocnFzbmUvT0d6ak9hT281ZEZGVmljaXgxRUxubG81a2k5VS9GQ1hzWFFTMmJlSSt0WEFFem9pUnRoUWI4RlUwSWlkejVMYktEZmgiLCJtYWMiOiJjOTk4MDBiNjFjYjRmMDY3MTNkNWRhMjdkOGIwZDY0YjYxNmM4MmNlOTJjZWUzMjI1YTA0OGM4OGM2OGU1OTIzIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1320031844\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1068393816 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">VloaUQCBjpDKrel7nHeAzB1sTLdTMwteSos1MmAN</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1068393816\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1749239715 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 17 Jun 2025 13:33:36 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IlJJODN2SG1PQnBDOXl5SHdMdFluYlE9PSIsInZhbHVlIjoiRVVKd1pFZStkME5qdzRSUlRqcWFabVcyRkpLVlNBbkRzVWZEbTVjd01Tc21IZmJGK0tzNmlIQzNXY3hxWGFoQzVCVldsWmx1aGtQcVhOcW1hd2UvN2lsSGMrTjlXWmUrWWYwUWpPaW43bW40VnlqdkFQTlY5d255M1VVSTR0RmJxTmIxWXFTVGlzTnArbWpUcjNGWGhFbkhzNkNWdVRCb2w0bzNQK01GNm9yekJnbFlCL3pXaE5MWDl3blZhVkxlMFVtS1lnMjhzU2VEcUhBUmxUSGhlaTdJdk9wK1IzSllRVUd3VjZBRHo1c3NWdG04OU9OV1RtKzBHem9EbEhaVGdGT0RRN3M4Z3E1UGxTZ05LZGE1ZXZHUk9NMUVJNTNYdkhhTGw2QTNsbXdFTmlKa1FPc3hYM0hURlM3aFN2YTQwODZvMTBtaGZFbm1LUHZDZ1hUV213NzJiUWZtWnc3ZTFlMTZzWXJPeWM4WTVkZW9UU2NsaWJjVXNTR3ZDRUZ6T3VEYnBpdlJXTlhDZk1Xc2RtREFoUTlKRlpLbjQ4RDZLRHo1UXNCUDM5L1h1V0w4aG4zcXlFVkRpWjEzTWt6bTU5MVRqelBsNEhlczB2R0pHNGVpNW03bTY1eHVoSnpwYXRqNElJUEhvSFJBTjJnUWhrbXRyNktJZVFWRENRalMiLCJtYWMiOiI3YzE4NGVkZDM3N2ZmNzcwN2YyYTI1MjI1MGRkMzA4NzA3NzJmMWRmOTBlZDIwM2E2MGIxOTIzNDcxODUxM2I4IiwidGFnIjoiIn0%3D; expires=Tue, 17 Jun 2025 15:33:36 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6IktITUgzbW1sSWpEOGNJUzg0Mkh5Z0E9PSIsInZhbHVlIjoiT0MzV0VJY2xtU1dETWZmVzJLL2d3aU12c1BpZ3Z3clZHOHZDMUhsaFhmb2dQNGcxaVE3dUZIZ3YwQWUrdEFmRXQrUWFkTHZZcDdsQmlrWGNMNlBRZ3d3RFZZcEFNN1Q3TzVHWDlHM2EyRG9vMVB4dFowRVlrMEdNZWg3d1RMOGcvWWprcU85aDNjR1NJbS9oNjRFd2pXY0E1WUQ2RkMzWkdheGt0NHhDeHdOYmRRUTJtYUVXRVFHR3pIUXpyNFlnc3UvT2ZmSm44UFoyb1hrZU5KaHdSbUJUSEgzbWhrMWo1Z0tpSXlPVDZKN3lORjMwQWM2eWNZb2dDTDRkcFFtTjF0OUNXT1VFN2NkTXV2bkZlV1VDKzNid2N4NWkzd1RRM1ZsaFdKQlAvWm1DOW0zaks5NzU5Sm9WMEcxOWRzWWtvc2M0bHAybXVvNEMxanh4Z0dmZytnNWlHNDZQYUZMM3ZDcnZLZW5xamFxMzg4ckcrem00Zkg1UG5yTWlaRzlBZXA4MXZEQThGdHIyYVVnTlczRzJQcVUzTVJuQkdNT2xBdGtXc1EyR3ZaZkEzSjFKcGFFNDJVVlduOVNYL2o0ZkJuUjRFdXlrTW5jalhxQm5POXlGbXhMeXhIeXVGNGxvaGc3T05JS1VLcWQ3RHZabUlpWk5UV0tEYXVMZitxeHMiLCJtYWMiOiJkMTliMzI0YzhkMTI5MTBkYTFlYTgzNzE1MGYxNDg2NGEwZTJhNzViMmYyZDQwNzgwMzMyNTYxNjU1ODgyMTY2IiwidGFnIjoiIn0%3D; expires=Tue, 17 Jun 2025 15:33:36 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IlJJODN2SG1PQnBDOXl5SHdMdFluYlE9PSIsInZhbHVlIjoiRVVKd1pFZStkME5qdzRSUlRqcWFabVcyRkpLVlNBbkRzVWZEbTVjd01Tc21IZmJGK0tzNmlIQzNXY3hxWGFoQzVCVldsWmx1aGtQcVhOcW1hd2UvN2lsSGMrTjlXWmUrWWYwUWpPaW43bW40VnlqdkFQTlY5d255M1VVSTR0RmJxTmIxWXFTVGlzTnArbWpUcjNGWGhFbkhzNkNWdVRCb2w0bzNQK01GNm9yekJnbFlCL3pXaE5MWDl3blZhVkxlMFVtS1lnMjhzU2VEcUhBUmxUSGhlaTdJdk9wK1IzSllRVUd3VjZBRHo1c3NWdG04OU9OV1RtKzBHem9EbEhaVGdGT0RRN3M4Z3E1UGxTZ05LZGE1ZXZHUk9NMUVJNTNYdkhhTGw2QTNsbXdFTmlKa1FPc3hYM0hURlM3aFN2YTQwODZvMTBtaGZFbm1LUHZDZ1hUV213NzJiUWZtWnc3ZTFlMTZzWXJPeWM4WTVkZW9UU2NsaWJjVXNTR3ZDRUZ6T3VEYnBpdlJXTlhDZk1Xc2RtREFoUTlKRlpLbjQ4RDZLRHo1UXNCUDM5L1h1V0w4aG4zcXlFVkRpWjEzTWt6bTU5MVRqelBsNEhlczB2R0pHNGVpNW03bTY1eHVoSnpwYXRqNElJUEhvSFJBTjJnUWhrbXRyNktJZVFWRENRalMiLCJtYWMiOiI3YzE4NGVkZDM3N2ZmNzcwN2YyYTI1MjI1MGRkMzA4NzA3NzJmMWRmOTBlZDIwM2E2MGIxOTIzNDcxODUxM2I4IiwidGFnIjoiIn0%3D; expires=Tue, 17-Jun-2025 15:33:36 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6IktITUgzbW1sSWpEOGNJUzg0Mkh5Z0E9PSIsInZhbHVlIjoiT0MzV0VJY2xtU1dETWZmVzJLL2d3aU12c1BpZ3Z3clZHOHZDMUhsaFhmb2dQNGcxaVE3dUZIZ3YwQWUrdEFmRXQrUWFkTHZZcDdsQmlrWGNMNlBRZ3d3RFZZcEFNN1Q3TzVHWDlHM2EyRG9vMVB4dFowRVlrMEdNZWg3d1RMOGcvWWprcU85aDNjR1NJbS9oNjRFd2pXY0E1WUQ2RkMzWkdheGt0NHhDeHdOYmRRUTJtYUVXRVFHR3pIUXpyNFlnc3UvT2ZmSm44UFoyb1hrZU5KaHdSbUJUSEgzbWhrMWo1Z0tpSXlPVDZKN3lORjMwQWM2eWNZb2dDTDRkcFFtTjF0OUNXT1VFN2NkTXV2bkZlV1VDKzNid2N4NWkzd1RRM1ZsaFdKQlAvWm1DOW0zaks5NzU5Sm9WMEcxOWRzWWtvc2M0bHAybXVvNEMxanh4Z0dmZytnNWlHNDZQYUZMM3ZDcnZLZW5xamFxMzg4ckcrem00Zkg1UG5yTWlaRzlBZXA4MXZEQThGdHIyYVVnTlczRzJQcVUzTVJuQkdNT2xBdGtXc1EyR3ZaZkEzSjFKcGFFNDJVVlduOVNYL2o0ZkJuUjRFdXlrTW5jalhxQm5POXlGbXhMeXhIeXVGNGxvaGc3T05JS1VLcWQ3RHZabUlpWk5UV0tEYXVMZitxeHMiLCJtYWMiOiJkMTliMzI0YzhkMTI5MTBkYTFlYTgzNzE1MGYxNDg2NGEwZTJhNzViMmYyZDQwNzgwMzMyNTYxNjU1ODgyMTY2IiwidGFnIjoiIn0%3D; expires=Tue, 17-Jun-2025 15:33:36 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1749239715\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-2036387393 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>16</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"30 characters\">http://localhost/hrm-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2036387393\", {\"maxDepth\":0})</script>\n"}}