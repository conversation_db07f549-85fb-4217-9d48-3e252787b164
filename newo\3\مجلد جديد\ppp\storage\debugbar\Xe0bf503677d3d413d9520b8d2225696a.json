{"__meta": {"id": "Xe0bf503677d3d413d9520b8d2225696a", "datetime": "2025-06-17 13:48:51", "utime": **********.230508, "method": "GET", "uri": "/customer/check/delivery?customer_id=10", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1750168130.66694, "end": **********.23053, "duration": 0.5635900497436523, "duration_str": "564ms", "measures": [{"label": "Booting", "start": 1750168130.66694, "relative_start": 0, "end": **********.158836, "relative_end": **********.158836, "duration": 0.4918959140777588, "duration_str": "492ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.158848, "relative_start": 0.49190807342529297, "end": **********.230533, "relative_end": 2.86102294921875e-06, "duration": 0.0716848373413086, "duration_str": "71.68ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 44977392, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET customer/check/delivery", "middleware": "web, verified", "controller": "App\\Http\\Controllers\\CustomerController@checkDelivery", "namespace": null, "prefix": "", "where": [], "as": "customer.check.delivery", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FHttp%2FControllers%2FCustomerController.php&line=365\" onclick=\"\">app/Http/Controllers/CustomerController.php:365-378</a>"}, "queries": {"nb_statements": 2, "nb_failed_statements": 0, "accumulated_duration": 0.01508, "accumulated_duration_str": "15.08ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 16 limit 1", "type": "query", "params": [], "bindings": ["16"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/AuthManager.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\AuthManager.php", "line": 57}, {"index": 23, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 33}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.20004, "duration": 0.01419, "duration_str": "14.19ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 94.098}, {"sql": "select * from `customers` where `customers`.`id` = '10' limit 1", "type": "query", "params": [], "bindings": ["10"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/CustomerController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Http\\Controllers\\CustomerController.php", "line": 368}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.21939, "duration": 0.0008900000000000001, "duration_str": "890μs", "memory": 0, "memory_str": null, "filename": "CustomerController.php:368", "source": "app/Http/Controllers/CustomerController.php:368", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FHttp%2FControllers%2FCustomerController.php&line=368", "ajax": false, "filename": "CustomerController.php", "line": "368"}, "connection": "kdmkjkqknb", "start_percent": 94.098, "width_percent": 5.902}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\Customer": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FModels%2FCustomer.php&line=1", "ajax": false, "filename": "Customer.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "16", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]", "pos": "array:1 [\n  2044 => array:9 [\n    \"name\" => \"تجربية\"\n    \"quantity\" => 1\n    \"price\" => \"10.00\"\n    \"id\" => \"2044\"\n    \"tax\" => 0\n    \"subtotal\" => 10.0\n    \"originalquantity\" => 1\n    \"product_tax\" => \"-\"\n    \"product_tax_id\" => 0\n  ]\n]"}, "request": {"path_info": "/customer/check/delivery", "status_code": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-1763799624 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>customer_id</span>\" => \"<span class=sf-dump-str title=\"2 characters\">10</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1763799624\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-2005756225 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1936 characters\">_clck=2gyfy%7C2%7Cfwu%7C0%7C1994; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clsk=1ti4td2%7C1750167218299%7C2%7C1%7Cl.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IkVNdHYzaWFrelpKZk01b1JTNlVnbVE9PSIsInZhbHVlIjoiV0Z3LzlQN29qbWpTazZsdU50Q3lCUFlpZjFSV2ZiZW13N0lPNHIxd3pPYkZxZGx2ZjdpWjVKcWExVmFpcXNLT2hMVTB0bXRGQjVCSVlpbFhmWDcrM3lDaEZINGExMi9lamtvT1JoTndLbGd4d1lyOHJvZmQzREpHTzlFbUtkWWdqeWNrSm0rZmVoU2tieG9WM2o4VFRrTmE1b0RtN3NwS0dPUTJOVk9veFJBWUhMd21lSVczSnFXVURxOEczV1IzU2lVNVJJd1JGZzRhRmlySGhKaVNSYW5QaE90Y3FVUXNFMFZYQWdLSW9xcmxZRUpzWEhBYVZoUUEzLzdBaEhqcXJZQXNOclgyQ1ZqMVduSTdpeWQ4a3pYSm9OYzkxWGdmclhFc2UrdWlXSEZMTlRORjJudkRDbUZKd29mdFhRNzcydFQzbVVaWXFNdUxnbW9jbDlCWVY1ck1zeXk1aFIwZytoVjV3M0t4aXUzaURJajVHRUlFNFhNMkRhQ0dtU0QzZEJCU0Q2bU82WkVxOE80UW1MUldKSG8wbVJZMGljbXJVVC9NMVNydzR3QkgweDdVVXFRL2tCNitjdDEzaHp0dEZ1MUtFRUVUSVRNYzJ2TmRkdENmSFdrL2Fmd2lEc3dGVThibUdQNmNlRFdXRnBqdzBmRDJhM0g2b0xlQTI1SWciLCJtYWMiOiIyNDYzNzc0OWY1MDRiM2QzNmQ0ZjMwYmFkYjI0MmY0YjlmMjE0ZTY1YTE0NDgwN2RlOTNiZDVkOGNlMjJhMjg0IiwidGFnIjoiIn0%3D; test_session=eyJpdiI6IjNEWU1WT1BwRG81QUU2blBNaUhLY0E9PSIsInZhbHVlIjoieUFWdGRrNUFoamp4L0FVYlFmektvVDk2QmZsU0IrRTFoUUUzSEpIK2t1dDN4bXFwa3h6NjRXNXFJRi8waE5tczFXL1BzeVBiTnUzZkVJeXViMzI3QkZFdTJZZUgrTHlHU1B2VHlNcGd6V0JPUHhSR3BOOXA4VTF1M0lsWjIzSUdqdzhTWFdUdkJIQ0UzZ2tGMVgrY3Bpelo1NGpBYVFCeHBPK2VZamF4YS9KemFuOHRZRnA1b3NQM093MnpMMm1zTDR1NEhxMm9VaW9ST3R4VUN4SjMxUFhZeVZ3WVEwNTEvdUxObzB1VXdvU1VLeVpKc3AvNTV6QW1NNnhVY2F0UHJOOTNuVnJ0V3dHeFp6bGU4WWZXU3d0bGFQbzFyVkRlZXArV0o0WFowRjAyYVVDZERUemxaZ0FKTFpremIwemZHaE5yV1doVHovOGNmNmk4QkFjTXBNZG5zZmtZRm1JWnhmODdDdEFvbDA3NVJubUFiMDNiOEdDVU1lemNNcmluTjRRZlRYeFh3UXVuaU55R1UvSndyUGhrVEM0Rmk0NUs4Y0d1L1BUOUppRkdFSVdNbHlxWi9xdmpIOU00c3BtMDg3ajJXT2Q3OWk4OTNyVDNrS2FaU1JrTlNtVDZBWThlc0YrcENJZlBJb0Y2YlVFUVljd0x2eFdPSWdIZnBJYlAiLCJtYWMiOiI1NmQxNjk2Nzk5ZTM3ZjNjMmYyOTc3N2Y5OTI0MTQ5ZGJkZTQxOWQwOTUwYjBjMjMyOWQ4OTIyOGJlMzEzOTNjIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2005756225\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1246598260 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">VloaUQCBjpDKrel7nHeAzB1sTLdTMwteSos1MmAN</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1246598260\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1473914403 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 17 Jun 2025 13:48:51 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IkFuTkc0ZnlLRzlLNFpZKzF3b0ljUnc9PSIsInZhbHVlIjoiMU0wQmFiMXE2R0UraHhWZVFtOFJaQkdJQ09VTXJBQm1GQlRDMFdmR0gwYkhFb2ZMSnZucXg1ZUxjUkpIT1k3Q3hCYUN4WG1uVnNESkNHU3Vicko3bEMxQkpJWU5CSFFlL2dWN3c4TGRWemlUMjF3ZTFlL1kyNEY4c1ZydnVMbG1oVG1PaVpKRXhEbmVhamtmamxQQ2ZtMXdZTng0Mld5T0psMTZ5UlRhcGphcmtEZUxuQ0VOdnBQM1FEc3ZTTTJNdU8yUGpDSGtZbUVOS2VZSytoU3ByTFYxbEJObGY3SWg0T2FlSnI0MHhIc210L0s1M3pmVm4zTTh3Y0ZDTVdlOTdxeHFOMmQzaXpjN2lsazF0TVlKYy9vTEd5SG5BanpHWkdrTnFKRmloMFBRZXJPVUZTZHlja3pyWnd0ZHpYNW5MQ1o5dW0rbUNMY1E4M1ZwQlpxSFk4a0tPZ0crSlpoNng5V3p1SlN2WVc5Q0F5Q2UvczgrWlpOdVNOT1BQbnh1R0VuR1dZL2NjNmV3NGQybHh3K3hyTUc4ZzdNemsrYjVDWUUzNENYS1hyS3VlOHY5V2dGMkwzZWRsK2NyL2M1WUhDa3R1dFYzSkMxS29qbzNNS2htdlFvZW5PeEZnNWNSN0hiaWVkcGllZ0hFYnJKVk9ubjAvbkc4UGs4UkwxSGMiLCJtYWMiOiI5MGI1ZTdmNzQyN2I2YWU1ODMwN2ExOTgzYTM3YTA0MjVhM2Q3ZTRiNzQzMGE5NjI1ODU5NTRiMmYxMWE5ZTBmIiwidGFnIjoiIn0%3D; expires=Tue, 17 Jun 2025 15:48:51 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6InRtbnltWUgwK0lWTjJTd2d6V1pCYVE9PSIsInZhbHVlIjoiREtvT0k0eWNUby9OZkFqS3Z1c0VFTUF5aWRMbWgzLzk4bWd5bU9XeVJaQVNzYWtNcWJYOUprV1lUakpnUHg3R2NtR2ZWUHZhYmFRSTJZeVo5UnNjTlNCS2tIMTZibnN4VFlhcHBKVWE3TUdaaXdTckNIZ2JlbXJpVlNaRXJLYnlUNnpBQnpFczFtSUdnQjUyS0VKT2JRRFV3cWVlSitXQk9PY2NST1JLRUZFcU4yTFl0dnNCQ2ZoMElHRXd2RVNZdU1RMHEycUVnYWlJM3N0REd6cjRuTnhkUjNxYzEzTjFtOENaZHk5ZEk5VEEvN2M4YWtHMnhueHB6MHhCam1aUGszTWZVNmtZdDY2YnJqS044OFRneVJDNG9tUFZnZGJkSSt0enNvaVpLSVdqRHA3SWpvaGRlOHB3Zk1NdUhGSUY5TGdmTk9lTm52RnJvZlpPNzJhMWkxOXNIRXBDWEtmUzRweUtaSHAxZUVWakxFQlQvajRSWDRrM1lRdG5MVUl0ajh6dEREaW5GUGw3UWQ1by85WnBFb0E3T2Z1aVNycnpUdmFqQkZabFFVUjVYaTVLV3p4S2xHU2Z5RktnR2toYlE5c281Ykk3OWtUampIL29MdFhqMFRpa2QrMGhqbjRmczZJU2crdktnWFFwVi9OYnZkeFpkU21xcUNCVXhhNGoiLCJtYWMiOiIwZGRiZDcxZGRkZWRhYWNjY2YyMDZhNjcwM2E1MTcwYTAzN2E0ODJiM2JhMGQ3NTE0ZTkyNTdkZWI3MmJjMjljIiwidGFnIjoiIn0%3D; expires=Tue, 17 Jun 2025 15:48:51 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IkFuTkc0ZnlLRzlLNFpZKzF3b0ljUnc9PSIsInZhbHVlIjoiMU0wQmFiMXE2R0UraHhWZVFtOFJaQkdJQ09VTXJBQm1GQlRDMFdmR0gwYkhFb2ZMSnZucXg1ZUxjUkpIT1k3Q3hCYUN4WG1uVnNESkNHU3Vicko3bEMxQkpJWU5CSFFlL2dWN3c4TGRWemlUMjF3ZTFlL1kyNEY4c1ZydnVMbG1oVG1PaVpKRXhEbmVhamtmamxQQ2ZtMXdZTng0Mld5T0psMTZ5UlRhcGphcmtEZUxuQ0VOdnBQM1FEc3ZTTTJNdU8yUGpDSGtZbUVOS2VZSytoU3ByTFYxbEJObGY3SWg0T2FlSnI0MHhIc210L0s1M3pmVm4zTTh3Y0ZDTVdlOTdxeHFOMmQzaXpjN2lsazF0TVlKYy9vTEd5SG5BanpHWkdrTnFKRmloMFBRZXJPVUZTZHlja3pyWnd0ZHpYNW5MQ1o5dW0rbUNMY1E4M1ZwQlpxSFk4a0tPZ0crSlpoNng5V3p1SlN2WVc5Q0F5Q2UvczgrWlpOdVNOT1BQbnh1R0VuR1dZL2NjNmV3NGQybHh3K3hyTUc4ZzdNemsrYjVDWUUzNENYS1hyS3VlOHY5V2dGMkwzZWRsK2NyL2M1WUhDa3R1dFYzSkMxS29qbzNNS2htdlFvZW5PeEZnNWNSN0hiaWVkcGllZ0hFYnJKVk9ubjAvbkc4UGs4UkwxSGMiLCJtYWMiOiI5MGI1ZTdmNzQyN2I2YWU1ODMwN2ExOTgzYTM3YTA0MjVhM2Q3ZTRiNzQzMGE5NjI1ODU5NTRiMmYxMWE5ZTBmIiwidGFnIjoiIn0%3D; expires=Tue, 17-Jun-2025 15:48:51 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6InRtbnltWUgwK0lWTjJTd2d6V1pCYVE9PSIsInZhbHVlIjoiREtvT0k0eWNUby9OZkFqS3Z1c0VFTUF5aWRMbWgzLzk4bWd5bU9XeVJaQVNzYWtNcWJYOUprV1lUakpnUHg3R2NtR2ZWUHZhYmFRSTJZeVo5UnNjTlNCS2tIMTZibnN4VFlhcHBKVWE3TUdaaXdTckNIZ2JlbXJpVlNaRXJLYnlUNnpBQnpFczFtSUdnQjUyS0VKT2JRRFV3cWVlSitXQk9PY2NST1JLRUZFcU4yTFl0dnNCQ2ZoMElHRXd2RVNZdU1RMHEycUVnYWlJM3N0REd6cjRuTnhkUjNxYzEzTjFtOENaZHk5ZEk5VEEvN2M4YWtHMnhueHB6MHhCam1aUGszTWZVNmtZdDY2YnJqS044OFRneVJDNG9tUFZnZGJkSSt0enNvaVpLSVdqRHA3SWpvaGRlOHB3Zk1NdUhGSUY5TGdmTk9lTm52RnJvZlpPNzJhMWkxOXNIRXBDWEtmUzRweUtaSHAxZUVWakxFQlQvajRSWDRrM1lRdG5MVUl0ajh6dEREaW5GUGw3UWQ1by85WnBFb0E3T2Z1aVNycnpUdmFqQkZabFFVUjVYaTVLV3p4S2xHU2Z5RktnR2toYlE5c281Ykk3OWtUampIL29MdFhqMFRpa2QrMGhqbjRmczZJU2crdktnWFFwVi9OYnZkeFpkU21xcUNCVXhhNGoiLCJtYWMiOiIwZGRiZDcxZGRkZWRhYWNjY2YyMDZhNjcwM2E1MTcwYTAzN2E0ODJiM2JhMGQ3NTE0ZTkyNTdkZWI3MmJjMjljIiwidGFnIjoiIn0%3D; expires=Tue, 17-Jun-2025 15:48:51 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1473914403\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1372251672 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>16</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pos</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-key>2044</span> => <span class=sf-dump-note>array:9</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"6 characters\">&#1578;&#1580;&#1585;&#1576;&#1610;&#1577;</span>\"\n      \"<span class=sf-dump-key>quantity</span>\" => <span class=sf-dump-num>1</span>\n      \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"5 characters\">10.00</span>\"\n      \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"4 characters\">2044</span>\"\n      \"<span class=sf-dump-key>tax</span>\" => <span class=sf-dump-num>0</span>\n      \"<span class=sf-dump-key>subtotal</span>\" => <span class=sf-dump-num>10.0</span>\n      \"<span class=sf-dump-key>originalquantity</span>\" => <span class=sf-dump-num>1</span>\n      \"<span class=sf-dump-key>product_tax</span>\" => \"<span class=sf-dump-str>-</span>\"\n      \"<span class=sf-dump-key>product_tax_id</span>\" => <span class=sf-dump-num>0</span>\n    </samp>]\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1372251672\", {\"maxDepth\":0})</script>\n"}}