{"__meta": {"id": "Xe1241352ed9064b03a365984d86d3d4e", "datetime": "2025-06-17 13:30:36", "utime": **********.767644, "method": "POST", "uri": "/warehouse-empty-cart", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1750167035.935034, "end": **********.767668, "duration": 0.8326339721679688, "duration_str": "833ms", "measures": [{"label": "Booting", "start": 1750167035.935034, "relative_start": 0, "end": **********.660699, "relative_end": **********.660699, "duration": 0.7256648540496826, "duration_str": "726ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.660718, "relative_start": 0.7256839275360107, "end": **********.767671, "relative_end": 3.0994415283203125e-06, "duration": 0.10695314407348633, "duration_str": "107ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 46327584, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST warehouse-empty-cart", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\ProductServiceController@warehouseemptyCart", "namespace": null, "prefix": "", "where": [], "as": "warehouse-empty-cart", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FHttp%2FControllers%2FProductServiceController.php&line=1663\" onclick=\"\">app/Http/Controllers/ProductServiceController.php:1663-1673</a>"}, "queries": {"nb_statements": 2, "nb_failed_statements": 0, "accumulated_duration": 0.0156, "accumulated_duration_str": "15.6ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 16 limit 1", "type": "query", "params": [], "bindings": ["16"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.719893, "duration": 0.01456, "duration_str": "14.56ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 93.333}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.750416, "duration": 0.0010400000000000001, "duration_str": "1.04ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 93.333, "width_percent": 6.667}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "ixL6BN3l9tyyUscqj0hv99yKIy0daqhD43fAAWcL", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "16"}, "request": {"path_info": "/warehouse-empty-cart", "status_code": "<pre class=sf-dump id=sf-dump-1973159487 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1973159487\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-469079645 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-469079645\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1941372911 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>session_key</span>\" => \"<span class=sf-dump-str title=\"3 characters\">pos</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1941372911\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-920671611 data-indent-pad=\"  \"><span class=sf-dump-note>array:19</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">15</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">ixL6BN3l9tyyUscqj0hv99yKIy0daqhD43fAAWcL</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1937 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=1pubyvl%7C2%7Cfwu%7C0%7C1993; _clsk=25xk9k%7C1750165707106%7C8%7C1%7Cl.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IjEwOEJraWhzVjJBOEttOHhPUjc0Ymc9PSIsInZhbHVlIjoiYlZVbFoyZE9LdkYybk1GWUtpWGhzSUxrUzJ5Zm40QzRXZlNUN2loeW1VRlpMVVZmUEhBdGwzR3hUaGgxSnZpS2VkUHh2aGpIMXNUN0orVWdGZ1VzeTg2dlA2dkoxSFladlAxTHVpVHRzSEVGYkFHVkNhQWF6TnA0NFU5STlUNjlKd1djRXc5T0ZrMFFPQzgxYlJVQmFlUmlzMCtuTHlaeFZCTk1mRERDNnVoMDJJL1BvQUNUWWRkWmk0ckRHbHQrZnBQb3VDQ0p0QWlFMHZtTzhUZnczaC9HZ3krdGdvTVZMVzFyYzNkMzRCOFlTem5xcEtnQ0tXdHNSL3lSbUorTkd2NHhwM1V1WEk3aFhCT1JGNlo2YVFaS040OFBjc2N1QmIveVlFSFpjY0duVWRQcmZ6MU4vOGw5TDBaYkFOKzVBUFRtMXpkaG1ZTkFjSExrWTQreDNZc0dSSEE1YmZYUjZBWE02RnRJbmpVMSt6a1p6TE95dkdPWnNIcWhMRjRXUmJxa1BNQzFwb2dLem8zRjZodmtaeXJkMmtVWWxsMlBLMGpUemFBYnF3TFUwTlA1aHEzMUdwNWtWdldNUHlCT0lDYXFpbk45ZXZQcUJ6cUxrMmlkZDF4SDdaS1lvNWcvQUZiUGh5S2lTbmx6N2hhZzc3MWs0cFdYeUJCQmNZUGoiLCJtYWMiOiIzM2M0NzVhMTQ2OGJkNDE2MzVmNWJjOTFlMDNmODYwODk0NmM2MzVkMGUzMTQwY2IxNGEyNzY5MTEzODEyNDY4IiwidGFnIjoiIn0%3D; test_session=eyJpdiI6ImNNWDBXOXhJY1h1bkFoaWlPdlJtM3c9PSIsInZhbHVlIjoiaTVEQnpXNjJEb1UzeFFCVTZNYmFPWUtvN05BcXBxVW0rU3hBbjhBY2t5d2FKT0p5VTFrREMzaUZDejRlb2pxL1lTdk1aN2sybERxR3ZqVEo4ZExHNCt0VE8rOXRLQlU0bFdJcEEwZHpsM1ZscGFFdHNRUWl0eTNmLzRGSmp3MGluZXB0dXlTSWxIOUpaNndhVE01S2g3ZldUZWw3L1VRbk1QZW9GazdVUUxPaVVtUC91WXVqdUNEZndRSEN5Z3ZkOGo4Kzcvem02cWhIQnZiTGVEaTBpbk0xV09tc2RYM1BuVTRrRHpvZkcveGR4YTVoMGV5TVQ4czZsNE9oRC9rZTdJMGllUjJSMmxaYmw2VE1meHBmTkhvY0RwWVFxZHppRlhodlJHeFAvb1pjVHBraUdBeXVkRUxKNXMwUktqeTQ2QnMvOHpPSjUwR0N3S1dwdVZ4WEFaUU05S1gyTnloZTU3MXI5TlZOTzBMaVg0c0VOTlExZHhGZG12YTk2ZkVINjYvM245eWVEcGFRSG1QWHR1YlFOVTFKdlhCeWszQmt3UnBXMUJIaStQaE5rbXVGcmNaelFqNm83V2QzMVI5K2lUa0VRUG41V2lUbk1zQlNuWllNQTM1RlRYUUw1b1lXb1MvUkRPcHRZL2ttZzI0VmEraytCV1FXOFNkamxRRGciLCJtYWMiOiI3ZWIxNTI1YzY3YjViMDljZTZmZjI0MzI2NjdkNWM2NDRhNmQyMjJiZGY5ZGI2NmViN2Y0YWEzN2ZjN2MzMzJmIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-920671611\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-814221079 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">ixL6BN3l9tyyUscqj0hv99yKIy0daqhD43fAAWcL</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">coJ97Z8YyNCeFgF4h0jUL1PrpYBjauguIcJbJbm8</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-814221079\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1664789638 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 17 Jun 2025 13:30:36 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IkppNlhvR2JpYmNGa3oxcmV6L3FnSEE9PSIsInZhbHVlIjoiNWF5Z1BYdi9kWStyNXBQdDA4Z0FRZUpVdnFDU25WOGdhZmJyRVRKZ2hDam9aQWhobXFQYTcyK01sM0xJS0ZJZ1hiM3VwK08zUzRwVDcrNjhzcGxkbXZpWGNLOEdFWUUyMEYxRllyZmdqUDRVemRLYm85c1RJWE5iY3FCQ0JFSWdDazJzSHdobkFEU05HdjdWckFXYk01QThlM2s1bytRTnFqczlaSTVRSjk4dE1LNE9DWlgvRDFRT0REVEtSbGkrV0pLd3ZFU0JuWUk4NkttSXFlWGltZjE2NEhlUTduZE43QXBFZE5udHBYZEJPUUhmMm9mQ09jTVJNdkt5S3RYU21jbmFoNmw3MUJlR1F3ODhhQTZ5V09GNFZ2ZlA5SEV5anJzblh2ZHBaNUZWNHFRd3pvZmpYS1RUWWJQeFhsMGhvWWkvMXIwU1ZRczE1N3VYK01odHFTQzFpQlJkNkZmV2ZsK1Q5UHFCYko2czhUYzlRR2xkYU56QktMZE10WXI3UXBaQ0ZYeHVzbkhTWjExYXJXU3YxamZLR0gwMjU3VUltdTBSamp0MDloUHVtc2RNRVN5UithaUZkYVladGtPbVlrRktEOUFJc2IrRyt1dExiUDBldDI5MHh3bW5wdnY3a1hQUnUxUmQ4UTk1MllkR29qcDZlVXN5anR1eER4a0kiLCJtYWMiOiJhMjdiMDEzZWU5NmM2MzUyOTk2YzJlMzQzMTE4NjhmZWYyYjM4ZGU1MTI3NjQ3MmQ3ZTlkOGMyM2E0YjI1NmNlIiwidGFnIjoiIn0%3D; expires=Tue, 17 Jun 2025 15:30:36 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6Im0zaDBmZktlaGZVc0Q3VitOR0g4ZFE9PSIsInZhbHVlIjoiNVk2WDJoUmFuZEhJY0tMYWVzTmlqRi8xM3VTWFMrQ2NNbVdOamtsa0JOc0ZhUnlLRmd6RTdoTkZQdHFGODV0WkFBbGtjV3VGOG9USytCaFRWRmNSdmk5ZEJNRmhEdnpDNGllOXhZd1Z3R2VpQ3ZURVI2b0ZUa1ArU0ovb1Y1MFZFaHU4UTU2UlVjSHJ2L1RTY2c0eUZRdWJsdEtad2l3aTVoWnl6QmRXOU9FNmh2ZEI3S0p1NGlFUzZyYVpuYW9XNWljbVF3M2VpaW5JWmpYbmJiT2pURS9KQmlhRVR5SnkzVXpYclJNeWx6alpNbTVTTWl6QmxZRmxhemU3MTZ2S3lvMm5LakxpZWx4K0dSTmp3S0tLeWwzeitaVy9ZUzRvOXFEakgyT2U4THpEa1hFVWVlYVpZVDlmK2RPSTd1eFUwUzlSUUVuY0FMcVR2cFE4Z3lHTnZ1REhDdjkrRllYZDBHaWUzOEdmZ21rQzFVL2JpOFdpMWZUSDZBYVlkYnpWTDVXUElYZlNYK1FMaUcwQU5wNzNic3huRWxiYXFMZ0ZNa01ZNERVQ2sxQzZhbFJNc1E2eXNlQUxSRExFMG1aQzdueDQxdjRhejdtWDBvK0prWmNOWGRTaDR3WDRxL0RLQWVsKzdWQVdaN093Qy9yQ0JFejZWQ0lCbXhFc0hqV3UiLCJtYWMiOiJlOTM1Y2U5NGYxYmFlYjc4YzllMjlhMDI4MjFjMGEzMDVhNmVlODE5YmIzODRlZTU5ODAwYTZkMzQ1ZmYzMDM2IiwidGFnIjoiIn0%3D; expires=Tue, 17 Jun 2025 15:30:36 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IkppNlhvR2JpYmNGa3oxcmV6L3FnSEE9PSIsInZhbHVlIjoiNWF5Z1BYdi9kWStyNXBQdDA4Z0FRZUpVdnFDU25WOGdhZmJyRVRKZ2hDam9aQWhobXFQYTcyK01sM0xJS0ZJZ1hiM3VwK08zUzRwVDcrNjhzcGxkbXZpWGNLOEdFWUUyMEYxRllyZmdqUDRVemRLYm85c1RJWE5iY3FCQ0JFSWdDazJzSHdobkFEU05HdjdWckFXYk01QThlM2s1bytRTnFqczlaSTVRSjk4dE1LNE9DWlgvRDFRT0REVEtSbGkrV0pLd3ZFU0JuWUk4NkttSXFlWGltZjE2NEhlUTduZE43QXBFZE5udHBYZEJPUUhmMm9mQ09jTVJNdkt5S3RYU21jbmFoNmw3MUJlR1F3ODhhQTZ5V09GNFZ2ZlA5SEV5anJzblh2ZHBaNUZWNHFRd3pvZmpYS1RUWWJQeFhsMGhvWWkvMXIwU1ZRczE1N3VYK01odHFTQzFpQlJkNkZmV2ZsK1Q5UHFCYko2czhUYzlRR2xkYU56QktMZE10WXI3UXBaQ0ZYeHVzbkhTWjExYXJXU3YxamZLR0gwMjU3VUltdTBSamp0MDloUHVtc2RNRVN5UithaUZkYVladGtPbVlrRktEOUFJc2IrRyt1dExiUDBldDI5MHh3bW5wdnY3a1hQUnUxUmQ4UTk1MllkR29qcDZlVXN5anR1eER4a0kiLCJtYWMiOiJhMjdiMDEzZWU5NmM2MzUyOTk2YzJlMzQzMTE4NjhmZWYyYjM4ZGU1MTI3NjQ3MmQ3ZTlkOGMyM2E0YjI1NmNlIiwidGFnIjoiIn0%3D; expires=Tue, 17-Jun-2025 15:30:36 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6Im0zaDBmZktlaGZVc0Q3VitOR0g4ZFE9PSIsInZhbHVlIjoiNVk2WDJoUmFuZEhJY0tMYWVzTmlqRi8xM3VTWFMrQ2NNbVdOamtsa0JOc0ZhUnlLRmd6RTdoTkZQdHFGODV0WkFBbGtjV3VGOG9USytCaFRWRmNSdmk5ZEJNRmhEdnpDNGllOXhZd1Z3R2VpQ3ZURVI2b0ZUa1ArU0ovb1Y1MFZFaHU4UTU2UlVjSHJ2L1RTY2c0eUZRdWJsdEtad2l3aTVoWnl6QmRXOU9FNmh2ZEI3S0p1NGlFUzZyYVpuYW9XNWljbVF3M2VpaW5JWmpYbmJiT2pURS9KQmlhRVR5SnkzVXpYclJNeWx6alpNbTVTTWl6QmxZRmxhemU3MTZ2S3lvMm5LakxpZWx4K0dSTmp3S0tLeWwzeitaVy9ZUzRvOXFEakgyT2U4THpEa1hFVWVlYVpZVDlmK2RPSTd1eFUwUzlSUUVuY0FMcVR2cFE4Z3lHTnZ1REhDdjkrRllYZDBHaWUzOEdmZ21rQzFVL2JpOFdpMWZUSDZBYVlkYnpWTDVXUElYZlNYK1FMaUcwQU5wNzNic3huRWxiYXFMZ0ZNa01ZNERVQ2sxQzZhbFJNc1E2eXNlQUxSRExFMG1aQzdueDQxdjRhejdtWDBvK0prWmNOWGRTaDR3WDRxL0RLQWVsKzdWQVdaN093Qy9yQ0JFejZWQ0lCbXhFc0hqV3UiLCJtYWMiOiJlOTM1Y2U5NGYxYmFlYjc4YzllMjlhMDI4MjFjMGEzMDVhNmVlODE5YmIzODRlZTU5ODAwYTZkMzQ1ZmYzMDM2IiwidGFnIjoiIn0%3D; expires=Tue, 17-Jun-2025 15:30:36 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1664789638\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-2023935259 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">ixL6BN3l9tyyUscqj0hv99yKIy0daqhD43fAAWcL</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>16</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2023935259\", {\"maxDepth\":0})</script>\n"}}