{"__meta": {"id": "Xea25c72dedf63154bb0149862cd534c6", "datetime": "2025-06-17 13:53:10", "utime": **********.940235, "method": "POST", "uri": "/warehouse-empty-cart", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.32928, "end": **********.940263, "duration": 0.61098313331604, "duration_str": "611ms", "measures": [{"label": "Booting", "start": **********.32928, "relative_start": 0, "end": **********.833575, "relative_end": **********.833575, "duration": 0.5042951107025146, "duration_str": "504ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.833588, "relative_start": 0.5043079853057861, "end": **********.940267, "relative_end": 4.0531158447265625e-06, "duration": 0.10667920112609863, "duration_str": "107ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 46341248, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST warehouse-empty-cart", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\ProductServiceController@warehouseemptyCart", "namespace": null, "prefix": "", "where": [], "as": "warehouse-empty-cart", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FHttp%2FControllers%2FProductServiceController.php&line=1663\" onclick=\"\">app/Http/Controllers/ProductServiceController.php:1663-1673</a>"}, "queries": {"nb_statements": 2, "nb_failed_statements": 0, "accumulated_duration": 0.01874, "accumulated_duration_str": "18.74ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 16 limit 1", "type": "query", "params": [], "bindings": ["16"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.8820138, "duration": 0.01746, "duration_str": "17.46ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 93.17}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.922436, "duration": 0.00128, "duration_str": "1.28ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 93.17, "width_percent": 6.83}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "16", "_previous": "array:1 [\n  \"url\" => \"http://localhost/hrm-dashboard\"\n]"}, "request": {"path_info": "/warehouse-empty-cart", "status_code": "<pre class=sf-dump id=sf-dump-51459460 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-51459460\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-954244139 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-954244139\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1054427033 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>session_key</span>\" => \"<span class=sf-dump-str title=\"3 characters\">pos</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1054427033\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1243462073 data-indent-pad=\"  \"><span class=sf-dump-note>array:19</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">15</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1936 characters\">_clck=2gyfy%7C2%7Cfwu%7C0%7C1994; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clsk=1ti4td2%7C1750168386913%7C7%7C1%7Cl.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6ImoxeTVtcmtSTVlRdWs2UkhERnYzd3c9PSIsInZhbHVlIjoiQWcxQkdZYWN3WHRXd25tSjNodUxxSnRsM2RPK0xpcEt2dlFETURFdWk1dGNFOElZbkpNU3hNME5QUDBBakcwUm1vY0wzWHJNVWxoR0JLY0hPWFFtbmNiT2RXem9lbVdmSnA5Q2dLQy8rTnpEaHNRYndFYTBVam1hNFZrL3hZcEhvZnM1U3BvUHBhcVptYlJRTXZ4N3BOVDNRN1luYVA4bmx4RnZDdkdEUXlRK0RhM2tLVnIrbFdKYjBUUjRDdG00ZWtEODNDRE84a2VrMU9lQUFJSUtEeXdobzFCSjV6ZDhRZUVPUjUwRGo5eUxOVDBOTk1LUUI2dWVwUDhjRTlXLzJ5bmExY0ZwZVdlcW5Qem5tT0tJKzZVekd2MitBWjBDazd4b2xLRmF3akp6Um03ZUVHbzdyUytRS0kvd0V1Q0lXMkVCc1RIb1dKalpHNGV5UFVqSlAvZGZTTWVnZUt6VHg5RlNjdTlyMGh6VjI4bFBqZ1p6MTZkQzgxWlpVUTNWb1pPQ2U3NjR1V1lsaEg1cnExY3VpeGRsamFBKzZDcWFkMTBPcnRXMHRHVE1IOHdPaXBTRTcvWFNjVnZSRzFHRlN1a0VvWkdQZ1NUbkkyRnVtTlU5bXB3TnA4SHlGRE1MYXRZbkJhRUhKQU1aTmc4VnZrYVJGN1ExcnFjNm5maTYiLCJtYWMiOiJlYmEyOTAyNDE3M2FlMTFhN2RkZjMxNTY5ZjQxNmIxNGUwODViODEwNWI2OTRhOWE1MGQyY2MwNGI0OTk3OTk0IiwidGFnIjoiIn0%3D; test_session=eyJpdiI6InNZRHZYTHNtZ0wwN3VaL1NyZXg4MEE9PSIsInZhbHVlIjoic2J0MWpNd3FmcDFiWTNmd2VTZXBaaC84UHJSS1BRTDBsWmk1bjcxRVBGLzZPS0Q5eGhYYlBjU0ZIQlkwelUxNXIzOVNUZ0lrVkcwL1lyd0ZRRnRBQkQvczBWcnNpeThCRU1zdlZLUG1OYmlWZjl2aTgwM3h4b0tNSUZhdkN6d2NrbmkrcURKbVRpRUgvbFNmOEVCTkttaHdybXlDc2d0c0xCODFlR2Z6TVhxSHhnaHNJc1ZFQnl6dEJVaG44eHdaWklMNUFZenVZdnBMd1ptMGxFT0VHUWI2QmlHN0sxL09aVlJKQ28zTWtQMFcxeDlmZXFnV0Noak9ITUNQR1VJRzV0aXd5TXdlREx4MXJXUXlHSml0YU54SEFvT1R0TE9YQmU5aDZPZGE4TUNIYURUemRzem9lbU12S1NvMnlWSEdFaXdiMlRKeEhTOERBVWJ0cWlOdlR6MHIwVHBWTURuamsvWFd2SFFLK3U2VU5TVFFFa2hhTVNJNHJGenZpS09FWEc4NmxVODFQOUVBaEQxU216V0NuOXI0ZmluTEZVeDVNeVRnNTZDRDYrc0xqdnM1WVpwZno2UjJxZVRTSWV4T25FWU10ZXdxY1M3eEoySWRVYjJPNVhHNTkrRFVLVUxRa0ZDeENvcTRwRzVtd081WVF6NnF1Tm5yeklaNTNuQUYiLCJtYWMiOiI0YWRmYzNmMzk0MmJlNTkyZGU2YzQ1YzhlMTkyZGI5ZWM5MTZhOTlmOTQxY2QxNzE5MTA4OGRmNWRkNTQ0MTUzIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1243462073\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1624115261 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">VloaUQCBjpDKrel7nHeAzB1sTLdTMwteSos1MmAN</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1624115261\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-927989115 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 17 Jun 2025 13:53:10 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6InFyeWc1U2RNbkpiSnBsL1d4bGc1OXc9PSIsInZhbHVlIjoiankxTWs5VTZZbWd0bWxJdGpWU2xLa0dOZnRYOGZhUEV6SEw1b05ENklaVEdTdlB2anlvdEtzeXJEM2JsSlg3U3JGNnhTQUJWM2YrY0dMMVFSRlJkaHE2ZEl0eFJITkZNSUdkT3RyQTZKU094VE4yazdldmZvdVc5aGlCMTZvUnhIUlQvcW9Gdjc1dDRpT2JtZ0ZLYjJxL01PRnVwK0dKUjQ3R05Nd0RoczdqWVVnd2N2NDloSmRGQThHMkpLL1l5MkFFWmZiZmlWanVMdEptblcxVUpFUXpFd0FubVl2Q0t3aFdRWk1seEpyTm90RWRiRXBZaDdLbTg5b3VITWxDZkdIWTYrc3dteEdyanlzeXdJaE9yanJ3RWdnamV0aUZwTXVNTkZYejhiREthOXRqQ2szMmptTFlMTGp6eFgvUzRSaWh4TDJRaXJmYTVxQjdqcHh2SXNwSDI0b2hub3RXdGM3aUh2M0dXTU9ZbVNZY216VjZHVlFPMm95Z2ExTXN4NHB3eSszVHdZSXVvRzJ2Wjh2WVF0ZUZLVWlBSU1MdGRCU3l2Z0gyV2dpWSt5bVRMeW5OTzhXZVU5Y2RYYnpHMUJxK0JLRWVHT2JUYVFMU0tWSDZlQ0VKU3VKTG9NdFBKNlJCcWx5eXJuUDNFYlZRNlF5bTU4bFBjaFd4RExiMGciLCJtYWMiOiI3MzgxYTNmMmEzNGQwYTY3NGQyNDI5YzVlYTcwMWU0YjU5OTdiOThlNTk2OWMwNThmNmFkYzMzOTM4NWJjNTE0IiwidGFnIjoiIn0%3D; expires=Tue, 17 Jun 2025 15:53:10 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6IkhTK0ZGY09OeFYvQTZEV0lrTzVYY3c9PSIsInZhbHVlIjoidWhtRGFXbEJWcklqS1hqbUlBTTJScnRTRUZIMFFoS0h6MFN3M0xwangrMjEzU00wZDJMWEV6ZWwxWUdZTGMxMVZSRktCZDk1VjJSKzdvNG9DTVJYUWEzUFdyZVhiZm9mVzVmUWxMNE4vcFR2V2gxTHB1MHdFQllTRkJBRkpxRHpoK1FzWDBERGFIVXd5UXo3ZDZHKzVvUUdkbW1kK0ExdDR0b1lxRDVtL3dMS3JQRFNQdDlHMm10RytmZUpZWVFFRS8xUE1tOTBIN1gvSU1wVmNMcjNhSlBzRi9XcHNkM3IzZTB5Z09NVCs1MmlPR0JjcVpab2lIaWxNZHRlNnRZMENYc1BYNGtWZ2NGbW1LaUpDU20xYUVVMW5KK3hIbmFoUEhNYXVzZnR4UmM3NEdIZlhBQ0pjTXVJTzl0K00xcHVnR2J3TFhYZkNXMHB3dEI1QWUxOTIxUDUxZU5Ld29KTWRtbXdBb0ZiM2lSQTBnMldnTkxaUjhhbCtMdEY5TjNUK3lLV2t0UFN3MW1xWHBPb01WYWM3clVZSnJqV1dLTUVyNVp4NEF3R0dlYlhmOVNnRnhKSWZVOHVjV1QveWFnS0NkTVAwTWtkbFlNV3lXc1JLaTFEQnlzUTF0K0R4bC83aGMydUdQWEMrMERSa3VaMWMxelNrYm9OOC9WdjlWSE8iLCJtYWMiOiJiMmVkNzNhYWMzNGM5MzBjNGYxZjI3YzhkMDJmYzZlOTI4ZmU2MjA3NWU3YjA2ZDI4ZTkwZWI2OGYxZWZlYWJjIiwidGFnIjoiIn0%3D; expires=Tue, 17 Jun 2025 15:53:10 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6InFyeWc1U2RNbkpiSnBsL1d4bGc1OXc9PSIsInZhbHVlIjoiankxTWs5VTZZbWd0bWxJdGpWU2xLa0dOZnRYOGZhUEV6SEw1b05ENklaVEdTdlB2anlvdEtzeXJEM2JsSlg3U3JGNnhTQUJWM2YrY0dMMVFSRlJkaHE2ZEl0eFJITkZNSUdkT3RyQTZKU094VE4yazdldmZvdVc5aGlCMTZvUnhIUlQvcW9Gdjc1dDRpT2JtZ0ZLYjJxL01PRnVwK0dKUjQ3R05Nd0RoczdqWVVnd2N2NDloSmRGQThHMkpLL1l5MkFFWmZiZmlWanVMdEptblcxVUpFUXpFd0FubVl2Q0t3aFdRWk1seEpyTm90RWRiRXBZaDdLbTg5b3VITWxDZkdIWTYrc3dteEdyanlzeXdJaE9yanJ3RWdnamV0aUZwTXVNTkZYejhiREthOXRqQ2szMmptTFlMTGp6eFgvUzRSaWh4TDJRaXJmYTVxQjdqcHh2SXNwSDI0b2hub3RXdGM3aUh2M0dXTU9ZbVNZY216VjZHVlFPMm95Z2ExTXN4NHB3eSszVHdZSXVvRzJ2Wjh2WVF0ZUZLVWlBSU1MdGRCU3l2Z0gyV2dpWSt5bVRMeW5OTzhXZVU5Y2RYYnpHMUJxK0JLRWVHT2JUYVFMU0tWSDZlQ0VKU3VKTG9NdFBKNlJCcWx5eXJuUDNFYlZRNlF5bTU4bFBjaFd4RExiMGciLCJtYWMiOiI3MzgxYTNmMmEzNGQwYTY3NGQyNDI5YzVlYTcwMWU0YjU5OTdiOThlNTk2OWMwNThmNmFkYzMzOTM4NWJjNTE0IiwidGFnIjoiIn0%3D; expires=Tue, 17-Jun-2025 15:53:10 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6IkhTK0ZGY09OeFYvQTZEV0lrTzVYY3c9PSIsInZhbHVlIjoidWhtRGFXbEJWcklqS1hqbUlBTTJScnRTRUZIMFFoS0h6MFN3M0xwangrMjEzU00wZDJMWEV6ZWwxWUdZTGMxMVZSRktCZDk1VjJSKzdvNG9DTVJYUWEzUFdyZVhiZm9mVzVmUWxMNE4vcFR2V2gxTHB1MHdFQllTRkJBRkpxRHpoK1FzWDBERGFIVXd5UXo3ZDZHKzVvUUdkbW1kK0ExdDR0b1lxRDVtL3dMS3JQRFNQdDlHMm10RytmZUpZWVFFRS8xUE1tOTBIN1gvSU1wVmNMcjNhSlBzRi9XcHNkM3IzZTB5Z09NVCs1MmlPR0JjcVpab2lIaWxNZHRlNnRZMENYc1BYNGtWZ2NGbW1LaUpDU20xYUVVMW5KK3hIbmFoUEhNYXVzZnR4UmM3NEdIZlhBQ0pjTXVJTzl0K00xcHVnR2J3TFhYZkNXMHB3dEI1QWUxOTIxUDUxZU5Ld29KTWRtbXdBb0ZiM2lSQTBnMldnTkxaUjhhbCtMdEY5TjNUK3lLV2t0UFN3MW1xWHBPb01WYWM3clVZSnJqV1dLTUVyNVp4NEF3R0dlYlhmOVNnRnhKSWZVOHVjV1QveWFnS0NkTVAwTWtkbFlNV3lXc1JLaTFEQnlzUTF0K0R4bC83aGMydUdQWEMrMERSa3VaMWMxelNrYm9OOC9WdjlWSE8iLCJtYWMiOiJiMmVkNzNhYWMzNGM5MzBjNGYxZjI3YzhkMDJmYzZlOTI4ZmU2MjA3NWU3YjA2ZDI4ZTkwZWI2OGYxZWZlYWJjIiwidGFnIjoiIn0%3D; expires=Tue, 17-Jun-2025 15:53:10 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-927989115\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>16</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"30 characters\">http://localhost/hrm-dashboard</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n"}}