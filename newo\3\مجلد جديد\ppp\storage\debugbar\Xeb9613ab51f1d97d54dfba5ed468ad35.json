{"__meta": {"id": "Xeb9613ab51f1d97d54dfba5ed468ad35", "datetime": "2025-06-17 13:10:06", "utime": **********.307705, "method": "POST", "uri": "/chats/getContacts", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1750165805.489282, "end": **********.307734, "duration": 0.8184521198272705, "duration_str": "818ms", "measures": [{"label": "Booting", "start": 1750165805.489282, "relative_start": 0, "end": **********.163161, "relative_end": **********.163161, "duration": 0.6738791465759277, "duration_str": "674ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.163182, "relative_start": 0.6739001274108887, "end": **********.307737, "relative_end": 3.0994415283203125e-06, "duration": 0.14455509185791016, "duration_str": "145ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 46131024, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/getContacts", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getContacts", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "contacts.get", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=333\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:333-390</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.01021, "accumulated_duration_str": "10.21ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.237988, "duration": 0.0070599999999999994, "duration_str": "7.06ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 69.148}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.269394, "duration": 0.00159, "duration_str": "1.59ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 69.148, "width_percent": 15.573}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (15) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.284892, "duration": 0.00156, "duration_str": "1.56ms", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 84.721, "width_percent": 15.279}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "zwnmKcRLmNWGDZ856YJrjbbNuEV0Tli3iJOiUNS6", "_previous": "array:1 [\n  \"url\" => \"http://localhost/financial/productservice\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/getContacts", "status_code": "<pre class=sf-dump id=sf-dump-1229876232 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1229876232\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-212992147 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-212992147\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1597930263 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">zwnmKcRLmNWGDZ856YJrjbbNuEV0Tli3iJOiUNS6</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1597930263\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1290458866 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">http://localhost/financial/productservice</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1994 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=a091af98-270d-406e-ab7e-33a6e610d6e2414686; _clck=nups8l%7C2%7Cfwu%7C0%7C1960; _clsk=kau4eo%7C1750165656311%7C14%7C1%7Cl.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6Imp6aEpRVC9Cc2VsTm5iZlhXd3lSb3c9PSIsInZhbHVlIjoibkZnNy9CMzhHeE4wSWxSK21tQ3VPdVI0NjhJUytSRndTdER0a1kvNmZJVGY1WFhjenBKZHc1U0pxbzYrRmhoM01zaFpidFN0VVZJUXA4Z05FYndFVVkvVHZIZ1dKajFpZjlZVHl0M3pqUjRFTGxJREpTT3lDTlJXUnQwc1piajNWUFF6aXlyUnhpSUpUN2pVbGZOalBBdUVEN1dKbVUvQ0xyVWJ1UWZCcXJKdGJLaFhGSGF0V3Z2QzdzV1hCazJZWjViQ040dlNuZmgxTDVEd3U2WVZTTU9NMUNMSVNuR0FvYmZVS1g0TWNDdE9TU3NuN0hINnMvQTdWazFtakpKcHR4QXBKcGpvOTFjam5va0IyeWJLaCs1N01kZDBJOTJmb3pQMHd6T1h5RG1LMjg4b2tNei9GMHEvS0k0ZW9xVHNqeSs0YWpvTFc1VUMwalZTV1RNZm1udWhKVGZLVndjOHdkR1hhVzBSY1dYTUU0VW5lc2ZJbjY2ay90bVNNK09VWHhlSVoxSTJldkVac0orSTZMd2RUdnZhRGlOWGZqeDVnT3NqRWRaZWZZOUEzdXd3TjNQZW5GSWUyWmRrWFkwTlJFbXpUZXo5MUM1NWJTNldDTVA0cUpNbDJ3bHRvbHpYdEFIQmo0RDZIdTFMWFo4cWZacjdYbnZkUnRwenkvUnAiLCJtYWMiOiI5MzNhYjI0MmU0ZmNiNmQ0NTM1YmMwNmJkZGM4ZjQzNDQ0M2UxYjIyYzUwNGNhYjE0NDNlNjNmZjhkZWIxNGNjIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6IlcveUdaUWYxSStTbldQcENlS2xDeHc9PSIsInZhbHVlIjoiZ25TR05xN2ZvRG1hb21Hdy9zYitYRkxuU1ZTSU5ZQW5IbjZUaHNuUEVhRGJUaXhxTHlaWWF4VW1KZW9SSXVoakU1c0dScjFtZWtYdm1mc0VyYW1WTGVsQTlQcEs5Qjl6dFdGZ0FDWDFhckxWWmc5YytadGRnd1FCYUlyM1MzUzdzVHYyR3B3RUgzYlFwYm9DaWpnNkpQQnhCb25ReGhxUDBrU0lOcnJlWmx1UDl1L1kyUEQ0eHFLb0MxV0JoaFNOWDlXOHJPY21DUlV1WVQ4ckVZK0JHNnVkQkFsNm5WNWZJdnhtRnhNYWw5WkhmbjJNTEEvem5iN3V5TlErcGttNm94eUdQN2pER2gzVmVzcldJM0FSYy9mdFVlc09zcW03YThrN0FETDBBR3p4TVJTajdNUk5KMkFwTElPNjlTdE83dEZObHcyN0phVGtaY1EzbHFTdFYyN2UvU0h6VHB5c2Rycm1DY2c2d2xnc3NaSFdadWJaVWpycE1TWU1MK2MzMVJBK2dOdTYyWlRzQkJKeTRDZ0paeHZYOTlZOTZ0UlY4VTVPd080MkZVQThhak80SmJOWXM3K3BKejgxWmhhTVBGWnFZcVkwaU9BbFIyVzltQmdpcGJwb2o2R3dpbWdxUlZWL2l6dFBQR1FIR3pxMHVSZTZTT2hrQlVoMWJkL2EiLCJtYWMiOiJkYjE3YTViZDQ1ZjJkMDUyYmQ1NWJkMGE1MDM4NmI4MjU3MTc3Yjc0Y2NkZGRjMjEyMGFkNGYzMGQwOWFlOTQ0IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1290458866\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">zwnmKcRLmNWGDZ856YJrjbbNuEV0Tli3iJOiUNS6</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">HxnOnSuhAAPTfN2VS4q58slgJoytwtHYqaX0oLoW</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 17 Jun 2025 13:10:06 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IklSb0hPRFowK1Q1SStXVkdKdHh1K3c9PSIsInZhbHVlIjoiWnZPRUJNWjRUQnkxYXpyaGdGQmdoRXJFZzNTOTc5Vk5EUG90VG9jMWprRDEzdktORDl2K1BSVnN1SlNUU08rU3BsZHJQWmx6SkpVWXNHa05jSFhnbzcwTmt4MHhHbjNtWnFGU09UMEFFL1RxY2pEaERxdWFOa0tYaHpaSUwrYU1UVms3Y3E2OHlDRWN6cU0yU2pHa1BML0hqM1dFSkw2cGFrSkxEeVZsbUpSV1hOanRiV1VyOUNKQWU0bWxWL0hFc3VjaGk3bHRBQXUwSUJjbVNBR0pRTG5UT2tVS294bTNUVmowcmhwd3RPU0E2N2tyVUNPQThuOEUvaVV4UHdreFdWWm9xKy9KT3pvbURxejB6YS9paDdnNkNZM0lSb1F1eXpYU24wd2RZY3lRZlBjaVNCaGJzdjh0dXNuQk9SMDRWOFpueG5HQkRsQ3FsUVFmaWJuUVJ6aER6UlgzK251ck4vMFh4dUU1ZFo5MHMrOGZxM2lQM2VkdjdRUzA2dms3YkZ2NkN3S3NPTEdIVE9WT09qL3A0Rkw3QVdYRVo0d0JmWEVaa0hXRjhPZ2xnU1l3dlplY2xMczR6dnljbjViK1ZXdGtCU2lIcG1ENldxWDRkQUdubUFRNHpJcEVXSFNRcDN3dDNqMWZ3RVdLUkZQb093Q3N4M3A0YndIT1FwcTMiLCJtYWMiOiJjMmY4Nzg3ZmU1NzM1MjEzOGRmZTkwMTE1MzdjNDg5NjcyZTQ5YmNiMzJhNTA0YWFjOTNmMTYwNmIzMGJkOGNjIiwidGFnIjoiIn0%3D; expires=Tue, 17 Jun 2025 15:10:06 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6IkVrWm9iSzRWcXUrcVdQUDZDSW9CdlE9PSIsInZhbHVlIjoiaXM3MFMwbWZSMEY4LzlxaThSS3JaN21uV3RHeUtYTVZLc3MxMWhpVTZ1cCsrNGZOVi8vMldUU05MdytVOUJRT3JxUEFEWVlpQkFRRjVCb3E2MjQrWmQ3Vy80aiszVEQ0Z0hFSUpuTmlPOXMxTU9QeFB3UU1ycHlmMjZBbjZ0blRxRkRMekRFRFVGeHNKN1VnRWdDQzZtU0JwckJzS0x4RXI0NXZVOTN2czhpem93YlpRcHdETkVYcVFIckpVeFEwZ25wSGFlVk5BZGpFaWk2cTdTUlJ2UFhMRTd0eUl5OGxzcktXZDdjTUdvZnVLTmJpMk9xQy81N1lLTTdPclVlODVoWm1GRkRWTC9uZU9BTFNadlZPVmVva2Z1cXh4SjJ2eDd2YTJZTzdlQTdWYnNmOVdKc1hOZU5lcmg4d3hKV3JyY2dXSDEvMGMzY1l1YWh6dkp1M0Y5UVNlZU90Uk0wa0RaL0JwR3Q3aDlXbmJBR3d4Y3FDdUxMZzl1c1A1WFdBMGpqdytJZk9lVW80STFLYVN4OUlia0hXVGdRL3h1WGRmUElvbTBrcEdCNHVjUUlwWmthQVVjd3JydUh1cTdyUHkyTVJSR3JNTTFFaW1rcE1QTjVTWjd3cnJhR25PWlMvRk5PVjJKcndDMk5EZjNDS1dsV1lqWXZLWkJ1SGF4N3UiLCJtYWMiOiIzNTIzNTJjYWI5YzM2YTE5NWJjMTliNTQ0OTI0ZDY2ZjM3NTY1OTJjYjQzODhlZDZhZDY3ZDk1ZGZhYzViY2UwIiwidGFnIjoiIn0%3D; expires=Tue, 17 Jun 2025 15:10:06 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IklSb0hPRFowK1Q1SStXVkdKdHh1K3c9PSIsInZhbHVlIjoiWnZPRUJNWjRUQnkxYXpyaGdGQmdoRXJFZzNTOTc5Vk5EUG90VG9jMWprRDEzdktORDl2K1BSVnN1SlNUU08rU3BsZHJQWmx6SkpVWXNHa05jSFhnbzcwTmt4MHhHbjNtWnFGU09UMEFFL1RxY2pEaERxdWFOa0tYaHpaSUwrYU1UVms3Y3E2OHlDRWN6cU0yU2pHa1BML0hqM1dFSkw2cGFrSkxEeVZsbUpSV1hOanRiV1VyOUNKQWU0bWxWL0hFc3VjaGk3bHRBQXUwSUJjbVNBR0pRTG5UT2tVS294bTNUVmowcmhwd3RPU0E2N2tyVUNPQThuOEUvaVV4UHdreFdWWm9xKy9KT3pvbURxejB6YS9paDdnNkNZM0lSb1F1eXpYU24wd2RZY3lRZlBjaVNCaGJzdjh0dXNuQk9SMDRWOFpueG5HQkRsQ3FsUVFmaWJuUVJ6aER6UlgzK251ck4vMFh4dUU1ZFo5MHMrOGZxM2lQM2VkdjdRUzA2dms3YkZ2NkN3S3NPTEdIVE9WT09qL3A0Rkw3QVdYRVo0d0JmWEVaa0hXRjhPZ2xnU1l3dlplY2xMczR6dnljbjViK1ZXdGtCU2lIcG1ENldxWDRkQUdubUFRNHpJcEVXSFNRcDN3dDNqMWZ3RVdLUkZQb093Q3N4M3A0YndIT1FwcTMiLCJtYWMiOiJjMmY4Nzg3ZmU1NzM1MjEzOGRmZTkwMTE1MzdjNDg5NjcyZTQ5YmNiMzJhNTA0YWFjOTNmMTYwNmIzMGJkOGNjIiwidGFnIjoiIn0%3D; expires=Tue, 17-Jun-2025 15:10:06 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6IkVrWm9iSzRWcXUrcVdQUDZDSW9CdlE9PSIsInZhbHVlIjoiaXM3MFMwbWZSMEY4LzlxaThSS3JaN21uV3RHeUtYTVZLc3MxMWhpVTZ1cCsrNGZOVi8vMldUU05MdytVOUJRT3JxUEFEWVlpQkFRRjVCb3E2MjQrWmQ3Vy80aiszVEQ0Z0hFSUpuTmlPOXMxTU9QeFB3UU1ycHlmMjZBbjZ0blRxRkRMekRFRFVGeHNKN1VnRWdDQzZtU0JwckJzS0x4RXI0NXZVOTN2czhpem93YlpRcHdETkVYcVFIckpVeFEwZ25wSGFlVk5BZGpFaWk2cTdTUlJ2UFhMRTd0eUl5OGxzcktXZDdjTUdvZnVLTmJpMk9xQy81N1lLTTdPclVlODVoWm1GRkRWTC9uZU9BTFNadlZPVmVva2Z1cXh4SjJ2eDd2YTJZTzdlQTdWYnNmOVdKc1hOZU5lcmg4d3hKV3JyY2dXSDEvMGMzY1l1YWh6dkp1M0Y5UVNlZU90Uk0wa0RaL0JwR3Q3aDlXbmJBR3d4Y3FDdUxMZzl1c1A1WFdBMGpqdytJZk9lVW80STFLYVN4OUlia0hXVGdRL3h1WGRmUElvbTBrcEdCNHVjUUlwWmthQVVjd3JydUh1cTdyUHkyTVJSR3JNTTFFaW1rcE1QTjVTWjd3cnJhR25PWlMvRk5PVjJKcndDMk5EZjNDS1dsV1lqWXZLWkJ1SGF4N3UiLCJtYWMiOiIzNTIzNTJjYWI5YzM2YTE5NWJjMTliNTQ0OTI0ZDY2ZjM3NTY1OTJjYjQzODhlZDZhZDY3ZDk1ZGZhYzViY2UwIiwidGFnIjoiIn0%3D; expires=Tue, 17-Jun-2025 15:10:06 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">zwnmKcRLmNWGDZ856YJrjbbNuEV0Tli3iJOiUNS6</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"41 characters\">http://localhost/financial/productservice</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n"}}