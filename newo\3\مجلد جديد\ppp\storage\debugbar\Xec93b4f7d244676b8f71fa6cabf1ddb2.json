{"__meta": {"id": "Xec93b4f7d244676b8f71fa6cabf1ddb2", "datetime": "2025-06-17 13:53:07", "utime": **********.105343, "method": "POST", "uri": "/chats/getContacts", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1750168386.494776, "end": **********.105372, "duration": 0.6105959415435791, "duration_str": "611ms", "measures": [{"label": "Booting", "start": 1750168386.494776, "relative_start": 0, "end": **********.006049, "relative_end": **********.006049, "duration": 0.5112729072570801, "duration_str": "511ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.006067, "relative_start": 0.5112910270690918, "end": **********.105375, "relative_end": 3.0994415283203125e-06, "duration": 0.09930801391601562, "duration_str": "99.31ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 46130632, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/getContacts", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getContacts", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "contacts.get", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=333\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:333-390</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.023459999999999998, "accumulated_duration_str": "23.46ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 16 limit 1", "type": "query", "params": [], "bindings": ["16"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.047551, "duration": 0.02114, "duration_str": "21.14ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 90.111}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.080669, "duration": 0.0011, "duration_str": "1.1ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 90.111, "width_percent": 4.689}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (16) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.091624, "duration": 0.00122, "duration_str": "1.22ms", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 94.8, "width_percent": 5.2}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "16", "_previous": "array:1 [\n  \"url\" => \"http://localhost/hrm-dashboard\"\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/getContacts", "status_code": "<pre class=sf-dump id=sf-dump-199274455 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-199274455\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-31349942 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-31349942\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-28594115 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-28594115\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1383305761 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"30 characters\">http://localhost/hrm-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1936 characters\">_clck=2gyfy%7C2%7Cfwu%7C0%7C1994; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clsk=1ti4td2%7C1750168382688%7C6%7C1%7Cl.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6ImMweXRhdWE2YklVbVJRdURHUi9qeHc9PSIsInZhbHVlIjoiRHJZbENNZkVYcVZMU25ycVBhNEtVT1JHbkx1VzJZUzR2VTNUNm5CY0p5WWdRUEx4TjNWV21VWWRrZGJ3Uk9kZU5LcmxUQjJvSGdjNU9paWphYXRUM3ptUzcva1doaUdNRlNla0JDSE9rL0pKM2U3RnZZWE81WDVmbWQ1eC8xb3VYMmdZVks1ckFUU1E2RzYyS2Myd2tvWmorNUM1M3NXS25EK3E3eElrTFFwNjU2QjczREZjSGt0N3hjMXhvcklCVzdmSFB4cHVucUQ1VTNpVE9SQUpRcWtDNTkrM295VitQQjQ5NUVkWTJCNk5nR2tnVFg5ZkZYUlQxb3pPbWh4UDBNMTMvVTU3L1hpL1FnQUpkSnRDSkIrSWZESVJYV2I2a2ZidU8wcEQvcDA2T3R3LzRkZlYwclAzUGZoZlROeEk2Q2ZRL1pScEtsdmkwK21NVmd4azhjMjdrcHpiVDFuYmd1MFU1ZXBQV0crQWFmZjNES2RTcEkvNUVyZzh5VEVRWW1YeWFyQ1FRVnI3UGRUVnU1SVM5dkRrMGY4YThDdkhZVWlSQUJDOWdhdnM1YXQ2a0I5YlNlK1FJS1ZtSEhOU3hQamthSEI2RzVHZXBTN2VqODhKSEo0TVR0QkRGQ0lMM3dpSTkzOC80OXdoaitVTmdXRGZyZVVUUXkyR21ET1oiLCJtYWMiOiJiY2JhZTc5ZDZmOTE2MTE5YmRlMTU4NWE4NzAwYzgxMWJhMzdkOTRiZDcwNjlkNGY2NGE1YzFlODNkMDFmZTZlIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6IkZzSWhUdFFuOGVOcklvVkxQOE9OYWc9PSIsInZhbHVlIjoiTFdRVU5UQTVZZS9yQzJ0eHloTDluYm1nN2tqeVJ3UE41TEdESXBOZzdZM1g3TmtkQ0J2VDk0REhDTHlTSk9NWXB2NEc2WHI2eEFNZGVGU04xanRRUCswQTNsNE1DOU5WcHJxY0tnTWxleW9WWmZuR0hTKytCdk5SMzFIWUpmR1l6RFc1d2tINkJjRVNxZGt0Z2tKT3QrWTRlYUptNXpaTVVtUFk5S1l4YmRjdVFMYzE0RmRJS0NZdVNsR1YvUndjdERtK0pYWDNVcTZ5M3ZBdGxBcVBYWkZTQjVKM081aEtMYzRGSzliZ0pxVWFpVG9NeGlwcmNEVndNRm1LcnBaYzh1cHdJTDc2WHlpMlhLWkFqOTdjMjhDRDR6aWNtYnh3UnNiVnBrb1c5cEZDandsVWtRTzhudDN0ZVlqUUc0OG5hZXJBYTZpdWs1bmRQa29zbU9Rb1pzNnlLWCtzVjN1cWFMRVJhcmZZZjJTVmh6K1ZwczVDUGc5TE0zQkZKcmM3NEZnaXpTdDVVWFcyaEMzS0ZXZTJyZEpmQXFCQ0R5OG10aTBqR05DUUN6UU5jUUpzT096NEE1S2tVVGdpV25hREwvajVQUzU5RUdzbFBpQWNzNkVjV2NJaUt0Ylo4ejkzY0xMTlIyeWovQUpnZEFteSt3SGpUR29tNmhGT2RCUkkiLCJtYWMiOiJkMmM0OWMzMTc5ZjcwNjI4NmUyNWViMzY3NjdjMTUyN2E5NTU2NjE0ODllZDU1ZWJkMjJiMDlhMzQ0NTI1NWM0IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1383305761\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1077170037 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">VloaUQCBjpDKrel7nHeAzB1sTLdTMwteSos1MmAN</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1077170037\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1516163113 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 17 Jun 2025 13:53:07 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IjFUcitaUjdPUEcvZlpVVE1lNisrYVE9PSIsInZhbHVlIjoiUWlUWG5FOTFBNVptcVN4NWliZU9tU2dWMEJYQnlidngwaFFNZ3R2VGk2QjF2ZUk5MXBxK0NPYUZNYVZDQTd4c2w0dGhCWE5xV013TnhEak44ZDF2bVMzYVRoNzNMTnlnYjUxalZxREsxQ2tpSkdYVzMwRGx1M0l1MDdVNVRMeUZ0ME1HOFV0UVVGRG16bC9TSi9iTHk2SDdHT3ByMWJLMWNUakRBU3lGdGVhcEkxcGNOZ3NWeWJFdTY4T0pSL21oajU4Y2lQeWVyS2tGdmNQQ21pMTlSdm80ZStSR3BoMnhOaklWd0RHNkdaaWd5SWZUbWYzb1JnZTVXMDljSzA3azhMSGgxeHU5RVZiVFVQODVxQWhBM003UlN0SVB6NjFYRi9HOUpGVG83dE5DN3dteWF1NkxoS0FmWWpQSkNiUHlUSHVXZTRIQjJMZFZrUzg2Z1N1amtINFJBL3ZJNEVSei95SnB3ZGJLYzhudkxBZS9XZFVGZGozOVRSamZkTlBWQWFVUlRNaFliWGphcFhJa1RUbHFPdnRFNWk5NVBicWtOTHZNdWVPcG1PS1ZnK2FzdkJIS0l0clhVb3MySmlrVGUxRGRTekZRWjZHblIvWlhScDdDY2JmYkoyZXphcGZ4d0RSTnlqNkZyaVRUYmJtdk0vYUVFM2paVFVtd2ovMWkiLCJtYWMiOiI1MmFiM2FhM2YyOTllMjc1ZGI0ODRhNmJjMDI5NjBlNDcxMDUzNWJlYjVmMmYxOWY4ZDQ3YjZhZjc0MGU0MjlmIiwidGFnIjoiIn0%3D; expires=Tue, 17 Jun 2025 15:53:07 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6InJmQkpIT2tzZ1UzbmlLdGRlOXRaS2c9PSIsInZhbHVlIjoidVdkL1hOU1hlTy8wTkpNNTd6ZVdNeFpIV1BraTNIblN3M0d5NXAzc0RHcXMxZHZxdEJBbC9hUTVFWkkyNkZaZmZGamxhbDJaSmoxVUtmQ1FLSVZLWTg5RkdEemtMSWRJOUdKS2c3bGJibVQ5TlROczhnL00yY28vYldKUjhUbldBT3UzbnpWRVVzSXhrMnFNbWRKNmVlK3M5SWl5dDlZUUNadmFXREJCZ1doMUlOdEFnV3JFUHJVbFUvZVhsTHpFM1R4anB4L0JFQzk0N0ZDQm8yNnpaN2tZVjZWa0pudGpaZ3grOGNpbnZ4VkZtRSsrMkNaMkM0THBBc1FETGNwSGF6Tk1PU0JNUDFlM2xQUThWenBnM2xiaTRFRDg4dFNNUk94cUFCRFJ4N2VkNGNiblFxK1lidWhFcmZDM3J4SmM5MEJRZjdKVzNKQ1VBY3dmWDlrUEF5MFpzQ2xHbWxSTHlBTzRCdmhEU2ZhSHNKVk1KdmZXTVFKcVpoR0tkNTFNcTdTNFhYazRCVDdKS0c5Sms4QmtaNzJHR0tTK1pFQVVjTEV6OXZGUFdtc3lnZk55d2t2M29reEg0cDRCVVhmZi9RV0tUcGZ2YmZITXB0eWVocVFUMUt2UEZSR1lMRUVSc1lFMCtUdy9tenRjTDVQWnFGUEVlQmViLzF1SWZIVVkiLCJtYWMiOiIzNzEyMGJhY2VkMzRlZTU2ZDFmNzJmMDkzMjM5MGViNzE0MzE2OWIwNjE0NDZkMDAwOGMwNTJjM2UxNWNiOGMzIiwidGFnIjoiIn0%3D; expires=Tue, 17 Jun 2025 15:53:07 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IjFUcitaUjdPUEcvZlpVVE1lNisrYVE9PSIsInZhbHVlIjoiUWlUWG5FOTFBNVptcVN4NWliZU9tU2dWMEJYQnlidngwaFFNZ3R2VGk2QjF2ZUk5MXBxK0NPYUZNYVZDQTd4c2w0dGhCWE5xV013TnhEak44ZDF2bVMzYVRoNzNMTnlnYjUxalZxREsxQ2tpSkdYVzMwRGx1M0l1MDdVNVRMeUZ0ME1HOFV0UVVGRG16bC9TSi9iTHk2SDdHT3ByMWJLMWNUakRBU3lGdGVhcEkxcGNOZ3NWeWJFdTY4T0pSL21oajU4Y2lQeWVyS2tGdmNQQ21pMTlSdm80ZStSR3BoMnhOaklWd0RHNkdaaWd5SWZUbWYzb1JnZTVXMDljSzA3azhMSGgxeHU5RVZiVFVQODVxQWhBM003UlN0SVB6NjFYRi9HOUpGVG83dE5DN3dteWF1NkxoS0FmWWpQSkNiUHlUSHVXZTRIQjJMZFZrUzg2Z1N1amtINFJBL3ZJNEVSei95SnB3ZGJLYzhudkxBZS9XZFVGZGozOVRSamZkTlBWQWFVUlRNaFliWGphcFhJa1RUbHFPdnRFNWk5NVBicWtOTHZNdWVPcG1PS1ZnK2FzdkJIS0l0clhVb3MySmlrVGUxRGRTekZRWjZHblIvWlhScDdDY2JmYkoyZXphcGZ4d0RSTnlqNkZyaVRUYmJtdk0vYUVFM2paVFVtd2ovMWkiLCJtYWMiOiI1MmFiM2FhM2YyOTllMjc1ZGI0ODRhNmJjMDI5NjBlNDcxMDUzNWJlYjVmMmYxOWY4ZDQ3YjZhZjc0MGU0MjlmIiwidGFnIjoiIn0%3D; expires=Tue, 17-Jun-2025 15:53:07 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6InJmQkpIT2tzZ1UzbmlLdGRlOXRaS2c9PSIsInZhbHVlIjoidVdkL1hOU1hlTy8wTkpNNTd6ZVdNeFpIV1BraTNIblN3M0d5NXAzc0RHcXMxZHZxdEJBbC9hUTVFWkkyNkZaZmZGamxhbDJaSmoxVUtmQ1FLSVZLWTg5RkdEemtMSWRJOUdKS2c3bGJibVQ5TlROczhnL00yY28vYldKUjhUbldBT3UzbnpWRVVzSXhrMnFNbWRKNmVlK3M5SWl5dDlZUUNadmFXREJCZ1doMUlOdEFnV3JFUHJVbFUvZVhsTHpFM1R4anB4L0JFQzk0N0ZDQm8yNnpaN2tZVjZWa0pudGpaZ3grOGNpbnZ4VkZtRSsrMkNaMkM0THBBc1FETGNwSGF6Tk1PU0JNUDFlM2xQUThWenBnM2xiaTRFRDg4dFNNUk94cUFCRFJ4N2VkNGNiblFxK1lidWhFcmZDM3J4SmM5MEJRZjdKVzNKQ1VBY3dmWDlrUEF5MFpzQ2xHbWxSTHlBTzRCdmhEU2ZhSHNKVk1KdmZXTVFKcVpoR0tkNTFNcTdTNFhYazRCVDdKS0c5Sms4QmtaNzJHR0tTK1pFQVVjTEV6OXZGUFdtc3lnZk55d2t2M29reEg0cDRCVVhmZi9RV0tUcGZ2YmZITXB0eWVocVFUMUt2UEZSR1lMRUVSc1lFMCtUdy9tenRjTDVQWnFGUEVlQmViLzF1SWZIVVkiLCJtYWMiOiIzNzEyMGJhY2VkMzRlZTU2ZDFmNzJmMDkzMjM5MGViNzE0MzE2OWIwNjE0NDZkMDAwOGMwNTJjM2UxNWNiOGMzIiwidGFnIjoiIn0%3D; expires=Tue, 17-Jun-2025 15:53:07 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1516163113\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>16</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"30 characters\">http://localhost/hrm-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n"}}