{"__meta": {"id": "Xf13c076e80a7a6b26a323cb945bd44e3", "datetime": "2025-06-17 13:53:07", "utime": **********.123504, "method": "POST", "uri": "/event/get_event_data", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1750168386.493774, "end": **********.123528, "duration": 0.6297540664672852, "duration_str": "630ms", "measures": [{"label": "Booting", "start": 1750168386.493774, "relative_start": 0, "end": **********.014384, "relative_end": **********.014384, "duration": 0.5206100940704346, "duration_str": "521ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.014403, "relative_start": 0.5206291675567627, "end": **********.123531, "relative_end": 3.0994415283203125e-06, "duration": 0.10912799835205078, "duration_str": "109ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 46443248, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET event/get_event_data", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\EventController@get_event_data", "namespace": null, "prefix": "", "where": [], "as": "event.get_event_data", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FHttp%2FControllers%2FEventController.php&line=317\" onclick=\"\">app/Http/Controllers/EventController.php:317-345</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.031540000000000006, "accumulated_duration_str": "31.54ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 16 limit 1", "type": "query", "params": [], "bindings": ["16"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.059659, "duration": 0.02945, "duration_str": "29.45ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 93.373}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.1047542, "duration": 0.0008100000000000001, "duration_str": "810μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 93.373, "width_percent": 2.568}, {"sql": "select * from `events` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/EventController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Http\\Controllers\\EventController.php", "line": 327}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.111471, "duration": 0.00128, "duration_str": "1.28ms", "memory": 0, "memory_str": null, "filename": "EventController.php:327", "source": "app/Http/Controllers/EventController.php:327", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FHttp%2FControllers%2FEventController.php&line=327", "ajax": false, "filename": "EventController.php", "line": "327"}, "connection": "kdmkjkqknb", "start_percent": 95.942, "width_percent": 4.058}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "16", "_previous": "array:1 [\n  \"url\" => \"http://localhost/hrm-dashboard\"\n]"}, "request": {"path_info": "/event/get_event_data", "status_code": "<pre class=sf-dump id=sf-dump-455653768 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-455653768\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-713910753 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-713910753\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1433569790 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1433569790\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-31168457 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"30 characters\">http://localhost/hrm-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1936 characters\">_clck=2gyfy%7C2%7Cfwu%7C0%7C1994; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clsk=1ti4td2%7C1750168382688%7C6%7C1%7Cl.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6ImMweXRhdWE2YklVbVJRdURHUi9qeHc9PSIsInZhbHVlIjoiRHJZbENNZkVYcVZMU25ycVBhNEtVT1JHbkx1VzJZUzR2VTNUNm5CY0p5WWdRUEx4TjNWV21VWWRrZGJ3Uk9kZU5LcmxUQjJvSGdjNU9paWphYXRUM3ptUzcva1doaUdNRlNla0JDSE9rL0pKM2U3RnZZWE81WDVmbWQ1eC8xb3VYMmdZVks1ckFUU1E2RzYyS2Myd2tvWmorNUM1M3NXS25EK3E3eElrTFFwNjU2QjczREZjSGt0N3hjMXhvcklCVzdmSFB4cHVucUQ1VTNpVE9SQUpRcWtDNTkrM295VitQQjQ5NUVkWTJCNk5nR2tnVFg5ZkZYUlQxb3pPbWh4UDBNMTMvVTU3L1hpL1FnQUpkSnRDSkIrSWZESVJYV2I2a2ZidU8wcEQvcDA2T3R3LzRkZlYwclAzUGZoZlROeEk2Q2ZRL1pScEtsdmkwK21NVmd4azhjMjdrcHpiVDFuYmd1MFU1ZXBQV0crQWFmZjNES2RTcEkvNUVyZzh5VEVRWW1YeWFyQ1FRVnI3UGRUVnU1SVM5dkRrMGY4YThDdkhZVWlSQUJDOWdhdnM1YXQ2a0I5YlNlK1FJS1ZtSEhOU3hQamthSEI2RzVHZXBTN2VqODhKSEo0TVR0QkRGQ0lMM3dpSTkzOC80OXdoaitVTmdXRGZyZVVUUXkyR21ET1oiLCJtYWMiOiJiY2JhZTc5ZDZmOTE2MTE5YmRlMTU4NWE4NzAwYzgxMWJhMzdkOTRiZDcwNjlkNGY2NGE1YzFlODNkMDFmZTZlIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6IkZzSWhUdFFuOGVOcklvVkxQOE9OYWc9PSIsInZhbHVlIjoiTFdRVU5UQTVZZS9yQzJ0eHloTDluYm1nN2tqeVJ3UE41TEdESXBOZzdZM1g3TmtkQ0J2VDk0REhDTHlTSk9NWXB2NEc2WHI2eEFNZGVGU04xanRRUCswQTNsNE1DOU5WcHJxY0tnTWxleW9WWmZuR0hTKytCdk5SMzFIWUpmR1l6RFc1d2tINkJjRVNxZGt0Z2tKT3QrWTRlYUptNXpaTVVtUFk5S1l4YmRjdVFMYzE0RmRJS0NZdVNsR1YvUndjdERtK0pYWDNVcTZ5M3ZBdGxBcVBYWkZTQjVKM081aEtMYzRGSzliZ0pxVWFpVG9NeGlwcmNEVndNRm1LcnBaYzh1cHdJTDc2WHlpMlhLWkFqOTdjMjhDRDR6aWNtYnh3UnNiVnBrb1c5cEZDandsVWtRTzhudDN0ZVlqUUc0OG5hZXJBYTZpdWs1bmRQa29zbU9Rb1pzNnlLWCtzVjN1cWFMRVJhcmZZZjJTVmh6K1ZwczVDUGc5TE0zQkZKcmM3NEZnaXpTdDVVWFcyaEMzS0ZXZTJyZEpmQXFCQ0R5OG10aTBqR05DUUN6UU5jUUpzT096NEE1S2tVVGdpV25hREwvajVQUzU5RUdzbFBpQWNzNkVjV2NJaUt0Ylo4ejkzY0xMTlIyeWovQUpnZEFteSt3SGpUR29tNmhGT2RCUkkiLCJtYWMiOiJkMmM0OWMzMTc5ZjcwNjI4NmUyNWViMzY3NjdjMTUyN2E5NTU2NjE0ODllZDU1ZWJkMjJiMDlhMzQ0NTI1NWM0IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-31168457\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-2049505471 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">VloaUQCBjpDKrel7nHeAzB1sTLdTMwteSos1MmAN</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2049505471\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-719572341 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 17 Jun 2025 13:53:07 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IlFNanhIVWZoRmkzMFVpQVZ0UjJXUkE9PSIsInZhbHVlIjoiQ2doTlVIb2RNUEV4RE9wV1BsZDcwUW1CU1hCdHBVNUVMeFh2akdqQWNMK3duNXA3bjJmbmpvVEh2RzMwbE0zN1UrdXdqNGdsb2RjUk0ydU04a0RvaEtXTnlrWDZLQlF3eHRoNGVFbExuOWRYR05kMmtDc3EyYmtlN3VkelA4OHhiaXhHNFowbXU1QXUxSUlDSWhtcDJhRVk2NHNBcklYczhkTHp0V0lQSFBHRVZpcVRDYWRRSGtlNVFQcVhWaCsxaU1wQjJVWjF6bDIvWm1oQ01hMUJwR1gwNlF6Y2g3THlRRmEyaVpoM0U2ZGJYeXl4c3J2cG5QdjlhVXEvM3ZLSjdGdzkwVkoybEk2MjhERk15MStlT0FxTDhQUXQwTEVUWGhkRU1GeXF3dVFVRnhRbjZpS0xhMmhpdnhHSXQ5UC80YnRINE5nZkpnWGcvdXNYcFRBcmJkZDI1dWpBb2ZuK1pwdWI4eHE1cmc1RVpoZXhaUlQ4SFZKZ0xUQ0pEbTk1SDdlelJjcVo3MXM4QjJRSlErTlVwZUEzZHZOM01OZFovYVl2U2lEV0ZiaDMxYWttQUFHMGdEQVJlWit3cmczL1FvbzFmUzBldnhFZW1uYlhTVENJTFhaN0NIY0VuVWJnUytyekk4b0ZJRjNtbkJvRTM2SFphaERzUTJpRDJzQlQiLCJtYWMiOiIwMDkxMTNiNTlkZTFhMGQxZGZkMmZhZTMyODU1ODMwODU1YzQ3ZGY2NWJlMzVhYjE4YzI2NGJiODdhMmFlM2ZiIiwidGFnIjoiIn0%3D; expires=Tue, 17 Jun 2025 15:53:07 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6Im1nWERtM1A2bTgzMkRNeWRmZnNvSmc9PSIsInZhbHVlIjoiTncwMXZYQkp0SDdJZ1FQVlFySERZUGN0RlRKaGJWNTFRTnByRDQ1VFB2MzRCL3RsRUUvdmQ2Ui9BbXRGU284bStoeFZXdktXTnRyR0U2OURsUEVGcHV1cy96RzBsVStKNU9tVzhlYUZMS1dJbjc3TDNxYnBZVUNvYkZ3VmNvcktQaFZmeHdLVDRrakNKdTQwS2xzYUtCcEhFenpQNmg3ZnpHVndCYUE5MWxueU9XaTNudTlwSjBzNGgzWjZlM0lINlhDYkxBR0JFTjFrVExLd3lTb3hGMXJzMC93c0p5bVhtbHdHRmppKzJTa0lCa3hSSDNXSmZiRGpra0lIZ1NzK3NLUThqb3JwNUJOb1YzY2UyZFBxUHZ2eTVMcVJ0eXd3a2hmVndNamlEdnR0Ly9Zd1dsRlRDbWF0ZVozU0p2ODVXYUZXOFNvaE5vbUZDbGJ4emZFL0lnaFVpeGQxaUJVck9HUkZLMjBNY1FSSDFzNHlPRmZPV2loSjE1R2NibXBHTHlhRUpkaFcwNW1wbUtMMFFOTm9GV245R1dEdUV5QklwdWl3SXBSamtmZWkvL3VrZUtkbXZ6V1JQZkhhSklIYVBteVZlMHdhZTdLeUM4T0E1QTh0aTR5THZEUE5iM2h5Y2FreEFHVitFTVhZbDVxbk51QjFMN2Fadk55VWNUL3MiLCJtYWMiOiJiZGQ3NTc3YmJlNzc5ZWFmMGI1YjNkMTQ1YzI2ZTJjMDk3ODlhYWU1YjJjNjkxNWI4OWU0Nzg5Y2Y1MTc4MmU4IiwidGFnIjoiIn0%3D; expires=Tue, 17 Jun 2025 15:53:07 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IlFNanhIVWZoRmkzMFVpQVZ0UjJXUkE9PSIsInZhbHVlIjoiQ2doTlVIb2RNUEV4RE9wV1BsZDcwUW1CU1hCdHBVNUVMeFh2akdqQWNMK3duNXA3bjJmbmpvVEh2RzMwbE0zN1UrdXdqNGdsb2RjUk0ydU04a0RvaEtXTnlrWDZLQlF3eHRoNGVFbExuOWRYR05kMmtDc3EyYmtlN3VkelA4OHhiaXhHNFowbXU1QXUxSUlDSWhtcDJhRVk2NHNBcklYczhkTHp0V0lQSFBHRVZpcVRDYWRRSGtlNVFQcVhWaCsxaU1wQjJVWjF6bDIvWm1oQ01hMUJwR1gwNlF6Y2g3THlRRmEyaVpoM0U2ZGJYeXl4c3J2cG5QdjlhVXEvM3ZLSjdGdzkwVkoybEk2MjhERk15MStlT0FxTDhQUXQwTEVUWGhkRU1GeXF3dVFVRnhRbjZpS0xhMmhpdnhHSXQ5UC80YnRINE5nZkpnWGcvdXNYcFRBcmJkZDI1dWpBb2ZuK1pwdWI4eHE1cmc1RVpoZXhaUlQ4SFZKZ0xUQ0pEbTk1SDdlelJjcVo3MXM4QjJRSlErTlVwZUEzZHZOM01OZFovYVl2U2lEV0ZiaDMxYWttQUFHMGdEQVJlWit3cmczL1FvbzFmUzBldnhFZW1uYlhTVENJTFhaN0NIY0VuVWJnUytyekk4b0ZJRjNtbkJvRTM2SFphaERzUTJpRDJzQlQiLCJtYWMiOiIwMDkxMTNiNTlkZTFhMGQxZGZkMmZhZTMyODU1ODMwODU1YzQ3ZGY2NWJlMzVhYjE4YzI2NGJiODdhMmFlM2ZiIiwidGFnIjoiIn0%3D; expires=Tue, 17-Jun-2025 15:53:07 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6Im1nWERtM1A2bTgzMkRNeWRmZnNvSmc9PSIsInZhbHVlIjoiTncwMXZYQkp0SDdJZ1FQVlFySERZUGN0RlRKaGJWNTFRTnByRDQ1VFB2MzRCL3RsRUUvdmQ2Ui9BbXRGU284bStoeFZXdktXTnRyR0U2OURsUEVGcHV1cy96RzBsVStKNU9tVzhlYUZMS1dJbjc3TDNxYnBZVUNvYkZ3VmNvcktQaFZmeHdLVDRrakNKdTQwS2xzYUtCcEhFenpQNmg3ZnpHVndCYUE5MWxueU9XaTNudTlwSjBzNGgzWjZlM0lINlhDYkxBR0JFTjFrVExLd3lTb3hGMXJzMC93c0p5bVhtbHdHRmppKzJTa0lCa3hSSDNXSmZiRGpra0lIZ1NzK3NLUThqb3JwNUJOb1YzY2UyZFBxUHZ2eTVMcVJ0eXd3a2hmVndNamlEdnR0Ly9Zd1dsRlRDbWF0ZVozU0p2ODVXYUZXOFNvaE5vbUZDbGJ4emZFL0lnaFVpeGQxaUJVck9HUkZLMjBNY1FSSDFzNHlPRmZPV2loSjE1R2NibXBHTHlhRUpkaFcwNW1wbUtMMFFOTm9GV245R1dEdUV5QklwdWl3SXBSamtmZWkvL3VrZUtkbXZ6V1JQZkhhSklIYVBteVZlMHdhZTdLeUM4T0E1QTh0aTR5THZEUE5iM2h5Y2FreEFHVitFTVhZbDVxbk51QjFMN2Fadk55VWNUL3MiLCJtYWMiOiJiZGQ3NTc3YmJlNzc5ZWFmMGI1YjNkMTQ1YzI2ZTJjMDk3ODlhYWU1YjJjNjkxNWI4OWU0Nzg5Y2Y1MTc4MmU4IiwidGFnIjoiIn0%3D; expires=Tue, 17-Jun-2025 15:53:07 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-719572341\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-647306670 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>16</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"30 characters\">http://localhost/hrm-dashboard</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-647306670\", {\"maxDepth\":0})</script>\n"}}