{"__meta": {"id": "Xf3f3220319a445c851eced83ea995e7f", "datetime": "2025-06-17 13:48:50", "utime": **********.658627, "method": "GET", "uri": "/customer/check/warehouse?customer_id=10&warehouse_id=8", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.07972, "end": **********.658648, "duration": 0.5789279937744141, "duration_str": "579ms", "measures": [{"label": "Booting", "start": **********.07972, "relative_start": 0, "end": **********.570042, "relative_end": **********.570042, "duration": 0.4903218746185303, "duration_str": "490ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.570056, "relative_start": 0.49033594131469727, "end": **********.658651, "relative_end": 3.0994415283203125e-06, "duration": 0.08859515190124512, "duration_str": "88.6ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 46221072, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET customer/check/warehouse", "middleware": "web, verified, auth, XSS, revalidate", "controller": "App\\Http\\Controllers\\CustomerController@checkWarehouse", "namespace": null, "prefix": "", "where": [], "as": "customer.check.warehouse", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FHttp%2FControllers%2FCustomerController.php&line=383\" onclick=\"\">app/Http/Controllers/CustomerController.php:383-397</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.018420000000000002, "accumulated_duration_str": "18.42ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 16 limit 1", "type": "query", "params": [], "bindings": ["16"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.610785, "duration": 0.016460000000000002, "duration_str": "16.46ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 89.359}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.6412811, "duration": 0.00082, "duration_str": "820μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 89.359, "width_percent": 4.452}, {"sql": "select * from `customers` where `customers`.`id` = '10' limit 1", "type": "query", "params": [], "bindings": ["10"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/CustomerController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Http\\Controllers\\CustomerController.php", "line": 388}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.6469839, "duration": 0.00114, "duration_str": "1.14ms", "memory": 0, "memory_str": null, "filename": "CustomerController.php:388", "source": "app/Http/Controllers/CustomerController.php:388", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FHttp%2FControllers%2FCustomerController.php&line=388", "ajax": false, "filename": "CustomerController.php", "line": "388"}, "connection": "kdmkjkqknb", "start_percent": 93.811, "width_percent": 6.189}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\Customer": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FModels%2FCustomer.php&line=1", "ajax": false, "filename": "Customer.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "16", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]", "pos": "array:1 [\n  2044 => array:9 [\n    \"name\" => \"تجربية\"\n    \"quantity\" => 1\n    \"price\" => \"10.00\"\n    \"id\" => \"2044\"\n    \"tax\" => 0\n    \"subtotal\" => 10.0\n    \"originalquantity\" => 1\n    \"product_tax\" => \"-\"\n    \"product_tax_id\" => 0\n  ]\n]"}, "request": {"path_info": "/customer/check/warehouse", "status_code": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>customer_id</span>\" => \"<span class=sf-dump-str title=\"2 characters\">10</span>\"\n  \"<span class=sf-dump-key>warehouse_id</span>\" => \"<span class=sf-dump-str>8</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-505564105 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1936 characters\">_clck=2gyfy%7C2%7Cfwu%7C0%7C1994; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clsk=1ti4td2%7C1750167218299%7C2%7C1%7Cl.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IlNUR1d0YWF1d2VyNFVCM1dVUmpzdFE9PSIsInZhbHVlIjoiQnFZazBjZzhMMFpaVE9DU1prdW1GNFBFeWlrT3pVR3k2MEJQZHEwclFvb0xjenRjOFd5Q2p1OCt2d1N5Mkc2WlcxaHZqU0NKd0J2VkFQRmU0NGxTZU9YMXZ3aDRsSTBNSUh2SGgxS2p3cXIrSjF2UTdpbkV3VWMyRDdqUFNkTHpFWHd3ZDhXV2lUcGNvY3BYL1VKbk5QRVJLSklxL0VUVWg3U3FrSTVXdW9jcHBDeUNkVkY0TkE1bzlrWlJ5VVhQNDRXVTBDcG1Qc0sxNEU4TjJyMzBubVFnMXpjN1VFTFBxR0tNMVIvMlErVWpOQkt4WVlGUUdtT3d3aGhrZ2ZadnFuaUw5dEZnc3U5aWNJM0Q0OS9zbVBHc0swb25YMEZLRm9sOTNPUnhBSE14ajRDOFdnWkpqZ1A3UzlwbStPZXdUTk9vSTBiUk5IcFpCSThuVzVINnNGR2VZUnpUa0t0OUdTbEE0WW9lNmxpcUN4MFIydWJCc01ldFhzbW1pbW9lQ3ozZUFFS01nMUtrcUFJYStQYmhrY1BXaDYvVTNQdFRPbjJjSlJTdDhzMHJNU1RFb2FiekQ2cDdoazZjczJTRzdIenNuK25ESjMxR0E1MHk3blpxamFOOGtkRUU5OFcxZkVUT1ZsQjhieWRzY1RUN2tlZXpCUXhLR3FmeFpYMnYiLCJtYWMiOiI0MGQxNmE5NDVhNDk1NzQ2MmM2OWY0OTBkMTMzODRlODhlMzllNDAwZTA3ZDY3MTFhZGEwNDgxMDljNmNhODg5IiwidGFnIjoiIn0%3D; test_session=eyJpdiI6Ikxrd0Y2Q0N4SEZyMzQzYzV0UkIxK0E9PSIsInZhbHVlIjoiOEF2MlZBU1JGOUNBWktLUzBYT3g5d3hiTEJKRURzMkFmQUJacm9lam13aStTbFhDWklERC9SZ1dNZ1FVcjZwdURaS0VXL0FadlFhRi9teWlhcm5abDl3SkZBLzlMRHB2MlhraWliVVI2c1lzbTlaaWFQUSt2MVBCczM0YUZGNXdMTzZ3M01GZ2tZNGNWQzdxeHhjWVhFbkJVaHpCby9vSFJXaWpGZUhGVmo1NUM2bVVoK1VuQVZtVkZIWnI5QnZUaHBTbFo5dkZYZ2VRUlcxMTRVdVlxSFV1ODJBWk53VHg0czg2TThJcitpUHlaVlNLdXdkUFJBSDNSOGZGMi9GLzZHVm96V3ZrMFFnMmhENkJrYWE0eVk0b1hta3hzMGNIQUJpb1lKbDliWHJheWdEYldhZWpuZks3MmdYNFBaNm5ydzhYWjg3b2hMeEVYNXV3ZHE5OSs2L0NlZGpPMWIyOUhxRUZsUTFXakx1eUk1amtBYTBKTlJ3RUtSUmNETHczU09LdDhXZ3JLMEdqcGx6SHNRWVYxZHZJc3A3Z0ZvcWNPWHF5NUJGZVBvQVQ4NG85QTBWcDRqN0c3MUNnVlVJK2kzNGFOSUFnME4waGVqa0h5bVFSbXRrdEsvZEFCTndHYnRkdWxmTk50djdrbWt2SDVNODNDQUNKd091bm9UVk0iLCJtYWMiOiIyZWNiNTJjMDc3OGE1ZWNkMmY5NDYyZGU3MWZjZWUzOGRhNjRiN2IwYzYwNjJmMzM0MDU1MzE4NzUxYjQ0NDQzIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-505564105\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1104806736 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">VloaUQCBjpDKrel7nHeAzB1sTLdTMwteSos1MmAN</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1104806736\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-207475317 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 17 Jun 2025 13:48:50 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-methods</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">POST, GET, OPTIONS, PUT, DELETE</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-headers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"80 characters\">Content-Type, Accept, Authorization, X-Requested-With, Application, X-CSRF-TOKEN</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IkVNdHYzaWFrelpKZk01b1JTNlVnbVE9PSIsInZhbHVlIjoiV0Z3LzlQN29qbWpTazZsdU50Q3lCUFlpZjFSV2ZiZW13N0lPNHIxd3pPYkZxZGx2ZjdpWjVKcWExVmFpcXNLT2hMVTB0bXRGQjVCSVlpbFhmWDcrM3lDaEZINGExMi9lamtvT1JoTndLbGd4d1lyOHJvZmQzREpHTzlFbUtkWWdqeWNrSm0rZmVoU2tieG9WM2o4VFRrTmE1b0RtN3NwS0dPUTJOVk9veFJBWUhMd21lSVczSnFXVURxOEczV1IzU2lVNVJJd1JGZzRhRmlySGhKaVNSYW5QaE90Y3FVUXNFMFZYQWdLSW9xcmxZRUpzWEhBYVZoUUEzLzdBaEhqcXJZQXNOclgyQ1ZqMVduSTdpeWQ4a3pYSm9OYzkxWGdmclhFc2UrdWlXSEZMTlRORjJudkRDbUZKd29mdFhRNzcydFQzbVVaWXFNdUxnbW9jbDlCWVY1ck1zeXk1aFIwZytoVjV3M0t4aXUzaURJajVHRUlFNFhNMkRhQ0dtU0QzZEJCU0Q2bU82WkVxOE80UW1MUldKSG8wbVJZMGljbXJVVC9NMVNydzR3QkgweDdVVXFRL2tCNitjdDEzaHp0dEZ1MUtFRUVUSVRNYzJ2TmRkdENmSFdrL2Fmd2lEc3dGVThibUdQNmNlRFdXRnBqdzBmRDJhM0g2b0xlQTI1SWciLCJtYWMiOiIyNDYzNzc0OWY1MDRiM2QzNmQ0ZjMwYmFkYjI0MmY0YjlmMjE0ZTY1YTE0NDgwN2RlOTNiZDVkOGNlMjJhMjg0IiwidGFnIjoiIn0%3D; expires=Tue, 17 Jun 2025 15:48:50 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6IjNEWU1WT1BwRG81QUU2blBNaUhLY0E9PSIsInZhbHVlIjoieUFWdGRrNUFoamp4L0FVYlFmektvVDk2QmZsU0IrRTFoUUUzSEpIK2t1dDN4bXFwa3h6NjRXNXFJRi8waE5tczFXL1BzeVBiTnUzZkVJeXViMzI3QkZFdTJZZUgrTHlHU1B2VHlNcGd6V0JPUHhSR3BOOXA4VTF1M0lsWjIzSUdqdzhTWFdUdkJIQ0UzZ2tGMVgrY3Bpelo1NGpBYVFCeHBPK2VZamF4YS9KemFuOHRZRnA1b3NQM093MnpMMm1zTDR1NEhxMm9VaW9ST3R4VUN4SjMxUFhZeVZ3WVEwNTEvdUxObzB1VXdvU1VLeVpKc3AvNTV6QW1NNnhVY2F0UHJOOTNuVnJ0V3dHeFp6bGU4WWZXU3d0bGFQbzFyVkRlZXArV0o0WFowRjAyYVVDZERUemxaZ0FKTFpremIwemZHaE5yV1doVHovOGNmNmk4QkFjTXBNZG5zZmtZRm1JWnhmODdDdEFvbDA3NVJubUFiMDNiOEdDVU1lemNNcmluTjRRZlRYeFh3UXVuaU55R1UvSndyUGhrVEM0Rmk0NUs4Y0d1L1BUOUppRkdFSVdNbHlxWi9xdmpIOU00c3BtMDg3ajJXT2Q3OWk4OTNyVDNrS2FaU1JrTlNtVDZBWThlc0YrcENJZlBJb0Y2YlVFUVljd0x2eFdPSWdIZnBJYlAiLCJtYWMiOiI1NmQxNjk2Nzk5ZTM3ZjNjMmYyOTc3N2Y5OTI0MTQ5ZGJkZTQxOWQwOTUwYjBjMjMyOWQ4OTIyOGJlMzEzOTNjIiwidGFnIjoiIn0%3D; expires=Tue, 17 Jun 2025 15:48:50 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IkVNdHYzaWFrelpKZk01b1JTNlVnbVE9PSIsInZhbHVlIjoiV0Z3LzlQN29qbWpTazZsdU50Q3lCUFlpZjFSV2ZiZW13N0lPNHIxd3pPYkZxZGx2ZjdpWjVKcWExVmFpcXNLT2hMVTB0bXRGQjVCSVlpbFhmWDcrM3lDaEZINGExMi9lamtvT1JoTndLbGd4d1lyOHJvZmQzREpHTzlFbUtkWWdqeWNrSm0rZmVoU2tieG9WM2o4VFRrTmE1b0RtN3NwS0dPUTJOVk9veFJBWUhMd21lSVczSnFXVURxOEczV1IzU2lVNVJJd1JGZzRhRmlySGhKaVNSYW5QaE90Y3FVUXNFMFZYQWdLSW9xcmxZRUpzWEhBYVZoUUEzLzdBaEhqcXJZQXNOclgyQ1ZqMVduSTdpeWQ4a3pYSm9OYzkxWGdmclhFc2UrdWlXSEZMTlRORjJudkRDbUZKd29mdFhRNzcydFQzbVVaWXFNdUxnbW9jbDlCWVY1ck1zeXk1aFIwZytoVjV3M0t4aXUzaURJajVHRUlFNFhNMkRhQ0dtU0QzZEJCU0Q2bU82WkVxOE80UW1MUldKSG8wbVJZMGljbXJVVC9NMVNydzR3QkgweDdVVXFRL2tCNitjdDEzaHp0dEZ1MUtFRUVUSVRNYzJ2TmRkdENmSFdrL2Fmd2lEc3dGVThibUdQNmNlRFdXRnBqdzBmRDJhM0g2b0xlQTI1SWciLCJtYWMiOiIyNDYzNzc0OWY1MDRiM2QzNmQ0ZjMwYmFkYjI0MmY0YjlmMjE0ZTY1YTE0NDgwN2RlOTNiZDVkOGNlMjJhMjg0IiwidGFnIjoiIn0%3D; expires=Tue, 17-Jun-2025 15:48:50 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6IjNEWU1WT1BwRG81QUU2blBNaUhLY0E9PSIsInZhbHVlIjoieUFWdGRrNUFoamp4L0FVYlFmektvVDk2QmZsU0IrRTFoUUUzSEpIK2t1dDN4bXFwa3h6NjRXNXFJRi8waE5tczFXL1BzeVBiTnUzZkVJeXViMzI3QkZFdTJZZUgrTHlHU1B2VHlNcGd6V0JPUHhSR3BOOXA4VTF1M0lsWjIzSUdqdzhTWFdUdkJIQ0UzZ2tGMVgrY3Bpelo1NGpBYVFCeHBPK2VZamF4YS9KemFuOHRZRnA1b3NQM093MnpMMm1zTDR1NEhxMm9VaW9ST3R4VUN4SjMxUFhZeVZ3WVEwNTEvdUxObzB1VXdvU1VLeVpKc3AvNTV6QW1NNnhVY2F0UHJOOTNuVnJ0V3dHeFp6bGU4WWZXU3d0bGFQbzFyVkRlZXArV0o0WFowRjAyYVVDZERUemxaZ0FKTFpremIwemZHaE5yV1doVHovOGNmNmk4QkFjTXBNZG5zZmtZRm1JWnhmODdDdEFvbDA3NVJubUFiMDNiOEdDVU1lemNNcmluTjRRZlRYeFh3UXVuaU55R1UvSndyUGhrVEM0Rmk0NUs4Y0d1L1BUOUppRkdFSVdNbHlxWi9xdmpIOU00c3BtMDg3ajJXT2Q3OWk4OTNyVDNrS2FaU1JrTlNtVDZBWThlc0YrcENJZlBJb0Y2YlVFUVljd0x2eFdPSWdIZnBJYlAiLCJtYWMiOiI1NmQxNjk2Nzk5ZTM3ZjNjMmYyOTc3N2Y5OTI0MTQ5ZGJkZTQxOWQwOTUwYjBjMjMyOWQ4OTIyOGJlMzEzOTNjIiwidGFnIjoiIn0%3D; expires=Tue, 17-Jun-2025 15:48:50 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-207475317\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>16</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pos</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-key>2044</span> => <span class=sf-dump-note>array:9</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"6 characters\">&#1578;&#1580;&#1585;&#1576;&#1610;&#1577;</span>\"\n      \"<span class=sf-dump-key>quantity</span>\" => <span class=sf-dump-num>1</span>\n      \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"5 characters\">10.00</span>\"\n      \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"4 characters\">2044</span>\"\n      \"<span class=sf-dump-key>tax</span>\" => <span class=sf-dump-num>0</span>\n      \"<span class=sf-dump-key>subtotal</span>\" => <span class=sf-dump-num>10.0</span>\n      \"<span class=sf-dump-key>originalquantity</span>\" => <span class=sf-dump-num>1</span>\n      \"<span class=sf-dump-key>product_tax</span>\" => \"<span class=sf-dump-str>-</span>\"\n      \"<span class=sf-dump-key>product_tax_id</span>\" => <span class=sf-dump-num>0</span>\n    </samp>]\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n"}}