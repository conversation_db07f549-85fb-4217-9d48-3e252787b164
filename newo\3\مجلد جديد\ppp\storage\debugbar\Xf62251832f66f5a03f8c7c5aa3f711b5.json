{"__meta": {"id": "Xf62251832f66f5a03f8c7c5aa3f711b5", "datetime": "2025-06-17 13:53:09", "utime": **********.575073, "method": "GET", "uri": "/product-categories", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1750168388.941187, "end": **********.575106, "duration": 0.6339190006256104, "duration_str": "634ms", "measures": [{"label": "Booting", "start": 1750168388.941187, "relative_start": 0, "end": **********.430601, "relative_end": **********.430601, "duration": 0.4894139766693115, "duration_str": "489ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.430613, "relative_start": 0.4894261360168457, "end": **********.575109, "relative_end": 3.0994415283203125e-06, "duration": 0.14449596405029297, "duration_str": "144ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 49310360, "peak_usage_str": "47MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET product-categories", "middleware": "web, verified, auth, XSS, category.access", "controller": "App\\Http\\Controllers\\ProductServiceCategoryController@getProductCategories", "namespace": null, "prefix": "", "where": [], "as": "product.categories", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FHttp%2FControllers%2FProductServiceCategoryController.php&line=203\" onclick=\"\">app/Http/Controllers/ProductServiceCategoryController.php:203-229</a>"}, "queries": {"nb_statements": 6, "nb_failed_statements": 0, "accumulated_duration": 0.02522, "accumulated_duration_str": "25.22ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 16 limit 1", "type": "query", "params": [], "bindings": ["16"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.476186, "duration": 0.01436, "duration_str": "14.36ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 56.939}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.50248, "duration": 0.00123, "duration_str": "1.23ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 56.939, "width_percent": 4.877}, {"sql": "select `permissions`.*, `model_has_permissions`.`model_id` as `pivot_model_id`, `model_has_permissions`.`permission_id` as `pivot_permission_id`, `model_has_permissions`.`model_type` as `pivot_model_type` from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `model_has_permissions`.`model_id` = 16 and `model_has_permissions`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["16", "App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 326}, {"index": 21, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 566}], "start": **********.537792, "duration": 0.0011799999999999998, "duration_str": "1.18ms", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:326", "source": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php:326", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=326", "ajax": false, "filename": "HasPermissions.php", "line": "326"}, "connection": "kdmkjkqknb", "start_percent": 61.816, "width_percent": 4.679}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (16) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 312}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 26, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}], "start": **********.5418332, "duration": 0.0011899999999999999, "duration_str": "1.19ms", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 66.495, "width_percent": 4.718}, {"sql": "select `product_service_categories`.*, COUNT(pu.category_id) product_services from `product_service_categories` left join `product_services` as `pu` on `product_service_categories`.`id` = `pu`.`category_id` where `product_service_categories`.`created_by` = 15 and `product_service_categories`.`type` = 'product & service' group by `product_service_categories`.`id` order by `product_service_categories`.`id` desc", "type": "query", "params": [], "bindings": ["15", "product &amp; service"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/ProductServiceCategory.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Models\\ProductServiceCategory.php", "line": 116}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/ProductServiceCategoryController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Http\\Controllers\\ProductServiceCategoryController.php", "line": 206}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.55054, "duration": 0.00463, "duration_str": "4.63ms", "memory": 0, "memory_str": null, "filename": "ProductServiceCategory.php:116", "source": "app/Models/ProductServiceCategory.php:116", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FModels%2FProductServiceCategory.php&line=116", "ajax": false, "filename": "ProductServiceCategory.php", "line": "116"}, "connection": "kdmkjkqknb", "start_percent": 71.213, "width_percent": 18.358}, {"sql": "select count(*) as aggregate from `product_services` left join `product_service_categories` as `c` on `c`.`id` = `product_services`.`category_id` where `product_services`.`type` = 'product' and `product_services`.`created_by` = 15", "type": "query", "params": [], "bindings": ["product", "15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/ProductServiceCategoryController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Http\\Controllers\\ProductServiceCategoryController.php", "line": 209}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.559933, "duration": 0.00263, "duration_str": "2.63ms", "memory": 0, "memory_str": null, "filename": "ProductServiceCategoryController.php:209", "source": "app/Http/Controllers/ProductServiceCategoryController.php:209", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FHttp%2FControllers%2FProductServiceCategoryController.php&line=209", "ajax": false, "filename": "ProductServiceCategoryController.php", "line": "209"}, "connection": "kdmkjkqknb", "start_percent": 89.572, "width_percent": 10.428}]}, "models": {"data": {"App\\Models\\ProductServiceCategory": {"value": 26, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FModels%2FProductServiceCategory.php&line=1", "ajax": false, "filename": "ProductServiceCategory.php", "line": "?"}}, "App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 28, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 1, "messages": [{"message": "[ability => create purchase, result => true, user => 16, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1861748874 data-indent-pad=\"  \"><span class=sf-dump-note>create purchase</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"15 characters\">create purchase</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>16</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1861748874\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.548815, "xdebug_link": null}]}, "session": {"_token": "PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "16", "_previous": "array:1 [\n  \"url\" => \"http://localhost/hrm-dashboard\"\n]"}, "request": {"path_info": "/product-categories", "status_code": "<pre class=sf-dump id=sf-dump-54563824 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-54563824\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-1888604627 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1888604627\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-965409885 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-965409885\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-788794919 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1936 characters\">_clck=2gyfy%7C2%7Cfwu%7C0%7C1994; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clsk=1ti4td2%7C1750168386913%7C7%7C1%7Cl.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IjNLMjhnMUlNUU5qZFVUMUlLUFQ0RUE9PSIsInZhbHVlIjoiS2VCbC9XV2RZUzdlWFhvOGdVM2lmUEprZWRYYThpQ3pvMEdsTkxyc3RraVBTRXg4cEFGa1YzTTNnWXpHM0ZUZ3JCUCtLenJzRlZFVmtkUnpGZkVjRWZnMG9VbUxtcUU0Skl4TjZwamVlNmNmRGhvSk4wdXB0V2Fyc01ycFV3RThqbDhuQVVreDlPbHo4SDFZZzFEajJ5bW1wNXRxVis0VWtWSFZwQXVtbEJxRk5nSnFyQ01ONmRTK2JRcTNINHBEU0QwYmpWSmtIR1VJZGl3OWtzWHVOeEwzbktpNVVDNG9HSm5FSFN5RWJVd2ErdmFYM1E1SkVWMHF6T09uK0prd20rMGREWlRNWmtzNWVaOE9ZZWFyMzRhdTlhZGlDM2JuMTB0K0FaZmFIVlNsazcxRDQ4dVVMRVVkVnE1dDNyQncrOUszZm1wS1ZWZnR5Wkx5RTlaTHhqRkxmUU1MU05ON2NLdTNSdkZudWZiRWhCSGp6WGhQUGhGQTRsUWIzRWZWdE9zcTZpYmxoSFdYbXVHSXBiODBkcjBCdUVXZDdwZEVKRlAxbW1vZmpSSy9ZejBJVkhGMXhMd0c5Rm44SGlZTTFNZkJNVDErOFNjZXdqbXpITndUNFBScGNXY2NrQllSRHpWRjRBQXoxNkV6UlZEckp1aEM2QUQxbFFHR2NxbWMiLCJtYWMiOiIxZDhlN2IzMWI5NGM5YjE1ZjZjMjcwNGNlY2MyZDNlYTcyNTk2ZTk4MTEzMGI4N2ZjZGIyMmNjNjI3OWZiMTI2IiwidGFnIjoiIn0%3D; test_session=eyJpdiI6IkQ3NWR6cmtoTVhtTFBDNVBDSzIrZUE9PSIsInZhbHVlIjoiRjdNNlNzNmc1NEtNSG40Z3V0SGRTV2pPYWhCOVFZZHFISTBlenpKRllZT0c5WGJHNVMzNUdzMCtxd3l0NDZKQnF4bEI2Nms5VzAvdVZuK1ZhdnJra3pWKzJvcUx1UmFoRWlXaVp3UTRBWDdleFIrSXFmRGZrbzBhYktJejQ2TFJjeVNyeFcrQWE5TWV1QkRtT3c3TU9Sdk5lQWdUc1JJMzBBemxLUU5TQThiZU1uZjR2NWpZVWMvSGlmZ3FRZkt4SGRTNmIvTjU5RFZBREtBOWFGQzBOb0NvU0d5WHNKTzhPY2RjVkgrQ1ZNOVpUMDRjenVBMW5lOEhyd1B1cVlqTG9ScjRQL2xROWt1M2d3RmhSd2VTL3FqWnZkSWhYaXRlNTdGU2NjVFJqZHBmU3dhVmNjUEtJL2ZzWmlIeTNiamRucWF5MGtCbm93MzI2c1MvS3hTbXlxcmhLbEpEcjdjZ2xSYWpjSGk4dC9FYU9aUFExZDJJRWVWNnhQRDZHSGc3REdLdmhSOSsyU2NQYVEydlNGRlMxSzdHKytTNVQyZmxWYS9uMm94eXZLc2t2SmxvdVNFdjZSK3BRRFZOa0NNMEJ0RTFSMzFqRUZXbFN3amd6ck50TEkwSzFqT0FyeTJ2L3M4cEk2eDAwNmNrdEd0WUtwREZQcDhzUk1KUFNOaFAiLCJtYWMiOiJmZTQ1YTg5MjE2ZjJjMjA0Y2Y5YTIxYTNmZjM4MjIyOGY4OTRmYzdhMmVjMzk0MzYzY2VlYjIyZDdkOTQ3NDc2IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-788794919\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1674416211 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">VloaUQCBjpDKrel7nHeAzB1sTLdTMwteSos1MmAN</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1674416211\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1285140661 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 17 Jun 2025 13:53:09 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6ImoxeTVtcmtSTVlRdWs2UkhERnYzd3c9PSIsInZhbHVlIjoiQWcxQkdZYWN3WHRXd25tSjNodUxxSnRsM2RPK0xpcEt2dlFETURFdWk1dGNFOElZbkpNU3hNME5QUDBBakcwUm1vY0wzWHJNVWxoR0JLY0hPWFFtbmNiT2RXem9lbVdmSnA5Q2dLQy8rTnpEaHNRYndFYTBVam1hNFZrL3hZcEhvZnM1U3BvUHBhcVptYlJRTXZ4N3BOVDNRN1luYVA4bmx4RnZDdkdEUXlRK0RhM2tLVnIrbFdKYjBUUjRDdG00ZWtEODNDRE84a2VrMU9lQUFJSUtEeXdobzFCSjV6ZDhRZUVPUjUwRGo5eUxOVDBOTk1LUUI2dWVwUDhjRTlXLzJ5bmExY0ZwZVdlcW5Qem5tT0tJKzZVekd2MitBWjBDazd4b2xLRmF3akp6Um03ZUVHbzdyUytRS0kvd0V1Q0lXMkVCc1RIb1dKalpHNGV5UFVqSlAvZGZTTWVnZUt6VHg5RlNjdTlyMGh6VjI4bFBqZ1p6MTZkQzgxWlpVUTNWb1pPQ2U3NjR1V1lsaEg1cnExY3VpeGRsamFBKzZDcWFkMTBPcnRXMHRHVE1IOHdPaXBTRTcvWFNjVnZSRzFHRlN1a0VvWkdQZ1NUbkkyRnVtTlU5bXB3TnA4SHlGRE1MYXRZbkJhRUhKQU1aTmc4VnZrYVJGN1ExcnFjNm5maTYiLCJtYWMiOiJlYmEyOTAyNDE3M2FlMTFhN2RkZjMxNTY5ZjQxNmIxNGUwODViODEwNWI2OTRhOWE1MGQyY2MwNGI0OTk3OTk0IiwidGFnIjoiIn0%3D; expires=Tue, 17 Jun 2025 15:53:09 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6InNZRHZYTHNtZ0wwN3VaL1NyZXg4MEE9PSIsInZhbHVlIjoic2J0MWpNd3FmcDFiWTNmd2VTZXBaaC84UHJSS1BRTDBsWmk1bjcxRVBGLzZPS0Q5eGhYYlBjU0ZIQlkwelUxNXIzOVNUZ0lrVkcwL1lyd0ZRRnRBQkQvczBWcnNpeThCRU1zdlZLUG1OYmlWZjl2aTgwM3h4b0tNSUZhdkN6d2NrbmkrcURKbVRpRUgvbFNmOEVCTkttaHdybXlDc2d0c0xCODFlR2Z6TVhxSHhnaHNJc1ZFQnl6dEJVaG44eHdaWklMNUFZenVZdnBMd1ptMGxFT0VHUWI2QmlHN0sxL09aVlJKQ28zTWtQMFcxeDlmZXFnV0Noak9ITUNQR1VJRzV0aXd5TXdlREx4MXJXUXlHSml0YU54SEFvT1R0TE9YQmU5aDZPZGE4TUNIYURUemRzem9lbU12S1NvMnlWSEdFaXdiMlRKeEhTOERBVWJ0cWlOdlR6MHIwVHBWTURuamsvWFd2SFFLK3U2VU5TVFFFa2hhTVNJNHJGenZpS09FWEc4NmxVODFQOUVBaEQxU216V0NuOXI0ZmluTEZVeDVNeVRnNTZDRDYrc0xqdnM1WVpwZno2UjJxZVRTSWV4T25FWU10ZXdxY1M3eEoySWRVYjJPNVhHNTkrRFVLVUxRa0ZDeENvcTRwRzVtd081WVF6NnF1Tm5yeklaNTNuQUYiLCJtYWMiOiI0YWRmYzNmMzk0MmJlNTkyZGU2YzQ1YzhlMTkyZGI5ZWM5MTZhOTlmOTQxY2QxNzE5MTA4OGRmNWRkNTQ0MTUzIiwidGFnIjoiIn0%3D; expires=Tue, 17 Jun 2025 15:53:09 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6ImoxeTVtcmtSTVlRdWs2UkhERnYzd3c9PSIsInZhbHVlIjoiQWcxQkdZYWN3WHRXd25tSjNodUxxSnRsM2RPK0xpcEt2dlFETURFdWk1dGNFOElZbkpNU3hNME5QUDBBakcwUm1vY0wzWHJNVWxoR0JLY0hPWFFtbmNiT2RXem9lbVdmSnA5Q2dLQy8rTnpEaHNRYndFYTBVam1hNFZrL3hZcEhvZnM1U3BvUHBhcVptYlJRTXZ4N3BOVDNRN1luYVA4bmx4RnZDdkdEUXlRK0RhM2tLVnIrbFdKYjBUUjRDdG00ZWtEODNDRE84a2VrMU9lQUFJSUtEeXdobzFCSjV6ZDhRZUVPUjUwRGo5eUxOVDBOTk1LUUI2dWVwUDhjRTlXLzJ5bmExY0ZwZVdlcW5Qem5tT0tJKzZVekd2MitBWjBDazd4b2xLRmF3akp6Um03ZUVHbzdyUytRS0kvd0V1Q0lXMkVCc1RIb1dKalpHNGV5UFVqSlAvZGZTTWVnZUt6VHg5RlNjdTlyMGh6VjI4bFBqZ1p6MTZkQzgxWlpVUTNWb1pPQ2U3NjR1V1lsaEg1cnExY3VpeGRsamFBKzZDcWFkMTBPcnRXMHRHVE1IOHdPaXBTRTcvWFNjVnZSRzFHRlN1a0VvWkdQZ1NUbkkyRnVtTlU5bXB3TnA4SHlGRE1MYXRZbkJhRUhKQU1aTmc4VnZrYVJGN1ExcnFjNm5maTYiLCJtYWMiOiJlYmEyOTAyNDE3M2FlMTFhN2RkZjMxNTY5ZjQxNmIxNGUwODViODEwNWI2OTRhOWE1MGQyY2MwNGI0OTk3OTk0IiwidGFnIjoiIn0%3D; expires=Tue, 17-Jun-2025 15:53:09 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6InNZRHZYTHNtZ0wwN3VaL1NyZXg4MEE9PSIsInZhbHVlIjoic2J0MWpNd3FmcDFiWTNmd2VTZXBaaC84UHJSS1BRTDBsWmk1bjcxRVBGLzZPS0Q5eGhYYlBjU0ZIQlkwelUxNXIzOVNUZ0lrVkcwL1lyd0ZRRnRBQkQvczBWcnNpeThCRU1zdlZLUG1OYmlWZjl2aTgwM3h4b0tNSUZhdkN6d2NrbmkrcURKbVRpRUgvbFNmOEVCTkttaHdybXlDc2d0c0xCODFlR2Z6TVhxSHhnaHNJc1ZFQnl6dEJVaG44eHdaWklMNUFZenVZdnBMd1ptMGxFT0VHUWI2QmlHN0sxL09aVlJKQ28zTWtQMFcxeDlmZXFnV0Noak9ITUNQR1VJRzV0aXd5TXdlREx4MXJXUXlHSml0YU54SEFvT1R0TE9YQmU5aDZPZGE4TUNIYURUemRzem9lbU12S1NvMnlWSEdFaXdiMlRKeEhTOERBVWJ0cWlOdlR6MHIwVHBWTURuamsvWFd2SFFLK3U2VU5TVFFFa2hhTVNJNHJGenZpS09FWEc4NmxVODFQOUVBaEQxU216V0NuOXI0ZmluTEZVeDVNeVRnNTZDRDYrc0xqdnM1WVpwZno2UjJxZVRTSWV4T25FWU10ZXdxY1M3eEoySWRVYjJPNVhHNTkrRFVLVUxRa0ZDeENvcTRwRzVtd081WVF6NnF1Tm5yeklaNTNuQUYiLCJtYWMiOiI0YWRmYzNmMzk0MmJlNTkyZGU2YzQ1YzhlMTkyZGI5ZWM5MTZhOTlmOTQxY2QxNzE5MTA4OGRmNWRkNTQ0MTUzIiwidGFnIjoiIn0%3D; expires=Tue, 17-Jun-2025 15:53:09 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1285140661\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-702986658 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>16</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"30 characters\">http://localhost/hrm-dashboard</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-702986658\", {\"maxDepth\":0})</script>\n"}}