{"__meta": {"id": "Xf6292c0ae9d71357560b8af73113d91d", "datetime": "2025-06-17 13:53:20", "utime": **********.0523, "method": "GET", "uri": "/customer/check/delivery?customer_id=10", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1750168399.480919, "end": **********.052325, "duration": 0.5714061260223389, "duration_str": "571ms", "measures": [{"label": "Booting", "start": 1750168399.480919, "relative_start": 0, "end": 1750168399.976125, "relative_end": 1750168399.976125, "duration": 0.4952061176300049, "duration_str": "495ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": 1750168399.97614, "relative_start": 0.4952211380004883, "end": **********.052327, "relative_end": 1.9073486328125e-06, "duration": 0.0761868953704834, "duration_str": "76.19ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 44963584, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET customer/check/delivery", "middleware": "web, verified", "controller": "App\\Http\\Controllers\\CustomerController@checkDelivery", "namespace": null, "prefix": "", "where": [], "as": "customer.check.delivery", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FHttp%2FControllers%2FCustomerController.php&line=365\" onclick=\"\">app/Http/Controllers/CustomerController.php:365-378</a>"}, "queries": {"nb_statements": 2, "nb_failed_statements": 0, "accumulated_duration": 0.01421, "accumulated_duration_str": "14.21ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 16 limit 1", "type": "query", "params": [], "bindings": ["16"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/AuthManager.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\AuthManager.php", "line": 57}, {"index": 23, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 33}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.0212219, "duration": 0.013560000000000001, "duration_str": "13.56ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 95.426}, {"sql": "select * from `customers` where `customers`.`id` = '10' limit 1", "type": "query", "params": [], "bindings": ["10"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/CustomerController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Http\\Controllers\\CustomerController.php", "line": 368}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.0413592, "duration": 0.00065, "duration_str": "650μs", "memory": 0, "memory_str": null, "filename": "CustomerController.php:368", "source": "app/Http/Controllers/CustomerController.php:368", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FHttp%2FControllers%2FCustomerController.php&line=368", "ajax": false, "filename": "CustomerController.php", "line": "368"}, "connection": "kdmkjkqknb", "start_percent": 95.426, "width_percent": 4.574}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\Customer": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FModels%2FCustomer.php&line=1", "ajax": false, "filename": "Customer.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "16", "_previous": "array:1 [\n  \"url\" => \"http://localhost/hrm-dashboard\"\n]", "pos": "array:1 [\n  2044 => array:9 [\n    \"name\" => \"تجربية\"\n    \"quantity\" => 1\n    \"price\" => \"10.00\"\n    \"id\" => \"2044\"\n    \"tax\" => 0\n    \"subtotal\" => 10.0\n    \"originalquantity\" => 1\n    \"product_tax\" => \"-\"\n    \"product_tax_id\" => 0\n  ]\n]"}, "request": {"path_info": "/customer/check/delivery", "status_code": "<pre class=sf-dump id=sf-dump-1758726771 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1758726771\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-1689855697 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>customer_id</span>\" => \"<span class=sf-dump-str title=\"2 characters\">10</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1689855697\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1360323523 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1936 characters\">_clck=2gyfy%7C2%7Cfwu%7C0%7C1994; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clsk=1ti4td2%7C1750168386913%7C7%7C1%7Cl.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6Ink3NVhHUXJXMGJ2Sm5aZG9GcU1Nc3c9PSIsInZhbHVlIjoieTgyRlRoeHE3ZDlUbGw2VWZ2Tkx2VlNqaGF1Z3hmUnBza2Q5Wm1NclV4ZDZZaFZUbXB3M1NubXE5VHc5QlFLRE1aVENBU3ZmNHVwV2dGenZqZy9KQmNHVzhrZE9DTkJNSklTa3p4SzV5WlhQR0U5SUQzWERXcExjakVjKzhWMFdFNWdTSm9QdnhSMEtpRmk2OEhTZ09kWmdWQVZ6WlRlZXQ3VnRZSTFneXlOWEUwRFRxZmtxby9DS3czaXMzQlQ0dmVqT3JzNFF5VUhTNkRSQ3FLSlA3T0w5SjVNTmtxTzFkMXNVNzJpTHRDSk42NFhwNXJNWE1IYzFZbkdBZ1l0Q1M2aHdzYWF3QkZkREIwK0orUlFSNnE3MlAwTzhtWXVKdncxcEJpd01iRUhoY1d5TmJxYmZGYWc4R2JvL29FRFAwNnkwQWE0QnJuOTBsTjY4T1Z1WThhWmk2TkI2elhkZHJQbkVoa0dGRlhLL2thSTA4L3RkbzJZdU1DVjIxUlBZK3pid2Q2VmxGSk1JRlEyTTQ0UWlTNFYyVndaMXA3MjRaNkI3WGpXZXVhT3ZQanpLMFNzMW5ML21TeDVoMkZKbXlhR2RlaEpWY2xsd29UWHlJZEZvY0JleENjTHpVWWNlN3duKzlPKzdjT3RQa1F0UElrOE0zQnNMNjdMOVl4dVQiLCJtYWMiOiJiNjk4NmI2NjQ3YzBhZjUzOWE4MTUyMGM1YzUwZjcwNjljY2QwMzUyZTY5ZmIzMGE4MWQ1ZTJiMGEyM2QwYjM0IiwidGFnIjoiIn0%3D; test_session=eyJpdiI6IkIzUjRkMHdTNkhJcGd4SUlKaXduRXc9PSIsInZhbHVlIjoiYzBqUGxtYTRobmQ4RXRiSEpvdjBDVXNNY2thRDJuam1SUTBNZ0xDMSt2N2VhdWxIS2VTb1o4VWszRkE5ekE5MGFCZHN0aEp4alIvdDNvcC9yd1liaUpWWkFLNE5HNDIvWm1yM1BkdTBWOStxanhEbHVkdWlUUUtkNlIwTXVFMVBMdDdrdVhGYnM2dy81L2ZaaFFGOU9MclBFaWlqMWxBV2Y0UkNaRDJCT1BnSkpCcndGR3Y1TE1zMWdQQzIxczF4cFp2STdBT1ZKQW1GS3ZnanVUc0pKYW83QTBoczUrTy9PRVpXU3ROd3Y0RGtLc3Fyam1jVzRhU1RvZHkwYWNGYXBjK3pTZFp5SzNOT0xCNGhMNk05T1dvM1lQRnRZbXhJYXc4cGhYT1dpQUE3R29HZ1BoR0xtV1hjWXZUY2dsRnkyRkF6NXAzZGhxOXRiR2R6VldTcDJCRHlCLy9GbjFaZVVTRVZVcncxTzRZVE9QZ1dSU0NOTFRMdmpUWXNTN2VZVlY3WGJSVDVGdm9KMXFlWnpJTHpUZWJRaWRIUXgrOG5FY0g0L0pqei9vSTk2QzBvc2Q1T0dLRVowQlZ2NXMxcm1CZnN3OE95YzdWVHo5M3BwVWNtdGhKb01TMS80eURWNWs3ZEpnMFpFY3VwSmNqcEcrSGRsc2xKSW8vdXR2K0ciLCJtYWMiOiJlNzRmODIxNGU0ZWNmNjM0YmMwMDNiYTMyODMzMzRmMDM0OTdlMzk3MGMyNjE2ZTczM2MzYjFmMjIxNzc2OWM2IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1360323523\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1851141327 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">VloaUQCBjpDKrel7nHeAzB1sTLdTMwteSos1MmAN</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1851141327\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-743043528 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 17 Jun 2025 13:53:20 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6ImxvV3c4bVZhb0F4M09BUFVqOWt5TkE9PSIsInZhbHVlIjoiRWZjdVN3bmtTQXNpdWVlbzRvY3YrOUZOMTU1VVdodFFlaytYRGtBVVhpTmxRL0d4b3VPSDFEaUpnYUxPT2JOSS9LbXl5ck93RklvektiQjl2bDRqTlUwMSs3RnE5TVVmSi9ucHFqMUM0eUNYaG9ZZ1cwZVpvQnk3dU5YU29sU3YyY2ZQM0FoVFRQSTl5RkVEOCsrNmxabk5PaW0rbmFMaDNGU3ZtTHpzR3RLQlVhbmRTVnVoR0lkNUN0RDhiUHd1eWxpc3ZBbG9nYVNPRHFBdFB5NEhCUHZNc2J2TGNkYUZ4Nnl2Z3phNmVSbmh1dVlPQm9RKzVWVDRWWlJhazJ0MG1xN05UMHBhQ2pxMjFRbTZtaHdHRmFMZWRwWklVV1ZwSHl6VXdJRVJ4cmZWYWdMZkJVQmNFcVBOUllESTI4aEhiT0d2SmNjY2dYdFp0VGcrZXlGNFlPVTVZbTFqcVVTM2EySVBLT05pNU9EaWxSQkdhYVlzYTJ6S0lPOUNwVksyYk1WalBMRUFaYTRPbHRZNllMZDZyVTMxZGtDOC9weFh6WlM1c2c0ZmNpSVorYjJmRktIdWhHT1AxbkNjdFNFZVVoNjM5UHg3TFZnMFpTVUJCYzQvYzdYWExqQVlJVnFEYVpqbXQyZjR5b045NEF6Nzd5SGxSSG9NUHNTcFNzNlUiLCJtYWMiOiI5ZjFhM2NmZTY3M2ZkNTI2NGNlNDYwZmZiZDg1NWQwMDY0MjdjN2RiZmYwNDM0NDYxYTlhODkzMDRiMmFhY2IwIiwidGFnIjoiIn0%3D; expires=Tue, 17 Jun 2025 15:53:20 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6InVxUk9WR3BjRCtyTHN3RnNwYmRkaFE9PSIsInZhbHVlIjoic3h0eHE4T2s0dlozVHFEb2tlc20xZ0FWTWZPUGt4bzh6Z3R0Q1FWbFArdjVWaVdScHhXK2pRNElIcTRINFdRRzV3emRhVGt5UXZhOGEzR0UvblpkOWVGZmtHV3BIakIwQkxNbDFvcDc1bkt6eW9kSnlVQWQ1Z1NIRmdhRWtQR1hZcmttdDNXVmhvaHFUNjUvMVArRkwxVVIyZmlOOGJ1c2NWNUUxcVZORHp6cWk2ZUNwRUNHekRaRzRIRm1YOVBuRFhPNXhGWmx3a0FqRVJVMkhONmdVaXRKYS90dTdZQ0YvaDNrQWZ2SjVrQnk4UjVoR1FzZkVXYWFBbFhmYXVpa1lHWVJkcWtZYUJjb0lrOWtodWFidWRxTEFkS001aGV4NFJaSVdtemxJWDBoSE05THJGTDJKeWk5YXNVbEl3dW4vQkhEd2dBREs0MFMyNFY1YU5BL3dOM2pETGFDMmlMMGFEcFBKc2EzSDFMdDZBTzlGdW9PajBFMVltTmxFSmV5Qi9zMkovYjN6RkdCMjlucjVoSjlYTTVxV3ZXd1doS0d0TjRPRmhuRGlNRE5mWFlCbytVcUp6KzkzblpWLzVBTW9jNGEzeXdhZG1iRGVJdVI5WERPTFZONFBmQlFSY2Q3QTBVbVM2dE5vcHJSMzN0L3hnK00xcUZUWU5tOXAzdmoiLCJtYWMiOiIyNTVjYmNhOThkMzQ5ZDQ2MDMxMTcxZGZlNGY1OWMxYmU0NGNhODJkYjkxNjdlZTJkOTRlODQyZGU5NDY2MTY2IiwidGFnIjoiIn0%3D; expires=Tue, 17 Jun 2025 15:53:20 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6ImxvV3c4bVZhb0F4M09BUFVqOWt5TkE9PSIsInZhbHVlIjoiRWZjdVN3bmtTQXNpdWVlbzRvY3YrOUZOMTU1VVdodFFlaytYRGtBVVhpTmxRL0d4b3VPSDFEaUpnYUxPT2JOSS9LbXl5ck93RklvektiQjl2bDRqTlUwMSs3RnE5TVVmSi9ucHFqMUM0eUNYaG9ZZ1cwZVpvQnk3dU5YU29sU3YyY2ZQM0FoVFRQSTl5RkVEOCsrNmxabk5PaW0rbmFMaDNGU3ZtTHpzR3RLQlVhbmRTVnVoR0lkNUN0RDhiUHd1eWxpc3ZBbG9nYVNPRHFBdFB5NEhCUHZNc2J2TGNkYUZ4Nnl2Z3phNmVSbmh1dVlPQm9RKzVWVDRWWlJhazJ0MG1xN05UMHBhQ2pxMjFRbTZtaHdHRmFMZWRwWklVV1ZwSHl6VXdJRVJ4cmZWYWdMZkJVQmNFcVBOUllESTI4aEhiT0d2SmNjY2dYdFp0VGcrZXlGNFlPVTVZbTFqcVVTM2EySVBLT05pNU9EaWxSQkdhYVlzYTJ6S0lPOUNwVksyYk1WalBMRUFaYTRPbHRZNllMZDZyVTMxZGtDOC9weFh6WlM1c2c0ZmNpSVorYjJmRktIdWhHT1AxbkNjdFNFZVVoNjM5UHg3TFZnMFpTVUJCYzQvYzdYWExqQVlJVnFEYVpqbXQyZjR5b045NEF6Nzd5SGxSSG9NUHNTcFNzNlUiLCJtYWMiOiI5ZjFhM2NmZTY3M2ZkNTI2NGNlNDYwZmZiZDg1NWQwMDY0MjdjN2RiZmYwNDM0NDYxYTlhODkzMDRiMmFhY2IwIiwidGFnIjoiIn0%3D; expires=Tue, 17-Jun-2025 15:53:20 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6InVxUk9WR3BjRCtyTHN3RnNwYmRkaFE9PSIsInZhbHVlIjoic3h0eHE4T2s0dlozVHFEb2tlc20xZ0FWTWZPUGt4bzh6Z3R0Q1FWbFArdjVWaVdScHhXK2pRNElIcTRINFdRRzV3emRhVGt5UXZhOGEzR0UvblpkOWVGZmtHV3BIakIwQkxNbDFvcDc1bkt6eW9kSnlVQWQ1Z1NIRmdhRWtQR1hZcmttdDNXVmhvaHFUNjUvMVArRkwxVVIyZmlOOGJ1c2NWNUUxcVZORHp6cWk2ZUNwRUNHekRaRzRIRm1YOVBuRFhPNXhGWmx3a0FqRVJVMkhONmdVaXRKYS90dTdZQ0YvaDNrQWZ2SjVrQnk4UjVoR1FzZkVXYWFBbFhmYXVpa1lHWVJkcWtZYUJjb0lrOWtodWFidWRxTEFkS001aGV4NFJaSVdtemxJWDBoSE05THJGTDJKeWk5YXNVbEl3dW4vQkhEd2dBREs0MFMyNFY1YU5BL3dOM2pETGFDMmlMMGFEcFBKc2EzSDFMdDZBTzlGdW9PajBFMVltTmxFSmV5Qi9zMkovYjN6RkdCMjlucjVoSjlYTTVxV3ZXd1doS0d0TjRPRmhuRGlNRE5mWFlCbytVcUp6KzkzblpWLzVBTW9jNGEzeXdhZG1iRGVJdVI5WERPTFZONFBmQlFSY2Q3QTBVbVM2dE5vcHJSMzN0L3hnK00xcUZUWU5tOXAzdmoiLCJtYWMiOiIyNTVjYmNhOThkMzQ5ZDQ2MDMxMTcxZGZlNGY1OWMxYmU0NGNhODJkYjkxNjdlZTJkOTRlODQyZGU5NDY2MTY2IiwidGFnIjoiIn0%3D; expires=Tue, 17-Jun-2025 15:53:20 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-743043528\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>16</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"30 characters\">http://localhost/hrm-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pos</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-key>2044</span> => <span class=sf-dump-note>array:9</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"6 characters\">&#1578;&#1580;&#1585;&#1576;&#1610;&#1577;</span>\"\n      \"<span class=sf-dump-key>quantity</span>\" => <span class=sf-dump-num>1</span>\n      \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"5 characters\">10.00</span>\"\n      \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"4 characters\">2044</span>\"\n      \"<span class=sf-dump-key>tax</span>\" => <span class=sf-dump-num>0</span>\n      \"<span class=sf-dump-key>subtotal</span>\" => <span class=sf-dump-num>10.0</span>\n      \"<span class=sf-dump-key>originalquantity</span>\" => <span class=sf-dump-num>1</span>\n      \"<span class=sf-dump-key>product_tax</span>\" => \"<span class=sf-dump-str>-</span>\"\n      \"<span class=sf-dump-key>product_tax_id</span>\" => <span class=sf-dump-num>0</span>\n    </samp>]\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n"}}